import {
  Alert,
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { Post } from "../../../Services/Axios";
import { useForm } from "react-hook-form";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import Swal from "sweetalert2";
import { useState } from "react";

const formField = ["Email", "UserName", "PhoneNumber", "IsActive" ,"Id", "Role"];

export const EditUserList = ({
  openDialog,
  setOpenDialog,
  setLoading,
  Data,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  const [save, setSave] = useState<boolean>(false);
  console.log(Data?.role)
  
  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setOpenDialog({ edit: false });
    setSave(false);
  };

  const onSubmitHandler = async (data: any) => {
    debugger;
    setSave(true);
    data.IsActive = data.IsActive === "Yes" ? true : false;
    const { error }: any = await Post(
      "app/UserManagement/UpdateUserList",
      data
    );
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "User Updated Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setLoading((prev: boolean) => !prev);
    });
    handleClose();
  };

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  return (
    <Dialog open={openDialog?.edit}>
      <form onSubmit={handleSubmit(onSubmitHandler)}>
        <div
          style={{
            backgroundColor: "#f0f0f0",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <DialogTitle className="mx-2 d-flex justify-content-center">
            <h4>Edit User List</h4>
          </DialogTitle>
          <CancelOutlinedIcon
            onClick={handleClose}
            sx={{
              color: "red",
              fontSize: "30px",
              marginRight: "10px",
              cursor: "pointer",
            }}
          />
        </div>
        <DialogContent className="row popup">
          {errorMsg.show && (
            <Alert severity="error" className="mb-3">
              {errorMsg.message}. <strong>check it out!</strong>
            </Alert>
          )}
          <div className="row mx-auto">
            <label htmlFor="email">Email</label>
            <TextField
              required
              defaultValue={Data?.email}
              type="text"
              id="email"
              {...register("Email")}
              className="col m-2"
            />
          </div>
          <div className="row mx-auto">
            <label htmlFor="username">Full Name</label>
            <TextField
              required
              defaultValue={Data?.userName}
              type="text"
              id="username"
              {...register("UserName")}
              className="col m-2"
            />
          </div>
          <div className="row mx-auto">
            <div className="col">
              <label htmlFor="Phonenumber">Phone</label>
              <TextField
                required
                defaultValue={Data?.phoneNumber}
                type="text"
                id="phonenumber"
                {...register("PhoneNumber")}
              />
            </div>
            <div className=" row col m-4 ">
              <FormControl>
                <InputLabel id="Is-Active">Is Active</InputLabel>
                <Select
                  labelId="category"
                  required
                  label="Category"
                  defaultValue={Data?.isActive ? "Yes" : "No"}
                  {...register("IsActive")}
                >
                  <MenuItem value={"Yes"}>Yes</MenuItem>
                  <MenuItem value={"No"}>No</MenuItem>
                </Select>
              </FormControl>
            </div>
          </div>
          <input {...register("Id")} value={Data?.id} hidden />
          <input {...register("Role")} value={Data?.role} hidden />
        </DialogContent>
        <DialogActions className="px-4">
          <Button onClick={handleClose} variant="contained" color="error">
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="success"
            className="mx-3"
            disabled={save}
          >
            {save ? "Saving..." : "Save"}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default EditUserList;
