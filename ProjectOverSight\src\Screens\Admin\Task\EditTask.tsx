import {
  <PERSON><PERSON>,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Grid,
} from "@mui/material";
import { useEffect, useState } from "react";
import "../../../StyleSheets/EditTask.css";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import { Get, Post } from "../../../Services/Axios";
import { ConvertToISO } from "../../../Utilities/Utils";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import TextareaAutosize from "@mui/material/TextareaAutosize";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { useContextProvider } from "../../../CommonComponents/Context";
import {
  TASKCLASSIFICATION,
  TASKTYPE,
} from "../../../Constants/Common/CommonMaster";
import { CommonMaster } from "../../../Models/Common/CommonMaster";
import { Regex } from "../../../Constants/Regex/Regex";

export const EditTask = ({
  openDialog,
  setOpenDialog,
  Data,
  setLoading,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [categoryLists, setCategoryList] = useState([]);
  const [taskstatus, settaskstatus] = useState("");
  const [category, setcategory] = useState<any>([]);
  const [selSubCat, setselSubCatt] = useState<any>([]);
  const [save, setSave] = useState<boolean>(false);
  const [selectedSubCategory, setSelectedSubCategory] = useState("");
  const { commonMaster } = useContextProvider();
  const [isEditable, setIsEditable] = useState(false);
  const [textEditable, settextEditable] = useState(false);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  const [actualStartDate, setActualStartDate] = useState("");

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setOpenDialog({ edit: false });
    setSave(false);
  };

  const handleCategoryChange = (event: any) => {
    let temp: any = [];
    categoryLists.map((e: any) => {
      if (event.target.value === e.categories) {
        temp.push(e);
      }
    });
    setselSubCatt(temp);
    setSelectedSubCategory("");
  };

  console.log(Data);

  const formField = [
    "Name",
    "Description",
    "EstimateStartDate",
    "StartDate",
    "EndDate",
    "EmployeeName",
    "EstimateEndDate",
    "Status",
    "Percentage",
    "TeamName",
    "CreatedBy",
    "UpdatedBy",
    "ProjectId",
    "Id",
    "category",
    "SubCategory",
    "weekEndingDate",
    "CategoryId",
    "taskType",
    "classification",
    "comment",
  ];

  var categoryList: any = new Set();

  useEffect(() => {
    setSelectedSubCategory(Data?.subCategory);
    const response = Get("app/Common/GetCategoriesList");
    response.then((res: any) => {
      res.data?.forEach((e: any) => {
        categoryList.add(e.categories);
      });
      setCategoryList(res.data || []);
      let temp = res.data?.filter((x: any) => x.categories === Data?.category);
      setcategory([...categoryList]);
      setselSubCatt(temp || []);
    });
    const status = Data?.status;
    settaskstatus(status);
    if (status === "Assigned" || status === "In Progress") {
      setIsEditable(true);
    } else if (status === "Completed" || status === "Ready-For-UAT") {
      setIsEditable(true);
      settextEditable(true);
    } else {
      setIsEditable(false);
      settextEditable(false);
    }
  }, [Data?.description, Data?.subCategory, Data?.status]);

  const onSubmitHandler = async (data: any) => {
    setSave(true);
    let CategoryId: any = categoryLists.find(
      (x: any) => x.subCategory === data.CategoryId
    );
    data.CategoryId = CategoryId?.id;
    if (selectedSubCategory === "") {
      setErrorMsg({
        message: "Please select sub category!",
        show: true,
      });
      return;
    }
    if (data.EstimateStartDate > data.EstimateEndDate) {
      setErrorMsg({
        message: "Estimate Start Date must be before Estimate End Date",
        show: true,
      });
      return;
    }

    const { error }: any = await Post("app/Task/UpdateTask", data);
    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Error updating the record!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Task Updated Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setLoading((prev: boolean) => !prev);
    });
    handleClose();
  };
  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  return (
    <div>
      <Dialog open={openDialog?.edit}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            className="fs-4"
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>Edit Task</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}.
              </Alert>
            )}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    required
                    defaultValue={Data?.name}
                    {...register("Name")}
                    label="Task Name"
                    type="text"
                    variant="outlined"
                    onChange={(e: any) => {
                      e.target.value = e.target.value.replace(
                        Regex.EMAIL_1,
                        ""
                      );
                    }}
                    inputProps={{ maxLength: 250 }}
                    disabled={textEditable}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    id="outlined-read-only-input"
                    label="Status"
                    {...register("Status")}
                    defaultValue={Data?.status}
                    className={`${Data?.status ? "read-only-input" : ""}`}
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={12}>
                <InputLabel id="description">Description</InputLabel>
                <TextareaAutosize
                  required
                  id="description"
                  {...register("Description")}
                  onChange={(e) => {
                    e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                  }}
                  className="form-control"
                  aria-label="empty textarea"
                  defaultValue={Data?.description}
                  disabled={textEditable}
                  style={{ height: 90 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    value={Data?.usName || ""} // Use value instead of defaultValue
                    margin="dense"
                    {...register("UsName")}
                    label="User Story Name"
                    type="text"
                    fullWidth
                    variant="outlined"
                    // className={`${isEditable ? "text-muted" : ""}`}
                    className={`${Data?.usName ? "read-only-input" : ""}`}
                    inputProps={{ readOnly: isEditable }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    id="userInterfaceNameId"
                    label="User Interface Name"
                    value={Data?.uiName}
                    margin="dense"
                    {...register("uiName")}
                    type="text"
                    fullWidth
                    variant="outlined"
                    // className={`${isEditable ? "text-muted" : ""}`}
                    className={`${Data?.uiName ? "read-only-input" : ""}`}
                    inputProps={{ readOnly: isEditable }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    required
                    defaultValue={Data?.teamName}
                    className={`${Data?.teamName ? "read-only-input" : ""}`}
                    label="Team Name"
                    type="text"
                    variant="outlined"
                    {...register("TeamName")}
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    className={`${Data?.employeeName ? "read-only-input" : ""}`}
                    defaultValue={
                      Data?.employeeName?.replace(Regex.CHAR_SPACE, "") || "-"
                    }
                    label="Employee Name"
                    type="text"
                    fullWidth
                    {...register("EmployeeName")}
                    variant="outlined"
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel required id="category">
                    Category
                  </InputLabel>
                  <Select
                    labelId="category"
                    required
                    id="category"
                    label="Category"
                    defaultValue={Data?.category}
                    {...register("SubCategory", {
                      onChange: (e: any) => {
                        handleCategoryChange(e);
                      },
                    })}
                    className={`${isEditable ? "read-only-input" : "text-muted"}`}
                    readOnly={isEditable}
                  >
                    {[...category].map((e: any, index: number) => {
                      return (
                        <MenuItem value={e} key={index}>
                          {e}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel required id="subCategory">
                    Sub Category
                  </InputLabel>
                  <Select
                    labelId="Sub category"
                    required
                    id="Sub-category"
                    label="Sub Category"
                    className={`${isEditable ? "read-only-input" : "text-muted"}`}
                    defaultValue={Data?.subCategory}
                    {...register("CategoryId", {
                      onChange: (e: any) => {
                        setSelectedSubCategory(e.target.value);
                        setErrorMsg({
                          message: "",
                          show: false,
                        });
                      },
                    })}
                    readOnly={isEditable}
                  >
                    {[...selSubCat].map((e: any) => {
                      return (
                        <MenuItem value={e.subCategory} key={e.id}>
                          {e.subCategory}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel required id="taskType">
                    Task Type
                  </InputLabel>
                  <Select
                    labelId="taskType"
                    required
                    id="taskType"
                    label="Task Type"
                    defaultValue={Data?.taskType}
                    {...register("taskType")}
                    className={`${isEditable ? "read-only-input" : "text-muted"}`}
                    readOnly={isEditable}
                  >
                    {commonMaster
                      .filter((x: CommonMaster) => x.codeType == TASKTYPE)
                      .sort((a, b) => a.codeValue.localeCompare(b.codeValue))
                      .map((e: any) => {
                        return (
                          <MenuItem value={e.codeValue} key={e.codeValue}>
                            {e?.codeValue}
                          </MenuItem>
                        );
                      })}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel required id="classification">
                    Classification
                  </InputLabel>
                  <Select
                    labelId="classification"
                    required
                    id="classification"
                    label="Classification"
                    defaultValue={Data?.classification}
                    {...register("classification")}
                    className={`${isEditable ? "read-only-input" : "text-muted"}`}
                    readOnly={isEditable}
                  >
                    {commonMaster
                      .filter(
                        (x: CommonMaster) => x.codeType === TASKCLASSIFICATION
                      )
                      .sort((a, b) => a.codeValue.localeCompare(b.codeValue))
                      .map((e: any) => {
                        return (
                          <MenuItem value={e.codeValue}>{e.codeValue}</MenuItem>
                        );
                      })}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    required
                    id="start-date"
                    label="Est Start date"
                    inputProps={{
                      min: new Date(),
                    }}
                    defaultValue={ConvertToISO(Data?.estimateStartDate)}
                    className={`${
                      (taskstatus != "UnAssigned" &&
                        taskstatus != "Assigned") ||
                      (Data?.estimateStartDate && isEditable)
                        ? ""
                        : "read-only-input"
                    } ${isEditable && "text-muted"}`}
                    margin="dense"
                    {...register("EstimateStartDate", {
                      onChange: (e: any) => {
                        setActualStartDate(e.target.value);
                      },
                    })}
                    type="date"
                    fullWidth
                    variant="outlined"
                    InputProps={
                      (taskstatus == "Assigned" ||
                        taskstatus == "UnAssigned") &&
                      !isEditable
                        ? { readOnly: true }
                        : {
                            readOnly: false,
                          }
                    }
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                {/* <InputLabel id="end-date">Est End date</InputLabel> */}
                <FormControl fullWidth>
                  <TextField
                    required
                    id="end-date"
                    label="Est End date"
                    defaultValue={ConvertToISO(Data?.estimateEndDate)}
                    className={`${
                      (taskstatus != "UnAssigned" &&
                        taskstatus != "Assigned") ||
                      (Data?.estimateEndDate && isEditable)
                        ? ""
                        : "read-only-input"
                    } ${isEditable && "text-muted"}`}
                    margin="dense"
                    {...register("EstimateEndDate", {
                      onChange: (e: any) => {
                        debugger;
                        if (
                          new Date(actualStartDate) > new Date(e.target.value)
                        ) {
                          e.target.value = "";
                          Swal.fire({
                            title: "",
                            text: "End date should be greater than start date.",
                            icon: "warning",
                          });
                          return;
                        }
                      },
                    })}
                    type="date"
                    fullWidth
                    variant="outlined"
                    InputProps={
                      (taskstatus == "Assigned" ||
                        taskstatus == "UnAssigned") &&
                      !isEditable
                        ? { readOnly: true }
                        : {
                            readOnly: false,
                          }
                    }
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Actual Start date</InputLabel>
                <FormControl fullWidth>
                  <TextField
                    required
                    id="outlined-read-only-input"
                    defaultValue={
                      Data?.actualStartDate
                        ? ConvertToISO(Data?.actualStartDate)
                        : null
                    }
                    className={`${
                      Data?.actualStartDate ? "read-only-input" : ""
                    } ${isEditable && "text-muted"}`}
                    margin="dense"
                    {...register("StartDate")}
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">Actual End date</InputLabel>
                <FormControl fullWidth>
                  <TextField
                    required
                    id="outlined-read-only-input"
                    defaultValue={ConvertToISO(
                      Data?.actualEndDate ? Data?.actualEndDate : ""
                    )}
                    className={`${
                      Data?.actualEndDate ? "read-only-input" : ""
                    }`}
                    margin="dense"
                    {...register("EndDate", {
                      onChange: (e: any) => {
                        debugger;
                        if (
                          new Date(actualStartDate) > new Date(e.target.value)
                        ) {
                          e.target.value = "";
                          Swal.fire({
                            title: "",
                            text: "End date should be greater than start date.",
                            icon: "warning",
                          });
                          return;
                        }
                      },
                    })}
                    type="date"
                    fullWidth
                    variant="outlined"
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                {/* <Grid item xs={5.5} sx={{ ml: 3 }} className="mx-2"> */}
                {/* <InputLabel id="end-date">Week Ending date</InputLabel> */}
                <FormControl fullWidth>
                  <TextField
                    required
                    id="end-date"
                    defaultValue={ConvertToISO(Data?.weekEndingDate)}
                    margin="dense"
                    {...register("weekEndingDate")}
                    label="Week Ending date"
                    type="date"
                    fullWidth
                    variant="outlined"
                    className={`${
                      Data?.weekEndingDate && isEditable
                        ? "read-only-input"
                        : ""
                    } ${isEditable && "text-muted"}`}
                    InputProps={
                      isEditable
                        ? { readOnly: true }
                        : {
                            readOnly: false,
                          }
                    }
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    value={Data?.comment || ""} // Use value instead of defaultValue
                    margin="dense"
                    {...register("comment")}
                    label="Comment"
                    type="text"
                    fullWidth
                    variant="outlined"
                    className={`${isEditable ? "read-only-input" : "text-muted"} `}
                    inputProps={{ readOnly: isEditable }}
                    disabled
                  />
                </FormControl>
              </Grid>
            </Grid>
            <input {...register("ProjectId")} value={Data?.projectId} hidden />
            <input {...register("CreatedBy")} value="user" hidden />
            <input {...register("UpdatedBy")} value="user" hidden />
            <input {...register("Id")} value={Data?.id} hidden />
          </DialogContent>
          <DialogActions className="px-4">
            <Button onClick={handleClose} variant="contained" color="error">
              Cancel
            </Button>
            <Button
              variant="contained"
              color="success"
              disabled={save}
              type="submit"
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
