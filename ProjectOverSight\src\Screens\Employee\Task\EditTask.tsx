import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextareaAutosize,
  Grid,
} from "@mui/material";
import { useEffect, useState } from "react";
import "../../../StyleSheets/EditTask.css";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import { Get, Post } from "../../../Services/Axios";
import { ConvertToISO } from "../../../Utilities/Utils";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { Category } from "../../../Models/Common/CommonMaster";
import { Regex } from "../../../Constants/Regex/Regex";
import { TransitionProps } from "@mui/material/transitions";
import Slide from "@mui/material/Slide";
import * as React from "react";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export const EditTask = ({
  openDialog,
  setOpenDialog,
  Data,
  setReload,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [categories, setCategories] = useState<string[]>([]);
  const [selSubCat, setSelSubCat] = useState<string[]>([]);
  const [selectedSubCategory, setSelectedSubCategory] = useState("");
  const [categoryLists, setCategoryList] = useState<Category[]>([]);

  const [taskTypeOptions, setTaskTypeOptions] = useState<string[]>([]);
  const [classificationOptions, setClassificationOptions] = useState<string[]>(
    []
  );

  const [selectedTaskType, setSelectedTaskType] = useState("");
  const [selectedClassification, setSelectedClassification] = useState("");
  // const [taskstatus, settaskstatus] = useState("");
  const [save, setSave] = useState<boolean>(false);

  // const [isEditable, setIsEditable] = useState(false);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ edit: false });
  };

  const handleCategoryChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    const top = e.target.value as string;
    // build distinct subcats
    const subs = Array.from(
      new Set(
        categoryLists
          .filter((c) => c.categories === top)
          .map((c) => c.subCategory)
      )
    ).sort();

    setSelSubCat(subs);
    setSelectedSubCategory("");
  };

  const formField = [
    "Name",
    "Description",
    "EstimateStartDate",
    "StartDate",
    "EndDate",
    "EmployeeName",
    "EstimateEndDate",
    "Status",
    "Percentage",
    "TeamName",
    "CreatedBy",
    "UpdatedBy",
    "ProjectId",
    "Id",
    "category",
    "SubCategory",
    "weekEndingDate",
    "CategoryId",
    "taskType",
    "classification",
  ];

  useEffect(() => {
    Get("app/Common/GetCategoriesList").then((res: any) => {
      const list = res.data as Category[];
      setCategoryList(list);

      // distinct top-level names
      setCategories(Array.from(new Set(list.map((c) => c.categories))).sort());

      // if you're editing, pre-seed subcats
      if (Data?.category) {
        setSelSubCat(
          Array.from(
            new Set(
              list
                .filter((c) => c.categories === Data.category)
                .map((c) => c.subCategory)
            )
          ).sort()
        );
        debugger;
        setSelectedSubCategory(Data.subCategory);
      }
    });
  }, [Data]);

  useEffect(() => {
    // clear old
    setSelectedTaskType("");
    setTaskTypeOptions([]);
    setSelectedClassification("");
    setClassificationOptions([]);

    if (!selectedSubCategory) return;

    const types = Array.from(
      new Set(
        categoryLists
          .filter((c) => c.subCategory === selectedSubCategory)
          .map((c) => c.taskType)
      )
    ).sort();

    setTaskTypeOptions(types);
  }, [selectedSubCategory, categoryLists]);

  useEffect(() => {
    // clear old
    setSelectedClassification("");
    setClassificationOptions([]);
    debugger;
    if (selectedTaskType == "") {
      setSelectedTaskType(Data.taskType ?? "");
      setSelectedClassification(Data.classification ?? "");
    }

    if (!selectedSubCategory || !selectedTaskType) return;

    const classes = Array.from(
      new Set(
        categoryLists
          .filter(
            (c) =>
              c.subCategory === selectedSubCategory &&
              c.taskType === selectedTaskType
          )
          .map((c) => c.taskClassification)
      )
    ).sort();
    debugger;
    if (!selectedTaskType && selectedTaskType == "") {
      setSelectedTaskType(Data.taskType ?? "");
      setSelectedClassification(Data.classification ?? "");
    }

    setClassificationOptions(classes);
    setSelectedClassification(Data.classification ?? "");
    if (selectedClassification == "") {
    }
  }, [selectedTaskType, selectedSubCategory, categoryLists]);

  const onSubmitHandler = async (data: any) => {
    let CategoryId: any = categoryLists.find(
      (x: any) => x.subCategory === data.CategoryId
    );
    data.CategoryId = CategoryId?.id;
    if (selectedSubCategory === "") {
      setErrorMsg({
        message: "Please select sub category!",
        show: true,
      });
      return;
    }
    if (data.EstimateStartDate > data.EstimateEndDate) {
      setErrorMsg({
        message: "Estimate Start Date must be before Estimate End Date",
        show: true,
      });
      return;
    }
    setSave(true);
    const { error }: any = await Post("app/Task/UpdateTask", data);
    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Task Updated Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  return (
    <div>
      <Dialog open={openDialog?.edit} TransitionComponent={Transition}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>Edit</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}.
              </Alert>
            )}
            <div className="row">
              <TextField
                required
                defaultValue={Data?.name}
                {...register("Name")}
                label="Task Name"
                type="text"
                variant="outlined"
                inputProps={{ maxLength: 250 }}
                className={`col m-2`}
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.EMAIL_1, "");
                }}
              />
              <TextField
                id="outlined-read-only-input"
                label="Status"
                className="col m-2"
                {...register("Status")}
                defaultValue={Data?.status}
                // InputProps={{
                //   readOnly: true,
                // }}
              />
            </div>
            <div className="row">
              <InputLabel id="description" sx={{ ml: 1 }}>
                Description
              </InputLabel>
              <TextareaAutosize
                defaultValue={Data?.description}
                {...register("Description")}
                className="col m-2 mb-3 form-control"
                style={{ height: 90 }}
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                }}
              />
            </div>
            <div className="row">
              <TextField
                required
                value={Data?.usName || ""}
                className={`col m-2`}
                margin="dense"
                {...register("UsName")}
                label="User Story Name"
                type="text"
                fullWidth
                variant="outlined"
                // className={`${isEditable ? "text-muted" : ""}`}
                // inputProps={{ readOnly: isEditable }}
              />
              <TextField
                id="userInterfaceNameId"
                label="User Interface Name"
                className={`col m-2`}
                value={Data?.uiName}
                margin="dense"
                {...register("uiName")}
                type="text"
                fullWidth
                variant="outlined"
                // className={`${isEditable ? "text-muted" : ""}`}
                // inputProps={{ readOnly: isEditable }}
              />
            </div>
            <div className="row">
              <TextField
                required
                defaultValue={Data?.teamName}
                className={`col m-2`}
                label="Team Name"
                type="text"
                variant="outlined"
                {...register("TeamName")}
                // InputProps={{
                //   readOnly: true,
                // }}
              />
              <TextField
                className={`col m-2`}
                defaultValue={Data?.employeeName || "-"}
                label="Employee Name"
                type="text"
                {...register("EmployeeName")}
                fullWidth
                variant="outlined"
                // InputProps={{
                //   readOnly: true,
                // }}
              />
            </div>
            <div className="row">
              <FormControl fullWidth className="col m-2">
                <InputLabel required id="category">
                  Category
                </InputLabel>
                <Select
                  labelId="category"
                  required
                  id="category"
                  label="Category"
                  defaultValue={Data?.category}
                  {...register("SubCategory", {
                    onChange: (e: any) => {
                      handleCategoryChange(e);
                    },
                  })}
                  // className={`${isEditable ? "text-muted" : ""}`}
                  // readOnly={isEditable}
                >
                  {categories.map((e) => (
                    <MenuItem key={e} value={e}>
                      {e}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl fullWidth className="col m-2">
                <InputLabel required id="subCategory">
                  Sub Category
                </InputLabel>
                <Select
                  labelId="Sub category"
                  required
                  id="Sub-category"
                  label="Sub Category"
                  defaultValue={Data?.subCategory}
                  {...register("CategoryId", {
                    onChange: (e: any) => {
                      setSelectedSubCategory(e.target.value);
                      setErrorMsg({
                        message: "",
                        show: false,
                      });
                    },
                  })}
                  // className={`${isEditable ? "text-muted" : ""}`}
                  // readOnly={isEditable}
                >
                  {selSubCat.map((sub) => (
                    <MenuItem key={sub} value={sub}>
                      {sub}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>

            <div className="row">
              <FormControl fullWidth className="col m-2">
                <InputLabel required id="taskType">
                  Task Type
                </InputLabel>
                <Select
                  labelId="taskType"
                  required
                  label="Task Type"
                  value={selectedTaskType}
                  {...register("taskType", {
                    onChange: (e) => setSelectedTaskType(e.target.value),
                  })}
                >
                  {taskTypeOptions.map((t) => (
                    <MenuItem key={t} value={t}>
                      {t}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl fullWidth className="col m-2">
                <InputLabel required id="classification">
                  Classification
                </InputLabel>
                <Select
                  labelId="classification"
                  required
                  label="Classification"
                  value={selectedClassification}
                  disabled={!selectedTaskType}
                  {...register("classification", {
                    onChange: (e) => setSelectedClassification(e.target.value),
                  })}
                >
                  {classificationOptions.map((c) => (
                    <MenuItem key={c} value={c}>
                      {c}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>

            <div className="row">
              <div className="col">
                <InputLabel id="start-date">Est Start date</InputLabel>
                <TextField
                  required
                  id="start-date"
                  defaultValue={ConvertToISO(Data?.estimateStartDate)}
                  margin="dense"
                  {...register("EstimateStartDate")}
                  type="date"
                  fullWidth
                  variant="outlined"
                  // className={`${
                  //   ((taskstatus != "UnAssigned"&& taskstatus != "Assigned" )|| (Data?.estimateStartDate && isEditable)) ? "" : "read-only-input"
                  // } ${isEditable && "text-muted"}`}
                  // InputProps={
                  //   ((taskstatus == "Assigned" || taskstatus == "UnAssigned") && !isEditable)
                  //     ? { readOnly: true}
                  //     : {
                  //         readOnly: false,
                  //       }
                  // }
                />
              </div>
              <div className="col">
                <InputLabel id="end-date">Est End date</InputLabel>
                <TextField
                  required
                  id="end-date"
                  defaultValue={ConvertToISO(Data?.estimateEndDate)}
                  margin="dense"
                  {...register("EstimateEndDate")}
                  type="date"
                  fullWidth
                  variant="outlined"
                  // className={`${
                  //   ((taskstatus != "UnAssigned"&& taskstatus != "Assigned" )|| (Data?.estimateEndDate && isEditable)) ? "" : "read-only-input"
                  // } ${isEditable && "text-muted"}`}
                  // InputProps={
                  //   ((taskstatus == "Assigned" || taskstatus == "UnAssigned") && !isEditable)
                  //     ? { readOnly: true}
                  //     : {
                  //         readOnly: false,
                  //       }
                  // }
                />
              </div>
            </div>
            <Grid container>
              <Grid item xs={5.5} sx={{ ml: 0 }}>
                <div>
                  <InputLabel id="end-date">Week Ending date</InputLabel>
                  <TextField
                    required
                    id="end-date"
                    defaultValue={ConvertToISO(Data?.weekEndingDate)}
                    margin="dense"
                    {...register("weekEndingDate")}
                    fullWidth
                    variant="outlined"
                    // className={`${
                    //   Data?.weekEndingDate && isEditable ? "read-only-input" : ""
                    // } ${isEditable && "text-muted"}`}
                    // InputProps={
                    //   isEditable
                    //     ? { readOnly: true}
                    //     : {
                    //         readOnly: false,
                    //       }
                    // }
                  />
                </div>
              </Grid>
            </Grid>
            <input {...register("ProjectId")} value={Data?.projectId} hidden />
            <input {...register("CreatedBy")} value="user" hidden />
            <input {...register("UpdatedBy")} value="user" hidden />
            <input {...register("Id")} value={Data?.id} hidden />
          </DialogContent>
          <DialogActions className="px-4 mx-3 mb-2">
            <Button onClick={handleClose} variant="contained" color="error">
              Cancel
            </Button>
            <Button
              variant="contained"
              color="success"
              type="submit"
              disabled={save}
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
