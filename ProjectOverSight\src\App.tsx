import { TopBar } from "./CommonComponents/TopBar";
import { Navigate, Route, Routes } from "react-router-dom";
import { LoginScreen } from "./Screens/Login/LoginScreen";
import { AdminRouter } from "./Router/AdminRouter";
import { EmployeeRouter } from "./Router/EmployeeRouter";
import { Typography } from "@mui/material";
import { UAT, VERSION } from "./Constants/Common/Versions";
import { useContextProvider } from "./CommonComponents/Context";

function App() {
  const { user } = useContextProvider();
  return (
    <>
      {user && <TopBar />}
      <Routes>
        <Route path="/Login" element={<LoginScreen />}></Route>
        <Route path="/" element={<Navigate replace to="Login" />}></Route>
      </Routes>
      <AdminRouter />
      <EmployeeRouter />
      <div
        style={{
          textAlign: "center",
          padding: "10px",
        }}
        className="w-100 footer"
      >
        <Typography className="fw-bolder text-center ">
          Version {VERSION} | {UAT}
        </Typography>
      </div>
    </>
  );
}
export default App;
