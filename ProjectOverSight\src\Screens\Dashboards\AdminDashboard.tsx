import { FormControlLabel, Grid, Typography } from "@mui/material";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import { useState, useEffect, useRef } from "react";
import { Get } from "../../Services/Axios";
import { ToolTip } from "../../CommonComponents/ToolTip";
import "../../StyleSheets/AdminDashboard.css";
import {
  ConvertDate,
  ConvertTime,
  ConvertToISO,
  WeekEndingDate,
  convertTo12HourFormat,
} from "../../Utilities/Utils";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { Link } from "react-router-dom";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import Swal from "sweetalert2";
import TurnedInIcon from "@mui/icons-material/TurnedIn";
import { Employee } from "../../Models/Employee/Employee";
import { Regex } from "../../Constants/Regex/Regex";
import { IOSSwitch } from "../../CommonComponents/IOSSwitch";
import { useContextProvider } from "../../CommonComponents/Context";
const skleton = Array.from(Array(12), () => 0);

interface TeamProject {
  teamId: string;
  isActive: boolean;
  priorities?: "High" | "Medium" | "Low" | null;
}

export const AdminDashboard = () => {
  const { role } = useContextProvider();
  const [Data, setData] = useState<any>({});
  const [teamProject, setTeamProject] = useState<any>([]);
  const [team, setTeam] = useState<any>({});
  const [teamEmployee, setTeamEmployee] = useState<any>([]);
  const [employeeTime, setEmployeeTime] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [unAssignedHours, setUnAssignedHours] = useState<any>();
  const [teamWeeklyPlan, setTeamWeeklyPlan] = useState<any>([]);
  const [projectStatus, setProjectStatus] = useState(true);
  const [selectTeam, setSelectTeam] = useState(false);
  const [teamStatus, setTeamStatus] = useState(true);
  const [teamList, setTeamList] = useState<any>([]);
  const [reload, setReload] = useState(true);
  const weekEndRef = useRef<any>();
  const [weekEndingDate, setWeekEndingDate] = useState(
    ConvertToISO(WeekEndingDate())
  );
  let counter = 0;

  async function fetchData() {
    setLoading(true);
    const response: any = await Get(
      `app/Common/GetDashboardData?weekEndingDate=${weekEndingDate}&teamId=${team?.Id ?? -1
      }`
    );

    const teamList: any = await Get("app/Team/GetTeamList");

    setData(response.data);
    const employeeDataMap = new Map<number, number>();
    response.data?.teamEmployees.forEach((employee: any) => {
      const { employeeId, unassignedHours } = employee;
      const sanitizedUnassignedHours =
        unassignedHours < 0 ? 0 : unassignedHours;
      if (!employeeDataMap.has(employeeId)) {
        employeeDataMap.set(employeeId, sanitizedUnassignedHours);
      }
    });
    let sumOfUnassignedHours = 0;
    for (const unassignedHours of employeeDataMap.values()) {
      sumOfUnassignedHours += unassignedHours;
    }

    setUnAssignedHours(sumOfUnassignedHours);

    if (role === "Customer") {
      const getTeams = response?.data?.teamList?.filter(
        (item: any) => teamList?.data.some((x: any) => x.id === item.teamId && item.isActive === true)
      );
      if (selectTeam == false) {
        setTeam({
          Id: getTeams && getTeams[0]?.teamId,
          name: getTeams && getTeams[0]?.name,
        });
      }
      setTeamList(getTeams);
    } else {
      let teams = response?.data?.teamList?.filter(
        (item: any) => item.isActive === true
      );

      if (selectTeam == false) {
        setTeam({
          Id: teams && teams[0]?.teamId,
          name: teams && teams[0]?.name,
        });
      }
      setTeamList(teams);
    }


    let tempProject: TeamProject[] | undefined = response?.data?.teamProjects
      ?.filter((item: TeamProject) => item.isActive === true)
      ?.sort((a: TeamProject, b: TeamProject) => {
        const priorityOrder: { [key: string]: number } = {
          High: 3,
          Medium: 2,
          Low: 1,
        };

        const priorityA = priorityOrder[a.priorities || ""] || 0;
        const priorityB = priorityOrder[b.priorities || ""] || 0;

        return priorityB - priorityA;
      });
    // let tempEmployee = response?.data?.teamEmployees?.filter(
    //   (item: any) => item?.teamId == response?.data?.teamList[0]?.teamId
    // );

    // let teamWeeklyPlan = response?.data?.weeklyPlan?.filter(
    //   (item: any) => item?.teamId == response?.data?.teamList[0]?.teamId
    // );

    // let employeeTime = response?.data?.employeeTime?.filter(
    //   (item: any) => item?.teamId == response?.data?.teamList[0]?.teamId
    // );


    setTeamWeeklyPlan(response?.data?.weeklyPlan);
    setEmployeeTime(response?.data?.employeeTime);
    setTeamProject(tempProject);
    setTeamEmployee(response?.data?.teamEmployees);
    setTimeout(() => {
      setLoading(false);
    }, 2500);

  }

  // setTeam(teamList[0]?.name)

  const getColorBasedOnPriority = (priority: string | null | undefined) => {
    switch (priority) {
      case "High":
        return "red";
      case "Medium":
        return "#E79711";
      case "Low":
        return "green";
      default:
        return "black";
    }
  };

  useEffect(() => {
    fetchData();
  }, [reload, team?.Id, role]);

  const handleteamClick = (clickedTeam: any) => {
    // var teamProject = Data.teamProjects
    //   ?.filter(
    //     (item: any) =>
    //       item?.teamId === clickedTeam.teamId && item.isActive === true
    //   )
    //   ?.sort((a: TeamProject, b: TeamProject) => {
    //     const priorityOrder: { [key: string]: number } = {
    //       High: 3,
    //       Medium: 2,
    //       Low: 1,
    //     };

    //     const priorityA = priorityOrder[a.priorities || ""] || 0;
    //     const priorityB = priorityOrder[b.priorities || ""] || 0;

    //     return priorityB - priorityA;
    //   });

    // var teamEmployee = Data.teamEmployees?.filter(
    //   (item: any) => item?.teamId === clickedTeam.teamId
    // );
    // var employeeTime = Data.employeeTime?.filter(
    //   (item: any) => item?.teamId === clickedTeam.teamId
    // );

    // var teamWeeklyPlan = Data.weeklyPlan?.filter(
    //   (item: any) => item?.teamId === clickedTeam.teamId
    // );

    setTeam({ Id: clickedTeam.teamId, name: clickedTeam.name });
    // setTeamEmployee(teamEmployee);
    // setTeamProject(teamProject);
    // setEmployeeTime(employeeTime);
    // setTeamWeeklyPlan(teamWeeklyPlan);
    setProjectStatus(true);
    setSelectTeam(true);
  };

  const handleWeekendingDateChange = (e: any) => {
    var date = new Date(e);
    var day = date.getUTCDay();
    if (day === 5) {
      date = e;
      setWeekEndingDate(e);
      setReload((prev) => !prev);
    } else {
      Swal.fire({
        icon: "error",
        title: "Please Select Only Friday!",
        showConfirmButton: true,
      });

      if (weekEndRef.current) {
        console.log(dayjs(weekEndingDate));
        weekEndRef.current.value = dayjs(weekEndingDate);
      }
    }
  };

  return (
    <div style={{ background: "#d5f0ef" }}>
      <div className="d-flex justify-content-between">
        <Breadcrumbs className="mx-3" separator=">">
          <Typography className="mt-3" sx={{ fontWeight: "bold" }}>
            Home
          </Typography>
        </Breadcrumbs>

        <div className="row d-flex justify-content-end mx-2 w-75">
          <div
            className="col-md-4 col-sm-2 col-lg-2"
          // style={{ display: role === "Customer" ? "none" : "block" }}
          >
            <label htmlFor="weekEndingDate">Week Ending Date</label>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                defaultValue={dayjs(weekEndingDate)}
                className="form-control custom-date-picker"
                ref={weekEndRef}
                format="DD-MM-YYYY"
                onChange={async (e: any) => {
                  handleWeekendingDateChange(ConvertToISO(e["$d"]));
                }}
              />
            </LocalizationProvider>
          </div>
        </div>
      </div>
      <div className="row m-auto g-2 mt-3" style={{ width: "93%" }}>
        <Grid container sx={{ justifyContent: "space-evenly" }}>
          <Grid xs={12} md={6} lg={4} sx={{ mt: "2%" }}>
            <div className="shadow bg-light scroll1 position-relative border border-2 rounded-2 w-auto">
              {role === "Admin" ? (
                <h5 className="text-center m-2" style={{ color: "#eb4007" }}>
                  Team Workload{" "}
                  {Data?.teamList?.length > 0 && `(${unAssignedHours.toFixed(2)})`}
                </h5>
              ) : (
                <h5 className="text-center m-2" style={{ color: "#eb4007" }}>
                  Teams
                </h5>
              )}
              {!loading && teamList?.length !== 0 && (
                <>
                  <h6 className="text-center">
                    Total Teams : <Link to={""}>{teamList?.length}</Link>
                  </h6>
                  <div
                    className="position-absolute assign-hour-toggle"
                    style={{
                      top: 25,
                      right: 0,
                      display: role === "Customer" ? "none" : "block",
                    }}
                  >
                    <FormControlLabel
                      control={
                        <IOSSwitch
                          defaultChecked
                          sx={{ m: 1 }}
                          onChange={(e: any) => {
                            setTeamStatus(e.target.checked);
                            var teamList = Data.teamList?.filter(
                              (item: any) => item.isActive === e.target.checked
                            );
                            setTeamList(teamList);
                          }}
                        />
                      }
                      label={`${teamStatus ? "Active" : "In Active"}`}
                    />
                  </div>
                </>
              )}
              <div className="mt-3 fixTableHead" style={{ height: "55vh" }}>
                {loading && teamList?.length === 0 ? (
                  <div className="mx-2">
                    {skleton.map((e) => (
                      <Skeleton key={e + 7} />
                    ))}
                  </div>
                ) : (
                  <>
                    {teamList?.length > 0 ? (
                      <table className="table table-bordered">
                        <thead>
                          <tr>
                            <th className="text-left">Team Name</th>
                            <th className="text-center">Assigned Hours</th>
                            {role === "Admin" && (
                              <th className="text-center">Un Assigned Hours</th>
                            )}
                          </tr>
                        </thead>
                        {teamList.map((e: any, index: number) => {
                          counter++;
                          return (
                            <tbody key={index}>
                              <tr>
                                <td
                                  className="text-left"
                                  style={{
                                    color: `${team.Id == e.teamId ? "#1031e8" : ""
                                      }`,
                                    cursor: "pointer",
                                  }}
                                  onClick={() => {
                                    handleteamClick(e);
                                    setProjectStatus(true);
                                  }}
                                >
                                  {e.name}
                                </td>
                                <td
                                  className={`text-center ${e.assignedHours < 40
                                      ? "text-bold"
                                      : "text-success"
                                    }`}
                                >
                                  {e.assignedHours}hrs
                                </td>
                                {role === "Admin" && (
                                  <td className="text-center">
                                    {e.unAssignedHours > 0
                                      ? e.unAssignedHours
                                      : 0}
                                    hrs
                                  </td>
                                )}
                              </tr>
                            </tbody>
                          );
                        })}
                      </table>
                    ) : (
                      <h6 className="mt-3 d-flex align-items-center justify-content-center">
                        <ErrorOutlineIcon className="mx-1" />
                        No Teams
                      </h6>
                    )}
                  </>
                )}
              </div>
            </div>
          </Grid>
          {role == "Customer" ? (
            <Grid xs={12} md={6} lg={4} sx={{ mt: "2%" }}>
              {/* &nbsp;&nbsp; */}
              <div className="shadow bg-light border border-2 rounded-2 mx-1 ">
                <h5 className="text-center m-1" style={{ color: "#17e8de" }}>
                  Team Employees
                  {teamEmployee?.length > 0 && ` (${teamEmployee?.length})`}
                </h5>
                {!loading && (
                  <h6 className="text-center">
                    Team Name :{" "}
                    <Link
                      to={`/${role}/${role === "Customer" ? "Team" : "TeamTaskQuadrant"}`}
                      state={{
                        data: {
                          teamId: team.Id,
                          teamName: team.name,
                          weekEndingDate: weekEndingDate,
                          teamRoute: true,
                        },
                        route: "adminDashBoard",
                      }}
                    >
                      {team?.name}
                    </Link>
                  </h6>
                )}
                <div
                  className="fixTableHead mt-4"
                  style={{ height: "55vh", width: "auto" }}
                >
                  {loading ? (
                    <div className="mx-2">
                      {skleton.map((e) => (
                        <Skeleton key={e + 7} />
                      ))}
                    </div>
                  ) : (
                    <>
                      {teamEmployee?.length > 0 ? (
                        <table className="table table-bordered">
                          <thead>
                            <tr>
                              <th className="text-left">Employee Name</th>
                              <th className="text-center">Assigned Hours</th>
                              {role == "Customer" ? (
                                <></>
                              ) : (
                                <th className="text-center">
                                  Un Assigned Hours
                                </th>
                              )}
                            </tr>
                          </thead>
                          {teamEmployee?.map((e: any, index: number) => {
                            const tooltipTitle = e.projectHours.map(
                              (ph: any) => (
                                <div key={index}>
                                  {ph.project}: {ph.hours}hrs
                                  <br />
                                </div>
                              )
                            );
                            return (
                              <tbody>
                                <tr key={e.employeeName}>
                                  <td className="text-left">
                                    {role !== "Customer" ? (
                                      <Link
                                        to={`/${role}/EmployeeOverView`}
                                        className="tableStyle"
                                        state={{
                                          employeeId: e.employeeId,
                                          employeeName: e.employeeName,
                                          weekendingDate: weekEndingDate,
                                          route: "adminDashboard",
                                        }}
                                      >
                                        <ToolTip
                                          title={
                                            tooltipTitle.length > 0 &&
                                            tooltipTitle
                                          }
                                        >
                                          {e.employeeName}
                                        </ToolTip>
                                      </Link>
                                    ) : (
                                      <ToolTip
                                        title={
                                          tooltipTitle.length > 0 &&
                                          tooltipTitle
                                        }
                                      >
                                        {e.employeeName}
                                      </ToolTip>
                                    )}
                                  </td>
                                  <td className="text-center">
                                    {e.assignedHours}
                                  </td>
                                  {role == "Customer" ? (
                                    <></>
                                  ) : (
                                    <td className="text-center">
                                      {e.unassignedHours < 0
                                        ? 0
                                        : e.unassignedHours}
                                    </td>
                                  )}
                                </tr>
                              </tbody>
                            );
                          })}
                        </table>
                      ) : (
                        <h6 className="mt-3 d-flex align-items-center justify-content-center">
                          <ErrorOutlineIcon className="mx-1" />
                          No Employees
                        </h6>
                      )}
                    </>
                  )}
                </div>
              </div>
            </Grid>
          ) : (
            <Grid xs={12} md={6} lg={4} sx={{ mt: "2%" }}>
              <div className="shadow bg-light border border-2 rounded-2 mx-1 ">
                <h5 className="text-center m-2 text-primary">
                  Team Attendance
                  {teamEmployee?.length > 0 && ` (${teamEmployee?.length})`}
                </h5>
                {!loading && (
                  <h6 className="text-center">
                    Team Name :{" "}
                    {role === "Customer" ? (
                      <>{team?.name ? team?.name : teamList[0].name}</>
                    ) : (
                      <Link
                        to={`/Admin/Attendance`}
                        state={{
                          teamId: team.Id,
                          team: team.name,
                          route: "adminDashboard",
                        }}
                      >
                        {team?.name}
                      </Link>
                    )}
                  </h6>
                )}
                <div className="mt-3 fixTableHead" style={{ height: "55vh" }}>
                  {loading ? (
                    <div className="mx-1">
                      {skleton.map((e) => (
                        <Skeleton key={e + 2} />
                      ))}
                    </div>
                  ) : (
                    <>
                      {employeeTime?.length > 0 ? (
                        <table className="table table-bordered">
                          <thead>
                            <tr>
                              <th className="text-left">Employee Name</th>
                              <th className="text-center">Avg In Time</th>
                              <th className="text-center">In Time</th>
                              <th className="text-center">Out Time </th>
                            </tr>
                          </thead>
                          <tbody>
                            {employeeTime?.map((e: any, index: number) => {
                              return (
                                <tr key={index}>
                                  <td className="text-left">
                                    {role === "Customer" ? (
                                      <>{e.employeeName}</>
                                    ) : (
                                      <Link
                                        to={`/Admin/EmployeeAttendence`}
                                        state={{
                                          userId: e.userId,
                                          route: "adminDashboard",
                                        }}
                                      >
                                        {e.employeeName.replace(
                                          Regex.CHAR_SPACE,
                                          " "
                                        )}
                                      </Link>
                                    )}
                                  </td>
                                  <td className={`text-center`}>
                                    {convertTo12HourFormat(e.avgInTime)}
                                  </td>
                                  <td className={`text-center`}>
                                    {ConvertTime(e.inTime, "")}
                                  </td>
                                  <td className="text-center">
                                    {ConvertTime(e.outTime, "")}
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      ) : (
                        <h6 className="mt-3 d-flex align-items-center justify-content-center">
                          <ErrorOutlineIcon className="mx-1" />
                          No Team Attendance
                        </h6>
                      )}
                    </>
                  )}
                </div>
              </div>
            </Grid>
          )}
          <Grid xs={12} md={12} lg={4} sx={{ mt: "2%" }}>
            <div className="shadow bg-light position-relative border border-2 rounded-2 col-md">
              <h5 className="text-center m-2" style={{ color: "#17e8de" }}>
                Team Projects
                {teamProject?.length > 0 && ` (${teamProject?.length})`}
              </h5>
              {!loading && (
                <>
                  <h6 className="text-center" style={{ marginRight: '40px' }}>
                    Team Name :{" "}
                    <Link to={`/${role}/Project`} >
                      <ToolTip title={team?.name}>
                        {team?.name?.length > 17 ? `${team?.name.substring(0, 15)}...` : team?.name}
                      </ToolTip>
                    </Link>
                  </h6>
                  <div
                    className="position-absolute project-toggle"
                    style={{
                      top: 25,
                      right: 0,
                      display: role === "Customer" ? "none" : "block",
                    }}
                  >
                    <FormControlLabel
                      control={
                        <IOSSwitch
                          sx={{ m: 1 }}
                          checked={projectStatus}
                          onChange={(e) => {
                            setProjectStatus(e.target.checked);
                            var teamProject = Data.teamProjects
                              ?.filter(
                                (item: any) =>
                                  item?.teamId === team.Id &&
                                  item.isActive === e.target.checked
                              )
                              ?.sort((a: TeamProject, b: TeamProject) => {
                                const priorityOrder: { [key: string]: number } =
                                {
                                  High: 3,
                                  Medium: 2,
                                  Low: 1,
                                };
                                const priorityA =
                                  priorityOrder[a.priorities || ""] || 0;
                                const priorityB =
                                  priorityOrder[b.priorities || ""] || 0;

                                return priorityB - priorityA;
                              });
                            setTeamProject(teamProject);
                          }}
                        />
                      }
                      label={`${projectStatus ? "Active" : "In Active"}`}
                    />
                  </div>
                </>
              )}
              <div className=" mt-3 fixTableHead" style={{ height: "55vh" }}>
                {loading ? (
                  <div className="mx-2">
                    {skleton.map((e) => (
                      <Skeleton key={e + 3} />
                    ))}
                  </div>
                ) : (
                  <>
                    {teamProject?.length > 0 ? (
                      <table className="table table-bordered">
                        <thead>
                          <tr>
                            <th className="text-left">Project Name</th>
                            <th className="text-left">Hours</th>
                            <th className="text-left">Status</th>
                            <th className="text-center">Percentage</th>
                          </tr>
                        </thead>
                        <tbody>
                          {teamProject.map((e: any, index: number) => {
                            return (
                              <tr key={index}>
                                <td>
                                  {e?.priorities != null ? (
                                    <ToolTip
                                      title={e?.priorities}
                                      sx={{ fontSize: "450%" }}
                                    >
                                      <TurnedInIcon
                                        sx={{
                                          color: getColorBasedOnPriority(
                                            e?.priorities
                                          ),
                                          fontSize: "150%",
                                        }}
                                      />
                                    </ToolTip>
                                  ) : (
                                    <></>
                                  )}
                                  <Link
                                    to={`/${role}/ProjectQuadrant`}
                                    state={{
                                      projectId: e.projectId,
                                      projectName: e.projectName,
                                      startDate: e.startDate,
                                      projectuserstory: {
                                        isActive: e.isActive,
                                      },
                                      endDate: e.endDate,
                                      status: e.Status,
                                      route: "adminDashboard",
                                    }}
                                  >
                                    {e.projectName.length > 15 ? (
                                      <>
                                        <ToolTip title={e.projectName}>
                                          {e.projectName.slice(0, 15) + "..."}
                                        </ToolTip>
                                      </>
                                    ) : (
                                      <>{e.projectName}</>
                                    )}
                                  </Link>
                                </td>
                                <td>{e.hours}</td>
                                <td
                                  className={`text-left ${e.percentage < 100
                                      ? "text-warning"
                                      : "test-success"
                                    }`}
                                >
                                  {e.status}
                                </td>
                                <td className="text-center">{e.percentage}</td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    ) : (
                      <h6 className="mt-3 d-flex align-items-center justify-content-center">
                        <ErrorOutlineIcon className="mx-1" />
                        No Projects
                      </h6>
                    )}
                  </>
                )}
              </div>
            </div>
          </Grid>
          <Grid lg={6} xs={12} sm={12} sx={{ mt: "2%" }}>
            <div className="shadow mx-1  d-flex position-relative flex-column align-items-center bg-light border border-2 rounded-2 col-md">
              <h5 className="text-center m-1" style={{ color: "#07eb1a" }}>
                Team Weekly Deliverable
              </h5>
              <div className="position-absolute project-toggle">
                <Link
                  to={`/${role}/WeeklyPlan`}
                  className="btn btn-primary btn-sm m-2"
                >
                  {role === "Customer" ? "View Weekly Plan" : "Add Weekly Plan"}
                </Link>
              </div>
              {!loading && (
                <h6 className="text-center">
                  Team Name :{" "}
                  <Link
                    to={`/${role}/WeeklyPlan`}
                    state={{
                      teamId: team.Id,
                      team: team.name,
                      weekEndingDate: weekEndingDate,
                      route: "adminDashboard",
                    }}
                  >
                    {team?.name}
                  </Link>
                </h6>
              )}
              <div
                className="fixTableHead mt-3 w-100"
                style={{ height: "55vh" }}
              >
                {loading ? (
                  <div className="mx-1" style={{ width: "100%" }}>
                    {skleton.map((e) => (
                      <Skeleton key={e + 4} />
                    ))}
                  </div>
                ) : (
                  <>
                    {teamWeeklyPlan?.length > 0 ? (
                      <table className="table table-bordered">
                        <thead>
                          <tr>
                            <th className="text-left">Employee Name</th>
                            <th className="text-left">Project Name</th>
                            <th className="text-left">Due Date</th>
                            <th className="text-left">Deliverable</th>
                            <th className="text-left">Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {teamWeeklyPlan.map((e: any, index: number) => (
                            <tr key={index}>
                              <td className="text-left col-md-3">
                                <Link
                                  to={`/${role}/WeeklyPlan`}
                                  state={{
                                    employeeId: e.employeeId,
                                    employeeName: e.employeeName,
                                    weekEndingDate: weekEndingDate,
                                    route: "adminDashboard",
                                  }}
                                >
                                  {e.employee?.length > 0
                                    ? e.employee?.map(
                                      (emp: Employee, index: number) =>
                                        emp?.name?.replace(
                                          Regex.CHAR_SPACE,
                                          " "
                                        ) +
                                        `${index !== e.employee?.length - 1
                                          ? ","
                                          : ""
                                        }`
                                    )
                                    : "-"}
                                </Link>
                              </td>
                              <td className="text-left  col-2">
                                {e.projectName}
                              </td>
                              <td className="text-left col-2">
                                {ConvertDate(e.dueDate)}
                              </td>
                              <td className="text-left">{e.description}</td>
                              <td className="text-left">{e.status}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <h6 className="mt-3 d-flex align-items-center justify-content-center">
                        <ErrorOutlineIcon className="mx-1" />
                        No Weelky Plan Deliverable
                      </h6>
                    )}
                  </>
                )}
              </div>
            </div>
          </Grid>
          {role == "Admin" ? (
            <Grid xs sx={{ mt: "2%" }}>
              <div className="shadow bg-light scroll1 position-relative border border-2 rounded-2 w-a">
                <h5 className="text-center m-1" style={{ color: "#17e8de" }}>
                  Team Employees
                  {teamEmployee?.length > 0 && ` (${teamEmployee?.length})`}
                </h5>
                {!loading && (
                  <h6 className="text-center">
                    Team Name :{" "}
                    <Link
                      to={`/${role}/TeamTaskQuadrant`}
                      state={{
                        data: {
                          teamId: team.Id,
                          teamName: team.name,
                          weekEndingDate: weekEndingDate,
                          teamRoute: true,
                        },
                        route: "adminDashBoard",
                      }}
                    >
                      {team?.name}
                    </Link>
                  </h6>
                )}
                <div
                  className="fixTableHead mt-4"
                  style={{ height: "55vh", width: "auto" }}
                >
                  {loading ? (
                    <div className="mx-2">
                      {skleton.map((e) => (
                        <Skeleton key={e + 7} />
                      ))}
                    </div>
                  ) : (
                    <>
                      {teamEmployee?.length > 0 ? (
                        <table className="table table-bordered">
                          <thead>
                            <tr>
                              <th className="text-left">Employee Name</th>
                              <th className="text-center">Assigned Hours</th>
                              <th className="text-center">Un Assigned Hours</th>
                            </tr>
                          </thead>
                          {teamEmployee?.map((e: any, index: number) => {
                            const tooltipTitle = e.projectHours.map(
                              (ph: any) => (
                                <div key={index}>
                                  {ph.project}: {ph.hours}hrs
                                  <br />
                                </div>
                              )
                            );
                            return (
                              <tbody>
                                <tr key={e.employeeName}>
                                  <td className="text-left col-md-3">
                                    <Link
                                      to={`/${role}/EmployeeOverView`}
                                      className="tableStyle"
                                      state={{
                                        employeeId: e.employeeId,
                                        employeeName: e.employeeName,
                                        weekendingDate: weekEndingDate,
                                        route: "adminDashboard",
                                      }}
                                    >
                                      <ToolTip
                                        title={
                                          tooltipTitle.length > 0 &&
                                          tooltipTitle
                                        }
                                      >
                                        {e.employeeName.replace(
                                          Regex.CHAR_SPACE,
                                          " "
                                        )}
                                      </ToolTip>
                                    </Link>
                                  </td>
                                  <td className="text-center">
                                    {e.assignedHours}
                                  </td>
                                  <td className="text-center">
                                    {e.unassignedHours < 0
                                      ? 0
                                      : e.unassignedHours}
                                  </td>
                                </tr>
                              </tbody>
                            );
                          })}
                        </table>
                      ) : (
                        <h6 className="mt-3 d-flex align-items-center justify-content-center">
                          <ErrorOutlineIcon className="mx-1" />
                          No Team Employees
                        </h6>
                      )}
                    </>
                  )}
                </div>
              </div>
            </Grid>
          ) : (
            <Grid xs={6} sx={{ mt: "2%" }}>
              <div className="shadow bg-light scroll1 position-relative border border-2 rounded-2 w-a">
                <h5 className="text-center m-1" style={{ color: "#17e8de" }}>
                  Project Status
                  {teamProject?.length > 0 && ` (${teamProject?.length})`}
                </h5>
                {!loading && (
                  <h6 className="text-center">
                    Team Name :{" "}
                    <Link
                      to={`/${role}/Team`}
                      state={{
                        teamId: team.Id,
                        team: team.name,
                        weekEndingDate: weekEndingDate,
                        route: "adminDashboard",
                      }}
                    >
                      {team?.name}
                    </Link>
                  </h6>
                )}
                <div
                  className="fixTableHead mt-4"
                  style={{ height: "55vh", width: "auto" }}
                >
                  {loading ? (
                    <div className="mx-2">
                      {skleton.map((e) => (
                        <Skeleton key={e + 7} />
                      ))}
                    </div>
                  ) : (
                    <>
                      {teamProject?.length > 0 ? (
                        <table className="table table-bordered">
                          <thead>
                            <tr>
                              <th className="text-left">Project Name</th>
                              <th className="text-center">
                                Total User Stories
                              </th>
                              <th className="text-center">
                                Completed User Stories
                              </th>
                              <th className="text-center">
                                In Progress User Stories
                              </th>
                              <th className="text-center">
                                Pending User Stories
                              </th>
                            </tr>
                          </thead>
                          {teamProject?.map((e: any) => {
                            return (
                              <tbody>
                                <tr key={e.projectName}>
                                  <td className="text-left col-md-3">
                                    {e.projectName}
                                  </td>
                                  <td className="text-center">
                                    {e.totalUserStories}
                                  </td>
                                  <td className="text-center">
                                    {e.completedUserStories}
                                  </td>
                                  <td className="text-center">
                                    {e.inProgressUserStories}
                                  </td>
                                  <td className="text-center">
                                    {e.pendingUserStories}
                                  </td>
                                </tr>
                              </tbody>
                            );
                          })}
                        </table>
                      ) : (
                        <h6 className="mt-3 d-flex align-items-center justify-content-center">
                          <ErrorOutlineIcon className="mx-1" />
                          No Project Status
                        </h6>
                      )}
                    </>
                  )}
                </div>
              </div>
            </Grid>
          )}
        </Grid>
      </div>
      {/* <div className="row mt-2 mx-4 g-2">
       
       
      </div> */}
    </div>
  );
};
