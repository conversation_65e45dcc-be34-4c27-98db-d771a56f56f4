import {
  Autocomplete,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  TextareaAutosize,
} from "@mui/material";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Project } from "../../../Models/Project/Project";
import { useContextProvider } from "../../../CommonComponents/Context";
import { useForm } from "react-hook-form";
import { Category, CommonMaster } from "../../../Models/Common/CommonMaster";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { Regex } from "../../../Constants/Regex/Regex";
import { ConvertToISO } from "../../../Utilities/Utils";
import { Team } from "../../../Models/Team/Team";
import { PRIORITYTYPE } from "../../../Constants/Common/CommonMaster";
import { AlertOption, ModalAction } from "../../../Models/Common/AlertOptions";
import { Get, Post } from "../../../Services/Axios";
import { IWeeklyPlan } from "../../../Models/WeeklyPlan/WeeklyPlan";
import Swal from "sweetalert2";

type EditWeeklyPlanProps = {
  open: ModalAction | null;
  setOpen: (open: ModalAction) => void;
  setReload: Dispatch<SetStateAction<boolean>>;
  weeklyPlan: IWeeklyPlan | null;
};

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const formFields = [
  "id",
  "project",
  "category",
  "description",
  "notes",
  "priority",
  "estimatedHours",
  "status",
  "dueDate",
  "teamId",
  "EmpId",
  "weekEndingDate",
  "CreatedBy",
  "UpdatedBy",
];

export const EditWeeklyPlan = ({
  open,
  setOpen,
  setReload,
  weeklyPlan,
}: EditWeeklyPlanProps) => {
  const [projects, setProjects] = useState<Array<Project>>([]);
  const { commonMaster, category } = useContextProvider();
  const [teams, setTeams] = useState([]);
  const [teamId, setTeamId] = useState<number | undefined>(weeklyPlan?.teamId!);
  const { register, handleSubmit, resetField } = useForm();
  const [teamMemebers, setTeamMembers] = useState<any>([]);
  const [employeeId, setEmployeeId] = useState<number[]>(
    weeklyPlan?.empId!.split("|").map(Number) || []
  );
  const [porjectId, setProjectId] = useState<number>(
    weeklyPlan?.projectId ?? 0
  );

  const handleTeamChange = async (e: any, selectedOption: any) => {
    if (!selectedOption || !selectedOption.id) {
      return;
    }
    setEmployeeId([]);
    const { id } = selectedOption;
    setTeamId(id);
    var teamMembers: any = await Get(
      `/app/Team/GetTeamEmployeelist?teamId=${id}`
    );
    setTeamMembers(teamMembers.data || []);
    return e;
  };

  function reset() {
    formFields.map((e: string) => {
      resetField(e);
    });
  }

  useEffect(() => {
    fetchData();
  }, []);

  async function fetchData() {
    var response: any = await Get("/app/Project/GetProjectList");
    var teamList: any = await Get("app/Team/GetTeamList");
    var teamMembers: any = await Get(
      `/app/Team/GetTeamEmployeelist?teamId=${weeklyPlan?.teamId}`
    );
    setTeamMembers(teamMembers.data || []);
    setTeams(teamList.data || []);
    setProjects(response.data || []);
  }

  async function onSubmitHandler(data: any) {
    var weeklyPlan: IWeeklyPlan = data;
    weeklyPlan.projectId = porjectId;
    weeklyPlan.teamId = teamId ?? 0;
    weeklyPlan.empId = employeeId.join("|");
    const employeeNames = teamMemebers
      .filter((employee: any) => employeeId.includes(employee.employeeId))
      .map((employee: any) => employee.employeeName);
    weeklyPlan.employees = employeeNames.join("|");
    const { error }: any = await Post("app/WeeklyPlan/UpdateWeeklyPlan", data);
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Weekly Plan Updated Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
      handleClose();
    });
  }

  function handleClose() {
    reset();
    setOpen({ add: false });
  }

  return (
    <Dialog open={open?.edit ?? false}>
      <form onSubmit={handleSubmit(onSubmitHandler)}>
        <div className="dialog-title-container">
          <DialogTitle className="dialog-title">Edit Weekly Plan</DialogTitle>
          <CancelOutlinedIcon
            className="close-icon"
            onClick={() => handleClose()}
          />
        </div>
        <DialogContent className="row popup d-flex justify-content-center">
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <Autocomplete
                  options={projects
                    .filter((x: any) => x.isActive === true)
                    .map((project: Project) => ({
                      label: project.name,
                      id: project.id,
                    }))}
                  onChange={(e: any, value: any) => {
                    setProjectId(value?.id);
                    return e;
                  }}
                  defaultValue={{
                    label: weeklyPlan?.project,
                    id: weeklyPlan?.projectId,
                  }}
                  // className="col-6 mb-2"
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Project"
                      required
                      {...register("project")}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <Autocomplete
                  options={teams
                    .filter((x: any) => x.isActive === true)
                    .map((team: Team) => ({
                      label: team.name,
                      id: team.id,
                    }))}
                  // className="col-6 mb-2"
                  onChange={handleTeamChange}
                  defaultValue={{
                    label: weeklyPlan?.team,
                    id: weeklyPlan?.teamId,
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Team"
                      required
                      {...register("teamId")}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="project-type">Employee</InputLabel>
                <Select
                  labelId="employee"
                  id="employees"
                  multiple
                  value={employeeId}
                  {...register("TechStackId", {
                    onChange: (e: any) => {
                      setEmployeeId(e.target.value);
                    },
                  })}
                  input={<OutlinedInput label="Employee" />}
                  renderValue={(selected) => (
                    <div>
                      {selected.map((value: number) => (
                        <span key={value} style={{ marginRight: 8 }}>
                          {
                            teamMemebers.find(
                              (option: any) => option.employeeId === value
                            )?.employeeName
                          }
                          ,
                        </span>
                      ))}
                    </div>
                  )}
                  MenuProps={MenuProps}
                >
                  {teamMemebers.map((member: any) => (
                    <MenuItem key={member.employeeId} value={member.employeeId}>
                      <Checkbox
                        checked={employeeId.indexOf(member.employeeId) > -1}
                      />
                      <ListItemText primary={member.employeeName} />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <TextField
                  required
                  // className="col m-2"
                  label="Weekly Deliverable"
                  defaultValue={`${weeklyPlan?.notes}`}
                  {...register("notes", {
                    onChange: (e: any) => {
                      e.target.value = e.target.value.replace(
                        Regex.CHAR_NUM,
                        ""
                      );
                    },
                  })}
                  type="text"
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={12}>
              <FormControl fullWidth>
                <TextareaAutosize
                  required
                  // className="col  m-2 form-control"
                  defaultValue={`${weeklyPlan?.description}`}
                  {...register("description", {
                    onChange: (e: any) => {
                      e.target.value = e.target.value.replace(
                        Regex.CHAR_NUM,
                        ""
                      );
                    },
                  })}
                  placeholder="Description*"
                  style={{ height: 100 }}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <TextField
                  required
                  // className="col m-2"
                  label="Estimated Hours"
                  defaultValue={`${weeklyPlan?.estimatedHours}`}
                  {...register("estimatedHours", {
                    onChange: (e: any) => {
                      e.target.value = e.target.value.replace(
                        Regex.DECIMAL,
                        ""
                      );
                    },
                  })}
                  type="text"
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <Autocomplete
                  options={category.map((category: Category) => ({
                    label: category.subCategory,
                    id: category.id,
                  }))}
                  // className="col-6 mb-2 mt-2"
                  defaultValue={{ label: weeklyPlan?.category, id: 0 }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Category"
                      required
                      {...register("category", { value: weeklyPlan?.category })}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <InputLabel id="start-date">Due Date*</InputLabel>
              <FormControl fullWidth>
                <TextField
                  required
                  id="start-date"
                  margin="dense"
                  defaultValue={ConvertToISO(`${weeklyPlan?.dueDate}`)}
                  inputProps={{
                    min: ConvertToISO(`${weeklyPlan?.dueDate}`),
                  }}
                  type="date"
                  {...register("dueDate")}
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <InputLabel id="end-date">Week Ending Date*</InputLabel>
              <FormControl fullWidth>
                <TextField
                  required
                  id="end-date"
                  margin="dense"
                  defaultValue={ConvertToISO(`${weeklyPlan?.weekEndingDate}`)}
                  inputProps={{
                    min: new Date().toISOString().slice(0, 10),
                  }}
                  {...register("weekEndingDate")}
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <Autocomplete
                  options={commonMaster
                    .filter((x) => x.codeType === PRIORITYTYPE)
                    .map((commonMaster: CommonMaster) => ({
                      label: commonMaster.codeValue,
                      id: commonMaster.codeValue,
                    }))}
                  defaultValue={{ label: `${weeklyPlan?.priority}`, id: "0" }}
                  // className="col-6 mb-2 mt-2"
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Priority"
                      required
                      {...register("priority", { value: weeklyPlan?.priority })}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  {...register("status")}
                  label="Status"
                  defaultValue={weeklyPlan?.status}
                >
                  <MenuItem value="Pending">Pending</MenuItem>
                  <MenuItem value="In Progress">In Progress</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          <input {...register("CreatedBy")} value="user" hidden />
          <input {...register("UpdatedBy")} value="user" hidden />
          <input {...register("status")} value={weeklyPlan?.status!} hidden />
          <input {...register("id")} value={weeklyPlan?.id} hidden />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => handleClose()}
            size="medium"
            variant="contained"
            color="error"
          >
            Cancel
          </Button>
          <Button
            size="medium"
            variant="contained"
            color="success"
            type="submit"
          >
            Save
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
