import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogTitle,
  <PERSON>vider,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  DialogContent,
  Grid,
  FormControl,
  Autocomplete,
  TextField,
  Breadcrumbs,
  Typography,
} from "@mui/material";
import { useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Get, Post } from "../../../Services/Axios";
import { useQuery } from "react-query";
import "../../../StyleSheets/Quadrant.css";
import PersonIcon from "@mui/icons-material/Person";
import DescriptionIcon from "@mui/icons-material/Description";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CircleIcon from "@mui/icons-material/Circle";
import TaskIcon from "@mui/icons-material/Task";
import { useNavigate } from "react-router-dom";
import React, { useState } from "react";
import BackDrop from "../../../CommonComponents/BackDrop";
import Swal from "sweetalert2";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { useContextProvider } from "../../../CommonComponents/Context";
import { ADMIN } from "../../../Constants/Common/Roles";
import InfoIcon from "@mui/icons-material/Info";
import axios from "axios";
const STATUS = ["All", "In-Progress", "Completed", "Ready-For-UAT"];

export const ProjectQuadrant = () => {
  const location = useLocation();
  // const json: any = sessionStorage.getItem("user") || null;
  // const sessionUser = JSON.parse(json);
  const [userId, setuserId] = useState(null);
  const [open, setopen] = useState<boolean>(false);
  const [statusFilter, setStatusFilter] = useState("All");
  const [weekEndingDateFilter, setWeekEndingDateFilter] = useState(null);
  const { role } = useContextProvider();
  // const [activeTab, setActiveTab] = useState("ProjectQuadrant");
  // const handleTabClick = (tab: any) => {
  //   setActiveTab(tab);
  // };

  async function getTeamName() {
    try {
      const [teamname, objective, tasks, Totaltask , EmpTask] = await axios.all([
        Get(`app/Team/GetTeamNames?projectId=${location?.state?.projectId}`),
        Get(
          `app/Project/GetProjectObjective?ProjectId=${location?.state?.projectId}`
        ),
        Get(
          `app/EmployeeTask/GetProjectTasklist?Id=${location?.state?.projectId}`
        ),
        Get(
          `app/Task/getProjectTaskList?projectId=${location?.state?.projectId}`
        ),
        Get(
          `app/EmployeeTask/GetEmpTask`
        ),
      ]);
      return { teamname, objective, tasks, Totaltask , EmpTask };
    } catch (error) {
      console.error("Error fetching data:", error);
      throw error; // Rethrow the error to propagate it to react-query
    }
  }

  const { data, isLoading, refetch }: any = useQuery(
    "ProjectQuadrant",
    getTeamName,
    { cacheTime: 10000 }
  );

  useEffect(() => {
    refetch();
  }, []);

  const navigate = useNavigate();
  const handleClick = () => {
    navigate(`/${role}/ProjectQuadrant/ProjectObjectiveList`, {
      state: { ...location.state },
    });
  };

  const roadmapClick = () => {
    navigate(`/${role}/ProjectQuadrant/ProjectRoadmapList`, {
      state: {
        ...location.state,
      },
    });
  };

  const ProjectReportClick = () => {
      navigate(`/${role}/ProjectQuadrant/ProjectReport`, {
        state: {
          ...location.state,
          projectName: location.state?.projectName,
          projectId: location?.state?.projectId,
          Projectuserstory: location.state?.projectuserstory,
        },
      });
  };

  async function AssignLead() {
    const { error }: any = await Post(
      `app/Project/AssignLead?userId=${userId}&projectId=${location.state?.projectId}`,
      ""
    );
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Assigning!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Lead Assigned Successfully!",
        icon: "success",
      };
    }
    Swal.fire({
      ...option,
      showCloseButton: true,
    }).then(() => {
      refetch();
    });
  }
  const handleStatusChange = (event: any) => {
    setStatusFilter(event.target.value);
  };

  const handleWeekEndingDateChange = (event: any) => {
    setWeekEndingDateFilter(event.target.value);
  };

  let filteredTasks: any[] = [];
  let weekEndingDateFilteredTasks: any[] = [];
  debugger
  if (role === "Customer") {
    filteredTasks = statusFilter === "All"
      ? data?.EmpTask?.data || []
      : data?.EmpTask?.data?.filter(
          (task: any) => task.status === statusFilter
        ) || [];
  
    weekEndingDateFilteredTasks = data?.EmpTask?.data?.filter((task: any) => {
      return task.weekEndingDate?.slice(0, 10) === weekEndingDateFilter;
    }) || [];
  } else {
    filteredTasks = statusFilter === "All"
      ? data?.tasks?.data || []
      : data?.tasks?.data?.filter(
          (task: any) => task.status === statusFilter
        ) || [];
  
    weekEndingDateFilteredTasks = data?.tasks?.data?.filter((task: any) => {
      return task.weekEndingDate?.slice(0, 10) === weekEndingDateFilter;
    }) || [];
  }
  
  

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Link to={`/${role}/Project`}>
          <Typography sx={{ fontWeight: "bold" }}>Project</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Project Quadrant</Typography>
      </Breadcrumbs>
      <Typography align="center" className="fw-bolder fs-3">
        Project Name: {location.state?.projectName}
      </Typography>
      <Grid container sx={{ mt: 3 }}>
        <div className="container shadow " style={{ maxHeight: "510vh" }}>
          <Grid container spacing={3} sx={{ pl: 7, pr: 6 }}>
            <Grid item xs={12} sm={12} md={6} lg={6}>
              <div className="item m-3">
                <List
                  sx={{
                    maxWidth: 780,
                    height: "50vh",
                    position: "relative",
                    bgcolor: "background.paper",
                    overflowY: `${
                      data?.teamname?.data?.length > 3 ? "scroll" : "hidden"
                    }`,
                    overflowX: "hidden",
                  }}
                  component="nav"
                  aria-labelledby="nested-list-subheader"
                  className=" firstTopBorder"
                  subheader={
                    <ListSubheader
                      component="div"
                      id="nested-list-subheader"
                      className="fw-bold"
                    >
                      <PersonIcon className="mx-2" />
                      Team Members ({data?.teamname?.data?.length || 0})
                    </ListSubheader>
                  }
                >
                  {data &&
                  data.teamname?.data !== "" &&
                  Array.isArray(data.teamname.data) ? (
                    <>
                      {data?.teamname?.data
                        .filter((e: any) => e?.userId === e?.leadId)
                        .map((lead: any) => (
                          <React.Fragment key={lead?.username}>
                            <ListItemButton>
                              <ListItemIcon>
                                <CircleIcon sx={{ fontSize: 10 }} />
                              </ListItemIcon>
                              <ListItemText
                                primary={`${lead?.username} (Lead)`}
                              />
                            </ListItemButton>
                            <Divider />
                          </React.Fragment>
                        ))}
                      {data.teamname.data
                        .filter((e: any) => e.userId !== e.leadId)
                        .map((member: any, index: number) => (
                          <React.Fragment key={index}>
                            <ListItemButton>
                              <ListItemIcon>
                                <CircleIcon sx={{ fontSize: 10 }} />
                              </ListItemIcon>
                              <ListItemText primary={member.username} />
                            </ListItemButton>
                            <Divider />
                          </React.Fragment>
                        ))}
                    </>
                  ) : (
                    <>
                      <ListItemButton>
                        <ListItemIcon>
                          <ErrorOutlineIcon />
                        </ListItemIcon>
                        <ListItemText primary="No data!" />
                      </ListItemButton>
                      <Divider />
                    </>
                  )}
                </List>
              </div>
              {role === ADMIN && (
                <button
                  className="btn btn-primary px-2 float-end"
                  style={{ marginRight: "2%" }}
                  onClick={() => {
                    setopen(true);
                  }}
                >
                  Assign Lead
                </button>
              )}
            </Grid>
            <Grid item xs={12} sm={12} md={6} lg={6}>
              <div className="item m-3 ">
                <List
                  sx={{
                    // width: "100vw",
                    maxWidth: 780,
                    height: "50vh",
                    position: "relative",
                    bgcolor: "background.paper",
                    overflowY: `${
                      data?.objective?.data?.projectObjectives?.length > 3
                        ? "scroll"
                        : "hidden"
                    }`,
                    overflowx: "hidden",
                  }}
                  component="nav"
                  aria-labelledby="nested-list-subheader"
                  className=" firstTopBorder"
                  subheader={
                    <ListSubheader
                      component="div"
                      id="nested-list-subheader"
                      className="fw-bold"
                    >
                      <DescriptionIcon className="mx-2" />
                      Project Objective (
                      {data?.objective?.data?.projectObjectives?.length || 0})
                    </ListSubheader>
                  }
                >
                  {(data &&
                    data?.objective?.data?.projectObjectives !== "" &&
                    data?.objective?.data?.projectObjectives?.map(
                      (e: any, index: number) => (
                        <React.Fragment key={index}>
                          <ListItemButton>
                            <ListItemIcon>
                              <CircleIcon sx={{ fontSize: 10 }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={`${e.description}`}
                              key={e.userId}
                            />
                          </ListItemButton>
                          <Divider />
                        </React.Fragment>
                      )
                    )) || (
                    <>
                      <ListItemButton>
                        <ListItemIcon>
                          <ErrorOutlineIcon />
                        </ListItemIcon>
                        <ListItemText primary="No data!" />
                      </ListItemButton>
                      <Divider />
                    </>
                  )}
                </List>
              </div>
              <div className="row">
                <div>
                  <button
                    className="btn btn-info px-2 float-end"
                    onClick={roadmapClick}
                    style={{ marginLeft: "7%", marginRight: "2%" }}
                  >
                    Project Roadmap
                  </button>
                  <button
                    className="btn btn-primary px-2 float-end"
                    onClick={handleClick}
                  >
                    Objective List
                  </button>
                </div>
              </div>
            </Grid>
            <Grid item xs={12} sm={12} md={6} lg={6}>
              <div className="item m-3 ">
                  <List
                    sx={{
                      maxWidth: 780,
                      height: "50vh",
                      position: "relative",
                      bgcolor: "background.paper",
                      overflowY: `${
                        data?.tasks?.data?.length > 3 ? "scroll" : "hidden"
                      }`,
                      overflowX: "hidden",
                    }}
                    className=" firstTopBorder"
                    component="nav"
                    aria-labelledby="nested-list-subheader"
                    subheader={
                      <ListSubheader
                        component="div"
                        id="nested-list-subheader"
                        className="fw-bold"
                      >
                        <TaskIcon className="mx-2" />
                        Project Tasks ({role == "Customer" ? data?.EmpTask?.data?.length : data?.tasks?.data?.length || 0})
                        <div className="d-flex container flex-column">
                          <div className="row">
                            <label className="col-5">Status</label>
                            <label className="col-7">Week Ending Date</label>
                          </div>
                          <div className="row w-100">
                            <div className="col-5">
                              <select
                                className="form-select col-md-4 col-sm-12 mb-2"
                                onChange={handleStatusChange}
                              >
                                {STATUS.map((status) => (
                                  <option value={status} key={status}>
                                    {status}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div className="col-7">
                              <input
                                type="date"
                                placeholder="Actual End Date"
                                className="form-control col-md-4 col-sm-12 mb-2"
                                onChange={handleWeekEndingDateChange}
                              />
                            </div>
                          </div>
                        </div>
                      </ListSubheader>
                    }
                  >
                    {filteredTasks.length > 0 ? (
                      filteredTasks.map((e: any, index: number) => (
                        <React.Fragment key={index}>
                          <ListItemButton>
                            <ListItemIcon>
                              <CircleIcon sx={{ fontSize: 10 }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={`${e.description} -${e.status}`}
                            />
                          </ListItemButton>
                          <Divider />
                        </React.Fragment>
                      ))
                    ) : (
                      <ListItemButton>
                        <ListItemIcon>
                          <ErrorOutlineIcon />
                        </ListItemIcon>
                        <ListItemText primary="No tasks with the selected status." />
                      </ListItemButton>
                    )}
                    {weekEndingDateFilteredTasks.length > 0 ? (
                      <>
                        <h3>Week Ending Date Filtered Tasks</h3>
                        {weekEndingDateFilteredTasks.map(
                          (e: any, index: number) => (
                            <React.Fragment key={index}>
                              <ListItemButton>
                                <ListItemIcon>
                                  <CircleIcon sx={{ fontSize: 10 }} />
                                </ListItemIcon>
                                <ListItemText
                                  primary={`${e.description} - ${e.status}- ${e.weekEndingDate}`}
                                />
                              </ListItemButton>
                              <Divider />
                            </React.Fragment>
                          )
                        )}
                      </>
                    ) : (
                      <ListItemButton>
                        <ListItemIcon>
                          <ErrorOutlineIcon />
                        </ListItemIcon>
                        <ListItemText primary="No tasks with the selected week ending date." />
                      </ListItemButton>
                    )}
                  </List>
              </div>
            </Grid>
            <Grid item xs={12} sm={12} md={6} lg={6}>
              <div
                className="item m-3 w-90 "
                style={{
                  height: "50vh",
                  borderTop: "3px solid rgb(5, 235, 185)",
                }}
              >
                <h5 className=" m-2" style={{ color: "#07eb1a" }}>
                  Project Report
                  <span
                    className="btn btn-primary reportcss"
                    onClick={ProjectReportClick}
                  >
                    <InfoIcon sx={{ mr: 1 }} />
                    View Report
                  </span>
                </h5>
                <div className="Container d-flex flex-column justify-content-center align-items-center mt-2 ">
                  <div className="scroll" style={{ height: "40vh" }}>
                    <div className="mb-3 border border-2 p-2 qudarantfontSize ">
                      Total Task
                      <span className="float-end" style={{fontWeight:'bold',color:'blueviolet'}} >
                        {(role == "Customer" ? data?.EmpTask?.data?.length : data?.tasks?.data?.length) || "0"}
                      </span>
                    </div>
                    <div className="mb-3 border border-2 p-2 qudarantfontSize">
                      In Progress
                      <span className="float-end" style={{fontWeight:'bold',color:'blueviolet'}} >
                        {data?.Totaltask?.data?.onGoing || "0"}
                      </span>
                    </div>

                    <div className="mb-3 border border-2 p-2 qudarantfontSize">
                      Ready For UAT
                      <span className="float-end" style={{fontWeight:'bold',color:'blueviolet'}} >
                        {data?.Totaltask?.data?.readyForUAT || "0"}
                      </span>
                    </div>
                    <div className="mb-3 border border-2 p-2 qudarantfontSize">
                      Completed Task
                      <span className="float-end" style={{fontWeight:'bold',color:'blueviolet'}} >
                        {data?.Totaltask?.data?.completed || "0"}
                      </span>
                    </div>
                    <div className="mb-3 border border-2 p-2 qudarantfontSize">
                      Assigned Task
                      <span className="float-end" style={{fontWeight:'bold',color:'blueviolet'}} >
                        {data?.Totaltask?.data?.assigned || "0"}
                      </span>
                    </div>
                    <div className="mb-3 border border-2 p-2 qudarantfontSize">
                      Un Assigned Task
                      <span className="float-end" style={{fontWeight:'bold',color:'blueviolet'}} >
                        {data?.Totaltask?.data?.unassigned || "0"}
                      </span>
                    </div>
                    <h6 className="p-2  d-flex justify-content-center"></h6>
                  </div>
                </div>
              </div>
            </Grid>
          </Grid>
        </div>
      </Grid>
      <Dialog open={open}>
        <DialogTitle>Assign Lead</DialogTitle>
        <DialogContent className="row d-flex justify-content-center">
          &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
          <div className="row d-flex justify-content-center">
            <FormControl className="m-2">
              <Autocomplete
                options={
                  Array.isArray(data?.teamname?.data)
                    ? data.teamname.data.map((e: any) => ({
                        label: e.username,
                        value: e.userId,
                      }))
                    : []
                }
                fullWidth
                onChange={(e: any, { value }: any) => {
                  setuserId(value);
                  return e;
                }}
                className="col-6 mb-2"
                renderInput={(params) => (
                  <TextField {...params} label="Employees" />
                )}
              />
            </FormControl>

            <div className="d-flex justify-content-end">
              <Button
                variant="contained"
                className="mx-2 mt-4"
                onClick={() => {
                  setopen(false);
                  AssignLead();
                }}
              >
                Assign
              </Button>
              <Button
                variant="contained"
                className="mx-2 mt-4"
                onClick={() => {
                  setopen(false);
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
          &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
        </DialogContent>
      </Dialog>
      <BackDrop open={isLoading} />
    </>
  );
};
