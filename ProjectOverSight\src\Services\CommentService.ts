import { ConvertTime } from "../Utilities/Utils";
import { Comment } from "../Models/Comments/Comment";
import { Attendence } from "../Models/Employee/Attendance";
import * as XLSX from 'xlsx';
import { Get } from "./Axios";

export function DownloadCommentList(data: any): void {
  const downloadData: Comment[] = [];
  data.forEach((e: any) => {
    let comment: Comment = {
      EmployeeTaskId: e.employeeTaskId || "-",
      Employee: e.employee || "-",
      Project: e.project || "-",
      Comment: e.comment || "-",
    };
    downloadData.push(comment);
  });
  const ws = XLSX.utils.json_to_sheet(downloadData);

  const columnWidths = [
    { wpx: 50 }, 
    { wpx: 100 },
    { wpx: 100 },   
    { wpx: 100 },     
  ];

  ws['!cols'] = columnWidths;

  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "CommentListtx");


  XLSX.writeFile(wb, "CommentList.xlsx");
}

export async function DownloadAttendanceList(data: any) {  
  const teamList: any = await Get("app/Team/GetTeamList");
  const downloadData: Attendence[] = [];

  data.forEach((e: any) => {
    const team = teamList?.data?.find((team:any) => team.id === e.teamId); 
    let attendance: Attendence = {
      DayId: e.dayId || "",
      EmployeeName: e.employeeName || "",
      TeamName:team ? team.name : "-",
      Department: e.department || "",
      Date: e.date.slice(0, 10) || "", 
      InTime: ConvertTime(e.inTime, "") || "",
      OutTime: ConvertTime(e.outTime, "") || "",
    };
    downloadData.push(attendance);
  });
  const ws = XLSX.utils.json_to_sheet(downloadData);

  const columnWidths = [
    { wpx: 50 }, 
    { wpx: 100 },
    { wpx: 100 },   
    { wpx: 100 },  
    { wpx: 70 },
    { wpx: 70 },    
  ];

  ws['!cols'] = columnWidths;

  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "AttendanceListtx");


  XLSX.writeFile(wb, "AttendanceListtx.xlsx");
}
