import { X } from "lucide-react";
import { Modal } from "react-bootstrap";

function ScrumData({ show, setShow, questions }: any) {
  const handleModalClose = () => {
    setShow(false);
  };

  function convertToAudioBlob(base64String: string) {
    const byteCharacters = atob(base64String);
    const byteNumbers = new Array(byteCharacters.length)
      .fill(0)
      .map((_, i) => byteCharacters.charCodeAt(i));
    const byteArray = new Uint8Array(byteNumbers);
    const audioBlob = new Blob([byteArray], { type: "audio/wav" });
    const audioUrl = URL.createObjectURL(audioBlob);
    return audioUrl;
  }
  return (
    <Modal
      show={show}
      onHide={handleModalClose}
      centered
      size="lg"
      className="mt-4"
    >
      <Modal.Header>
        <Modal.Title>Scrum Details</Modal.Title>
        <X onClick={handleModalClose} />
      </Modal.Header>
      <Modal.Body style={{ maxHeight: "60vh", overflowY: "auto" }}>
        {questions.map((item: any, index: number) => (
          <div key={index} className="mb-3">
            <label>
              {index + 1}. {item.qText} <b className="text-danger">*</b>
            </label>
            <div className="output-section">
              <div className="audio-player w-100 d-flex justify-content-center">
                <audio
                  controls
                  src={convertToAudioBlob(item.voiceFileBytes)}
                ></audio>
              </div>
              <textarea
                className="transcript-box"
                cols={90}
                rows={4}
                value={item.responseText}
                readOnly
              />
            </div>
          </div>
        ))}
      </Modal.Body>
      <Modal.Footer>
        <button className="btn btn-secondary" onClick={handleModalClose}>
          Close
        </button>
      </Modal.Footer>
    </Modal>
  );
}

export default ScrumData;
