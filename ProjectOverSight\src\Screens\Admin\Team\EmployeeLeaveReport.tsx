import Breadcrumbs from "@mui/material/Breadcrumbs";
import { Toolt<PERSON>, Typography } from "@mui/material";
import { Link } from "react-router-dom";
import { Get } from "../../../Services/Axios";
import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import Button from "@mui/material/Button";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import BackDrop from "../../../CommonComponents/BackDrop";
import DownloadIcon from "@mui/icons-material/Download";
import AddIcon from "@mui/icons-material/Add";
import { DownloadLeaveReport } from "../../../Services/TeamService";
import { Calendar } from "./Calendar";
import { useContextProvider } from "../../../CommonComponents/Context";
import { ADMIN } from "../../../Constants/Common/Roles";
import { Day } from "../../../Models/Team/Day";
import YEARS, { ConvertToISO } from "../../../Utilities/Utils";
import { X } from "lucide-react";
import { Check } from "lucide-react";
import AttendanceInfo from "./AttendanceInfo";
import StarRateIcon from "@mui/icons-material/StarRate";

const months = [
  { id: 1, name: "January" },
  { id: 2, name: "February" },
  { id: 3, name: "March" },
  { id: 4, name: "April" },
  { id: 5, name: "May" },
  { id: 6, name: "June" },
  { id: 7, name: "July" },
  { id: 8, name: "August" },
  { id: 9, name: "September" },
  { id: 10, name: "October" },
  { id: 11, name: "November" },
  { id: 12, name: "December" },
];

const CURRENT_YEAR = new Date().getFullYear();
const CURRENT_MONTH = new Date().getMonth() + 1;

export const EmployeeLeaveReport = () => {
  const location = useLocation();
  const [refetch, setRefetch] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const { role } = useContextProvider();
  const [dateFilter, setDateFilter] = useState<any>({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  });
  const [TeamView, setTeamView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });
  const [rows, setRows] = useState<any>([]);
  const [Days, setDays] = useState<any>([]);
  const [show, setShow] = useState(false);
  const [viewattendence, setviewattendence] = useState<any>([]);

  async function fetchData() {
    setLoading(true);
    try {
      const response: any = await Get(
        `app/Team/GetTeamleavedetails?TeamId=${location.state?.data?.id}&month=${dateFilter.month}&year=${dateFilter.year}`
      );
      if (response?.data) {
        setRows(response?.data?.teamLeaveDetails || []);
        setDays(response?.data?.days || []);
      } else {
        console.error("Error fetching team leave details:", response?.error);
      }
    } catch (error) {
      console.error("Error fetching team leave details:", error);
    }
    setLoading(false);
  }

  useEffect(() => {
    fetchData();
  }, [refetch]);

  const daysInMonth = new Date(dateFilter.year, dateFilter.month, 0).getDate();
  const dateLabels = Array.from({ length: daysInMonth }, (_, i) => {
    const currentDate = new Date(dateFilter.year, dateFilter.month - 1, i + 1);
    return currentDate.toLocaleDateString("en-US", {
      weekday: "short",
      year: "numeric",
      month: "short",
      day: "2-digit",
    });
  });

  const days: number[] = [];
  for (let i = 1; i <= dateLabels.length; i++) {
    days.push(i);
  }

  const handleClickOpen = () => {
    setTeamView({ edit: true });
  };

  function isDisabled(id: number): boolean {
    if (parseInt(dateFilter.year) === CURRENT_YEAR) {
      return id > CURRENT_MONTH;
    }
    return false;
  }

  function isSelected(id: number) {
    if (parseInt(dateFilter.year) === CURRENT_YEAR) {
      return id === CURRENT_MONTH;
    }
    return id === dateFilter.month;
  }

  function handleYearChange(year: string) {
    if (parseInt(year) === CURRENT_YEAR) {
      setDateFilter({ year: Number(year), month: CURRENT_MONTH });
    } else {
      setDateFilter({ ...dateFilter, year: Number(year) });
    }
    setRefetch(!refetch);
  }

  function calculateAbsent(totalDays: any, row: any): number {
    var present = 0;
    dateLabels.map((_, index: number) => {
      var date = row.employeeTimes.find(
        (x: any) => new Date(x.inTime).getDate() == index + 1
      );
      if (date) {
        var day = new Date(date.inTime).getDay();
        if (day !== 0 && day !== 6) present++;
      }
    });
    return (
      totalDays -
      present -
      Days?.filter(
        (x: Day) =>
          x.holidayApplicable === true &&
          x.date.slice(0, 10) <= ConvertToISO(new Date())
      ).length
    );
  }

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Link color="slateblue" to={`/${role}/Team`}>
          <Typography sx={{ fontWeight: "bold" }}>Team</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>
          Employee Leave Report
        </Typography>
      </Breadcrumbs>
      <Typography align="center" className="fs-3">
        Team Name:{" "}
        <span className="fw-bolder">{location.state?.data?.name}</span>
      </Typography>
      <h2 className="m-4">Employee Leave Report</h2>
      <div className="well mx-auto mt-4">
        <div className="container">
          <div className="row">
            <div className="d-flex mx-2">
              <select
                className="form-select m-2 col-md-5"
                style={{ width: "230px" }}
                defaultValue={dateFilter.year}
                onChange={(e: any) => {
                  handleYearChange(e.target.value);
                }}
              >
                <option selected disabled>
                  Select Year
                </option>
                {YEARS.map((year: number) => (
                  <option value={year} key={year}>
                    {year}
                  </option>
                ))}
              </select>
              <select
                className="form-select m-2 col-md-5"
                style={{ width: "230px" }}
                defaultValue={dateFilter.month}
                onChange={async (e: any) => {
                  setDateFilter({ ...dateFilter, month: e.target.value });
                  setRefetch(!refetch);
                }}
              >
                <option selected disabled>
                  Select Month
                </option>
                {months.map((month) => (
                  <option
                    key={month.id}
                    value={month.id}
                    selected={isSelected(month.id)}
                    disabled={isDisabled(month.id)}
                  >
                    {month.name}
                  </option>
                ))}
              </select>
              <div className="col-md-7">
                <div className="row justify-content-end">
                  <div className="col-auto">
                    <Button
                      variant="contained"
                      endIcon={<SearchIcon />}
                      className="mx-2 mt-4"
                    >
                      Search
                    </Button>
                    <Button
                      variant="contained"
                      endIcon={<RefreshIcon />}
                      className="mx-2 mt-4"
                    >
                      Reset
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="col-md-11">
        <div className="row justify-content-end">
          <div className="col-auto">
            <Button
              variant="contained"
              className="float-md-end mx-2"
              onClick={() => {
                DownloadLeaveReport(rows, dateLabels, Days);
              }}
            >
              Download
              <DownloadIcon className="mx-1" />
            </Button>
            {role === ADMIN && (
              <Button
                variant="contained"
                className=" float-md-end mx-2"
                onClick={handleClickOpen}
              >
                Add Holidays
                <AddIcon className="mx-1" />
              </Button>
            )}
          </div>
        </div>
      </div>
      <div className="well mx-auto mt-4">
        <div className="container mt-4" style={{ marginLeft: "15px" }}>
          <div style={{ maxHeight: "500px", overflowY: "auto" }}>
            <table className="table table-bordered ">
              <thead>
                <tr>
                  <th
                    style={{
                      position: "sticky",
                      top: 0,
                      background: "slateblue",
                      width: "50%",
                    }}
                  >
                    Employee Name
                  </th>
                  {dateLabels.map((label, index) => (
                    <th
                      key={index}
                      style={{
                        position: "sticky",
                        top: 0,
                        background: "slateblue",
                      }}
                    >
                      {label}
                    </th>
                  ))}
                  <th
                    style={{
                      position: "sticky",
                      top: 0,
                      background: "slateblue",
                      width: "30rem",
                    }}
                  >
                    Count of Absents
                  </th>
                </tr>
              </thead>
              <tbody>
                {rows &&
                  rows?.map((row: any) => {
                    var totalDays =
                      dateFilter.month === new Date().getMonth() + 1
                        ? new Date().getDate()
                        : days[days.length - 1];

                    return (
                      <tr key={row.employeeId}>
                        <td>{row.employeeName}</td>
                        {days?.map((day, index) => {
                          var date = row.employeeTimes.find(
                            (x: any) => new Date(x.inTime).getDate() === day
                          );
                          const isPastDate = day <= new Date().getDate();
                          return (
                            <td style={{ width: "5rem" }} key={index}>
                              {date?.inTime ? (
                                <Tooltip title="View">
                                  <Check
                                    style={{ color: "green" }}
                                    onClick={() => {
                                      setShow(true);
                                      setviewattendence(date);
                                    }}
                                  />
                                </Tooltip>
                              ) : !isPastDate &&
                                !Days[index]?.holidayApplicable ? (
                                "-"
                              ) : Days[index]?.holidayApplicable ? (
                                <Tooltip
                                  title={Days[index]?.holidayName ?? "-"}
                                  placement="top"
                                  className="fs-6"
                                  style={{
                                    transition: "font-size 0.3s",
                                  }}
                                  arrow
                                >
                                  <StarRateIcon />
                                </Tooltip>
                              ) : (
                                <Tooltip title="Leave">
                                  <X style={{ color: "red" }} />
                                </Tooltip>
                              )}
                            </td>
                          );
                        })}
                        <td>
                          {calculateAbsent(totalDays, row) < 0
                            ? 0
                            : calculateAbsent(totalDays, row)}
                        </td>
                      </tr>
                    );
                  })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <BackDrop open={loading} />
      <Calendar
        openDialog={TeamView}
        setReload={setRefetch}
        setOpenDialog={setTeamView}
        setRows={setRows}
      />
      <AttendanceInfo
        show={show}
        setShow={setShow}
        viewattendence={viewattendence}
      />
    </>
  );
};
