import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>alogContent,
    <PERSON>alogT<PERSON>le,
    TextField,
    Alert,
    MenuItem,
    DialogActions,
} from "@mui/material";
import * as React from 'react';
import { useForm } from "react-hook-form";
import { useEffect, useState, useRef } from "react";
import Swal from "sweetalert2";
import "./../../../App.css";
import { Get, Post } from "../../../Services/Axios";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import dayjs from 'dayjs';
import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { TransitionProps } from '@mui/material/transitions';
import Slide from '@mui/material/Slide';
import { TimePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
const formField = [
    "UserName",
    "email",
    "skills",
    "phoneNumber",
    "secondaryPhoneNumber",
    "secondaryEmail",
    "department",
    "category",
    "Id",
    "Role",
    "officialIntime",
    "officialOuttime,"
];

type DepartmentTimeDetails = {
    Id?: number;
    CommonMasterId?: number;
    StartTime?: string | null;
    EndTime?: string | null;

};

const Transition = React.forwardRef(function Transition(
    props: TransitionProps & {
        children: React.ReactElement<any, any>;
    },
    ref: React.Ref<unknown>,
) {
    return <Slide direction="up" ref={ref} {...props} />;
});

export const EditDepartmentTime = ({
    editDepartment,
    setEditDepartment,
    data,
    setReload,
    setRefetch,
}: any) => {


    const { register, handleSubmit, resetField } = useForm();
    const [save, setSave] = useState<boolean>(false);
    const subCategoryRef = useRef<any>(null);
    const startTimeRef = useRef<HTMLInputElement>(null);
    const endTimeRef = useRef<HTMLInputElement>(null);
    const [modelError, setModelError] = useState<any>({
        Department: false,
        Category: false,
    });
    const [userDto, setUserDto] = useState<any>({
        Department: "",
        Category: "",
    });
    const [categories, setcategories] = useState<any>([]);
    const [subCategories, setSubCategories] = useState<any>([]);
    const [errorMsg, setErrorMsg] = useState<any>({
        message: "",
        show: false,
    });
    function reset() {
        formField.map((e: string) => {
            resetField(e);
        });
    }

    var Category: string[] = [];
    var categorySet = new Set<any>();

    useEffect(() => {
        const categoriesList = Get("app/CommonMaster/GetCodeTableList");
        categoriesList.then((response: any) => {
            var category = response?.data?.filter(
                (x: any) => x.codeType === "EmployeeCategory"
            );
            setcategories(category || []);
        });

        const subCategories = categories.filter(
            (element: any) => element.codeName === data?.categories[0]?.codeName
        );
        setSubCategories(subCategories);

    }, [data]);



    categories?.forEach((element: any) => {
        categorySet.add(element.codeName);
    });

    Category = [...categorySet];

    const handleCategoryChange = (event: any) => {
        if (subCategoryRef.current) subCategoryRef.current.value = "";
        const subCategories = categories.filter(
            (element: any) => element.codeName === event.target.value
        );
        setSubCategories(subCategories);
    };


    const onSubmitHandler = async (Data: any) => {


        setSave(true);
        const subCategories = categories.filter(
            (element: any) => element.codeValue === Data.subCategory
        );

        let commonMasterId = null;

        if (subCategories.length > 0) {
            commonMasterId = subCategories[0].id;
        }


        const startTimeValue = startTimeRef.current?.value;
        const endTimeValue = endTimeRef.current?.value;
        const DepartmentTimeDetail: DepartmentTimeDetails = {
            Id: data.id,
            CommonMasterId: commonMasterId,
            StartTime: startTimeValue,
            EndTime: endTimeValue,
        };
        const { error }: any = await Post("app/DepartmentTime/UpdateDepartmentTime", DepartmentTimeDetail);
        var option: AlertOption;

        if (error) {
            option = {
                title: "Error",
                text: "Data Not Updated!",
                icon: "error",
            };
        } else {
            option = {
                title: "Success",
                text: "Employee Updated Successfully!",
                icon: "success",
            };
        }
        Swal.fire({
            ...option,
            confirmButtonColor: "#3085d6",
        }).then(() => {
            setReload((prev: boolean) => !prev);
            setRefetch((prev: boolean) => !prev);
        });
        handleClose();
    };
    const handleClose = () => {
        setErrorMsg({
            message: "",
            show: false,
        });
        setSave(false);
        setEditDepartment({ edit: false });
        reset();
    };


    return (
        <div>
            <Dialog open={editDepartment?.edit || false} TransitionComponent={Transition}>
                <form onSubmit={handleSubmit(onSubmitHandler)}>
                    <div
                        style={{
                            backgroundColor: "#f0f0f0",
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                        }}
                    >
                        <DialogTitle style={{ color: "blue", flex: "1" }}>
                            Edit Department-Time{" "}
                            <span style={{ color: "blue" }}>{" - " + data?.categories[0]?.codeName}</span>
                        </DialogTitle>
                        <CancelOutlinedIcon
                            onClick={handleClose}
                            sx={{
                                color: "red",
                                fontSize: "30px",
                                marginRight: "10px",
                                cursor: "pointer",
                            }}
                        />
                    </div>
                    <DialogContent className="row popup">
                        {errorMsg.show && (
                            <Alert severity="error" className="mb-3">
                                {errorMsg.message}. <strong>check it out!</strong>
                            </Alert>
                        )}
                        <div className='d-flex' style={{ justifyContent: 'center' }}>
                            <TextField
                                id="standard-select-currency"
                                className="col mx-2 m-2"
                                select
                                label="Category"
                                defaultValue={data?.categories[0]?.codeName}
                                variant="outlined"
                                {...register('Category')}
                                onChange={(e: any) => {
                                    handleCategoryChange(e);
                                    setModelError({ ...modelError, Category: false });
                                    setUserDto({ ...userDto, Category: e.target.value });
                                }}
                            >
                                {Category?.map((option: any) => (
                                    <MenuItem key={option} value={option}>
                                        {option}
                                    </MenuItem>
                                ))}
                            </TextField>
                            <TextField
                                id="standard-select-currency"
                                className="col mx-2 m-2"
                                select
                                label="Department"
                                variant="outlined"
                                defaultValue={data?.categories[0]?.codeValue || ''}
                                {...register('subCategory')}
                                onChange={(selectedOption: any) => {
                                    return {
                                        subCategory: selectedOption.target.value,
                                    };
                                }}
                            >
                                {subCategories?.length > 0 ? (
                                    subCategories.map((option: any) => (
                                        <MenuItem key={option.id} value={option.codeValue}>
                                            {option.codeValue}
                                        </MenuItem>
                                    ))
                                ) : (
                                    <MenuItem value={data?.categories[0]?.codeValue}>
                                        {data?.categories[0]?.codeValue}
                                    </MenuItem>
                                )}
                            </TextField>



                        </div>
                        <div className='d-flex' style={{ justifyContent: 'center' }}>
                            <div className='col m-2 '>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DemoContainer components={['TimePicker', 'TimePicker']}>
                                        <TimePicker
                                            className='w-100'
                                            label="Start Time"
                                            inputRef={startTimeRef}
                                            defaultValue={dayjs(data?.startTime)}
                                        />
                                    </DemoContainer>
                                </LocalizationProvider>
                            </div>
                            <div className='col m-2'>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DemoContainer components={['TimePicker', 'TimePicker']}>
                                        <TimePicker
                                            className='w-100'
                                            label="End Time"
                                            inputRef={endTimeRef}
                                            defaultValue={dayjs(data?.endTime)}
                                        />
                                    </DemoContainer>
                                </LocalizationProvider>
                            </div>
                        </div>
                        <div className="row">
                            <input {...register("CreatedBy")} value="user" hidden />
                            <input {...register("UpdatedBy")} value="user" hidden />
                            {/* <input {...register("Id")} value={Data?.projectId} hidden /> */}
                        </div>
                    </DialogContent>
                    <DialogActions>
                        <Button
                            onClick={handleClose}
                            size="medium"
                            variant="contained"
                            color="error"
                        >
                            Cancel
                        </Button>
                        <Button
                            size="medium"
                            variant="contained"
                            color="success"
                            disabled={save}
                            type="submit"
                        >
                            {save ? "Saving..." : "Save"}
                        </Button>
                    </DialogActions>
                </form>
            </Dialog>
        </div>
    );
};
