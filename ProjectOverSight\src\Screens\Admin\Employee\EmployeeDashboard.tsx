import { Breadcrumbs, Typography } from "@mui/material";
import { Link } from "react-router-dom";
import { useContextProvider } from "../../../CommonComponents/Context";

export const EmployeeDashboard = () => {
  const { role } = useContextProvider();
  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Employee Dashboard</Typography>
      </Breadcrumbs>
      <div className="container">
        <div>
          <div
            className="border border-end-1 border-dark m-2"
            style={{ width: "40rem", height: "12rem" }}
          ></div>
          <div
            className="border border-end-1 border-dark m-2"
            style={{ width: "40rem", height: "12rem" }}
          ></div>
          <div
            className="border border-end-1 border-dark m-2"
            style={{ width: "40rem", height: "12rem" }}
          ></div>
        </div>
        <div>
          <div
            className="border border-end-1 border-dark m-2"
            style={{ width: "50rem", height: "18.2rem" }}
          ></div>
          <div
            className="border border-end-1 border-dark m-2"
            style={{ width: "50rem", height: "18.2rem" }}
          ></div>
        </div>
      </div>
    </>
  );
};
