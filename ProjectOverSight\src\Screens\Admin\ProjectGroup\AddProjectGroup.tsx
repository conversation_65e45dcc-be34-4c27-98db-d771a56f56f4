import { PostFiles } from "../../../Services/Axios";
import { useForm } from "react-hook-form";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import Swal from "sweetalert2";
import { Button, Form, Modal } from "react-bootstrap";
import { Divider } from "@mui/material";

const formField = ["name", "description", "createdBy", "updatedBy", "file"];

export const AddProjectGroup = ({ isOpen, onClose, setReload }: any) => {
  const { register, handleSubmit, resetField } = useForm();

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const onSubmitHandler = async (data: any) => {
    data.file = data.file[0];
    const { error }: any = await PostFiles("app/Project/AddProjectGroup", data);
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Project Added Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal show={isOpen} onHide={() => handleClose()} centered>
      <Form onSubmit={handleSubmit(onSubmitHandler)}>
        <Modal.Header closeButton>
          <Modal.Title>Create Group</Modal.Title>
        </Modal.Header>
        <Divider />
        <Modal.Body>
          <Form.Group className="mb-3" controlId="exampleForm.ControlInput1">
            <Form.Label>Name*</Form.Label>
            <Form.Control
              type="text"
              placeholder="Name"
              required
              {...register("name")}
            />
          </Form.Group>
          <Form.Group className="mb-3" controlId="exampleForm.ControlTextarea1">
            <Form.Label>Description*</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              placeholder="Description"
              required
              {...register("description")}
            />
          </Form.Group>
          <Form.Group className="mb-3" controlId="exampleForm.ControlTextarea1">
            <Form.Label>Choose Image</Form.Label>
            <Form.Control type="file" accept="image/*" {...register("file")} />
          </Form.Group>
          <Form.Control {...register("createdBy")} value="user" hidden />
          <Form.Control {...register("updatedBy")} value="user" hidden />
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => handleClose()}>
            Close
          </Button>
          <Button variant="primary" type="submit">
            Save
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default AddProjectGroup;
//  <Dialog open={isOpen}>
//       <form onSubmit={handleSubmit(onSubmitHandler)}>
//         <div
//           style={{
//             backgroundColor: "#f0f0f0",
//             display: "flex",
//             justifyContent: "space-between",
//             alignItems: "center",
//           }}
//         >
//           <DialogTitle className="mx-2 d-flex justify-content-center">
//             <h4>Create Group</h4>
//           </DialogTitle>
//           <CancelOutlinedIcon
//             onClick={onClose}
//             sx={{
//               color: "red",
//               fontSize: "30px",
//               marginRight: "10px",
//               cursor: "pointer",
//             }}
//           />
//         </div>
//         <DialogContent className="row popup">
//           {errorMsg.show && (
//             <Alert severity="error" className="mb-3">
//               {errorMsg.message}. <strong>check it out!</strong>
//             </Alert>
//           )}
//           <div className="row mx-auto">
//             <label htmlFor="groupName">Group Name:</label>
//             <TextField
//               type="text"
//               id="groupName"
//               placeholder="Enter group name"
//               {...register("name")}
//               className="col m-2"
//             />
//           </div>

//           <div className="row mx-auto">
//             <label htmlFor="groupDescription">Group Description:</label>
//             <TextareaAutosize
//               id="groupDescription"
//               placeholder="Enter group description"
//               {...register("description")}
//               className="col m-2 mb-3 form-control"
//               style={{ height: 80 }}
//             />
//           </div>

//           <div className="row mx-auto">
//             <div className="col">
//               <label htmlFor="groupIcon">Upload Image:</label>
//               <input
//                 type="file"
//                 id="groupIcon"
//                 accept="image/*"
//                 {...register("file")}
//               />
//             </div>
//             <input {...register("createdBy")} value="user" hidden />
//             <input {...register("updatedBy")} value="user" hidden />
//             <div className="col"></div>
//           </div>
//         </DialogContent>
//         <div className="mx-3 my-4">
//           <Button
//             type="submit"
//             size="medium"
//             variant="contained"
//             color="success"
//             className="mx-3"
//           >
//             {save ? "Saving..." : "Save"}
//           </Button>
//         </div>
//       </form>
//     </Dialog>
