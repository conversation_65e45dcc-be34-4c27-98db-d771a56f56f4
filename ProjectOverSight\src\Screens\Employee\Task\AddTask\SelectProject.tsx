import { useEffect, useState } from "react";
import { Project } from "../../../../Models/Project/Project";
import { Get } from "../../../../Services/Axios";
import { FormControl, TextField } from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import { Dispatch, SetStateAction } from "react";
import { TaskDTO } from "../../../../Models/Task/Task";

type SelectProjectProp = {
  setTask: Dispatch<SetStateAction<TaskDTO>>;
};

export const SelectProject = ({ setTask }: SelectProjectProp) => {
  const [projects, setProjects] = useState<Project[]>([]);

  async function fetchData() {
    const response: any = await Get("/app/Project/GetEmployeeProjectlist");
    setProjects(response.data || []);
  }

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="row w-50 mx-auto mt-4 h-50 d-flex align-items-center">
      <FormControl className="col m-2">
        <Autocomplete
          disablePortal
          options={projects
            .filter((x) => x.isActive === true)
            .map((project: Project) => ({
              label: project.name,
              value: project.id,
            }))}
          onChange={(e: any, value: any) => {
            setTask((prev: TaskDTO) => {
              return { ...prev, projectId: value?.value ?? 0 };
            });
            return e;
          }}
          renderInput={(params) => (
            <TextField {...params} label="Project Name" required />
          )}
        />
      </FormControl>
    </div>
  );
};
