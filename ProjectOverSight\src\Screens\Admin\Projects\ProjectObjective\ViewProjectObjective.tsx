import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  TextareaAutosize,
  Button,
  FormControl,
  Select,
  InputLabel,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

export const ViewProjectObjective = ({
  openDialog,
  setOpenDialog,
  Data,
}: any) => {
  const handleClose = () => {
    setOpenDialog({ view: false });
  };
  return (
    <div className="w-50">
      <Dialog open={openDialog?.view} onClose={handleClose}>
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle sx={{ color: "blue", fontWeight: "bold" }}>
              Project Objective
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent
            className="row popup d-flex justify-content-center"
            sx={{
              width: 590,
            }}
          >
            <div className="row">
              <TextareaAutosize
                readOnly
                className="col m-2 form-control"
                placeholder="Description"
                style={{ height: 100 }}
                value={Data?.description}
              />
            </div>
            <div className="row">
              <FormControl className="col m-2">
                <InputLabel id="project-type">Status</InputLabel>
                <Select
                  labelId="Status"
                  readOnly
                  className="disabled"
                  id="Status"
                  label="Status"
                  value={Data?.status}
                >
                  <MenuItem value="Completed">Completed</MenuItem>
                  <MenuItem value="In Progress">In Progres</MenuItem>
                </Select>
              </FormControl>
            </div>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="contained">
              ok
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
