import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import Checkbox from '@mui/material/Checkbox';
import ListItemText from '@mui/material/ListItemText';

const style = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 600,
  maxHeight: '80vh',
  bgcolor: 'background.paper',
  border: 'none',
  boxShadow: 24,
  p: 4,
  borderRadius: '8px',
};

type Plan = {
  id: number;
  label: string;
  notes: string;
};

type BasicModalProps = {
  open: boolean;
  handleClose: () => void;
  options: Plan[];
  onSelect: (selected: Plan[]) => void;
  selectedOptions: Plan[];
};

export default function MultiModal({ open, handleClose, options, onSelect, selectedOptions }: BasicModalProps) {
  const [localSelectedOptions, setLocalSelectedOptions] = React.useState<Plan[]>([]);

  React.useEffect(() => {
    setLocalSelectedOptions(selectedOptions);
  }, [selectedOptions]);

  const handleToggle = (option: Plan) => () => {
    const currentIndex = localSelectedOptions.findIndex((selected) => selected.id === option.id);
    const newSelectedOptions = [...localSelectedOptions];

    if (currentIndex === -1) {
      newSelectedOptions.push(option);
    } else {
      newSelectedOptions.splice(currentIndex, 1);
    }

    setLocalSelectedOptions(newSelectedOptions);
  };

  const handleSave = () => {
    onSelect(localSelectedOptions);
    handleClose();
  };

  return (
    <Modal open={open} onClose={handleClose} aria-labelledby="modal-title" aria-describedby="modal-description">
      <Box sx={style}>
        <Typography id="modal-title" variant="h6" component="h2">
          Select Weekly Plans
        </Typography>
        <List sx={{ maxHeight: '60vh', overflow: 'auto' }}>
          {options.map((option) => (
            <ListItem key={option.id} button onClick={handleToggle(option)}>
              <ListItemIcon>
                <Checkbox
                  edge="start"
                  checked={localSelectedOptions.some((selected) => selected.id === option.id)}
                  tabIndex={-1}
                  disableRipple
                />
              </ListItemIcon>
              <ListItemText primary={option.notes} />
            </ListItem>
          ))}
        </List>
        <Box display="flex" justifyContent="flex-end" mt={2}>
          <Button onClick={handleClose} variant="outlined" sx={{ mr: 2 }}>Cancel</Button>
          <Button onClick={handleSave} variant="contained" color="primary">Save</Button>
        </Box>
      </Box>
    </Modal>
  );
}
