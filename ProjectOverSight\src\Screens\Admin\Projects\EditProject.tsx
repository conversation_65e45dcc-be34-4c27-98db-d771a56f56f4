import {
  Alert,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Checkbox,
  ListItemText,
  OutlinedInput,
  TextareaAutosize,
  Grid,
} from "@mui/material";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import Select from "@mui/material/Select";
import Swal from "sweetalert2";
import { Get, Post } from "../../../Services/Axios";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../Models/Common/AlertOptions";

const formField = [
  "Name",
  "Type",
  "Description",
  "StartDate",
  "EndDate",
  "Status",
  "Percentage",
  "CreatedBy",
  "UpdatedBy",
  "TechStackId",
  "id",
  "TeamId",
];

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

export const EditProject = ({
  openDialog,
  setOpenDialog,
  Data,
  setRows,
  setfilterRows,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [techStack, settechStack] = useState<any>([]);
  const [TeamName,setTeamName] = useState<any>([]);
  const [commonMaster, setCommonMaster] = useState<any>([]);
  const [save, setSave] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });

  async function fetchCommonMaster3() {
    const commonMaster: any = await Get("app/CommonMaster/GetCodeTableList");
    const teamName: any = await Get("app/Team/GetTeamList");
    setCommonMaster(commonMaster.data);
    setTeamName(teamName?.data)
  }

  useEffect(() => {
    fetchCommonMaster3();
    let temp: any = [];
    Data?.projectTechStacks?.map((e: any) => {
      temp.push(e.techStack);
    });
    settechStack(temp);
  }, [Data]);

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const onSubmitHandler = async (data: any) => {
    setSave(true);
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }
    data.isActive = data.isActive === "Yes";
    data.TechStackId = techStack;
    const { error }: any = await Post(`app/Project/UpdateProject`, data);
    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Project Updated Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      let employeeList = Get("app/Project/GetProjectList");
      employeeList.then((response: any) => {       
        const allProjects = response?.data || [];        
      const activeProject = allProjects.filter((Project: any) => Project.isActive);    
      const inactiveProject = allProjects.filter((Project: any) => !Project.isActive);
      activeProject.sort((a: any, b: any) => a.name.localeCompare(b.name));
      inactiveProject.sort((a: any, b: any) => (a.name > b.name ? 1 : -1));
      const mergedProjects = [...activeProject, ...inactiveProject];
      setRows(mergedProjects || []);
      setfilterRows(mergedProjects || []); 
      });
    });
    handleClose();
  };

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setOpenDialog({ edit: false });
    setSave(false);
  };

  return (
    <div>
      <Dialog open={openDialog?.edit || false}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Edit Project{" "}
              <span style={{ color: "blue" }}>{" - " + Data?.name}</span>
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                defaultValue={Data?.name}
                {...register("Name")}
                label="Project Name"
                type="text"
                variant="outlined"
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <InputLabel id="project-type">Project Type</InputLabel>
                <Select
                  labelId="project-type"
                  required
                  defaultValue={Data?.type}
                  id="project-type"
                  label="Project Type"
                  {...register("Type")}
                >
                  <MenuItem value="Web App">Web App</MenuItem>
                  <MenuItem value="Mobile App">Mobile App</MenuItem>
                  <MenuItem value="Mob App & Web App">
                    Web and Mobile App
                  </MenuItem>
                  <MenuItem value="Staffing">Staffing</MenuItem>
                  <MenuItem value="Infrastructure">Infrastructure</MenuItem>
                  <MenuItem value="Enrollment">Enrollment</MenuItem>
                  <MenuItem value="Others">Others</MenuItem>
                </Select>
                </FormControl>
            </Grid>
              <Grid item xs={12} >
              <InputLabel>Description</InputLabel>
            <FormControl fullWidth>
              <TextareaAutosize
                required
                placeholder="Description"
                {...register("Description")}
                defaultValue={Data?.description}
                style={{ height: 100 }}
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Start date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  required
                  id="start-date"
                  defaultValue={Data?.startDate?.slice(0, 10)}
                  {...register("StartDate")}
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">End date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  required
                  id="end-date"
                  {...register("EndDate")}
                  defaultValue={Data?.endDate?.slice(0, 10)}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                  <InputLabel id="project-type">Tech Stack</InputLabel>
                  <Select
                    labelId="Tech-Stack"
                    id="Tech-Stack"
                    multiple
                    {...register("TechStackId", {
                      onChange: (e: any) => {
                        settechStack(e.target.value);
                      },
                    })}
                    value={techStack}
                    input={<OutlinedInput label="Tech Stack" />}
                    renderValue={(selected: any) => (
                      <div>
                        {selected.map((value: number) => {
                          debugger
                          console.log(selected);
                          
                          return(
                          <span key={value} style={{ marginRight: 8 }}>
                            {
                              commonMaster?.find(
                                (option: any) => option.id === value
                              )?.codeName
                            }
                            ,
                          </span>
                        )})}
                      </div>
                    )}
                    MenuProps={MenuProps}
                  >
                    {commonMaster?.length > 0 &&
                      commonMaster?.map((e: any) => {
                        if (e.codeType == "ProjectTechStackCatagory")
                          return (
                            <MenuItem value={e.id} key={e.codeValue}>
                              <Checkbox
                                checked={techStack.indexOf(e.id as never) > -1}
                              />
                              <ListItemText primary={e.codeName} />
                            </MenuItem>
                          );
                      })}
                  </Select>
                  </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                  <InputLabel id="project-type">Status</InputLabel>
                  <Select
                    labelId="Status"
                    defaultValue={Data?.status}
                    required
                    id="Status"
                    label="Status"
                    {...register("Status")}
                  >
                    <MenuItem value="Completed">Completed</MenuItem>
                    <MenuItem value="In Progress">In Progres</MenuItem>
                  </Select>
                </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                  <InputLabel id="team">Team List</InputLabel>
                  <Select
                    labelId="team"
                    id="team"
                    required
                    defaultValue={Data?.teamId}
                    label="Team List"
                    {...register("TeamId")}
                  >
                    {TeamName?.filter((x: any) => x.isActive === true)
                      .map((e: any) => (
                        <MenuItem value={e.id} key={e.id}>
                          {e.name}
                        </MenuItem>
                      ))}
                  </Select>
                  </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                  <InputLabel id="active">Is Active</InputLabel>
                  <Select
                    labelId="active"
                    id="isActive"
                    required
                    defaultValue={Data?.isActive ? "Yes" : "No"}
                    label="Team List"
                    {...register("isActive")}
                  >
                    <MenuItem value={"Yes"}>Yes</MenuItem>
                    <MenuItem value={"No"}>No</MenuItem>
                  </Select>
                  </FormControl>
            </Grid>
              <input {...register("CreatedBy")} value="user" hidden />
              <input {...register("UpdatedBy")} value="user" hidden />
              <input {...register("Percentage")} value="0" hidden />
              <input {...register("id")} value={Data?.id} hidden />
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              color="success"
              disabled={save}
              type="submit"
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
