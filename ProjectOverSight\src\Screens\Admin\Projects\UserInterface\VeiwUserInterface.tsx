import {
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Grid,
  TextareaAutosize,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { Get } from "../../../../Services/Axios";
import { useEffect, useState } from "react";

export const ViewUserInterface = ({
  openDialog,
  setOpenDialog,
  projectId,
  Data,
  data1,
}: any) => {
  const [Eachprojectobjectives, setEachprojectobjectives] = useState<any>([]);
  const handleClose = () => {
    setOpenDialog({ view: false });
  };

  useEffect(() =>{
    getobjectives();
  })

  async function getobjectives() {
    try {
      // Fetch project objectives
      const objectiveResponse: any = await Get(
        `app/Project/GetProjectObjective?ProjectId=${projectId}`
      );
  
      // Extract project objectives or set to an empty array if not found
      const objectiveData = objectiveResponse?.data?.projectObjectives || [];
  
      // Extract and flatten project objective IDs
      const ObjId = Data?.projectObjectiveId
        ? (Array.isArray(Data.projectObjectiveId)
            ? Data.projectObjectiveId
            : Data.projectObjectiveId.split("|"))
        : [];
  
      // Create an array of filtered objectives based on ObjId
      const eachObjectiveResults = ObjId.map((element: any) =>
        objectiveData.filter((objective: any) => objective?.id === element)
      ) || [] ;
  
      // Flatten the resulting array of arrays (if needed) and set state
      setEachprojectobjectives(eachObjectiveResults.flat() || []);
    } catch (error) {
      console.log(Eachprojectobjectives);
      console.error("Error fetching project objectives:", error);
    }
  }

  var objective: any[] = [];
  data1?.data?.projectObjectiveMappings?.forEach((e: any) => {
    objective.push({ label: e.description, id: e.projectObjectiveId });
  });

  return (
    <div>
      <Dialog open={openDialog?.view}>
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              User Interface
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    value={Data?.name}
                    disabled={Data?.name ? false : true}
                    className="read-only-input"
                    label="User Interface Name"
                    type="text"
                    variant="outlined"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    id="outlined-read-only-input"
                    label="Status"
                    className="read-only-input"
                    disabled={Data?.status ? false : true}
                    defaultValue={Data?.status}
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <TextareaAutosize
                    className="read-only-input form-control"
                    value={Data?.description}
                    disabled={Data?.description ? false : true}
                    style={{ height: 100 }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Start date</InputLabel>
                <FormControl fullWidth>
                  <TextField
                    disabled={Data?.startDate ? false : true}
                    id="start-date"
                    className="read-only-input"
                    value={Data?.startDate?.slice(0, 10)}
                    margin="dense"
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">End date</InputLabel>
                <FormControl fullWidth>
                  <TextField
                    disabled={Data?.endDate ? false : true}
                    id="end-date"
                    className="read-only-input"
                    value={Data?.endDate?.slice(0, 10)}
                    margin="dense"
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    disabled={Data?.complexity ? false : true}
                    id="Complexity"
                    className="read-only-input"
                    value={Data?.complexity?.slice(0, 10)}
                    margin="dense"
                    label="Complexity"
                    fullWidth
                    variant="outlined"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    label="UI-Category"
                    className="read-only-input"
                    value={Data?.uiCategory}
                    margin="dense"
                    disabled={Data?.uiCategory ? false : true}
                    type="text"
                    fullWidth
                    variant="outlined"
                  />
                </FormControl>
              </Grid>
              {/* <Grid item xs={12} md={6}>
                {Data?.projectObjectiveId ? (
                  <InputLabel id="ProjectObjectiv">
                    Project Objectives
                  </InputLabel>
                ) : (
                  <></>
                )} */}
              {/* <FormControl fullWidth>
                  <Autocomplete
                    multiple
                    disabled={Data?.projectObjectiveId ? false : true}
                    readOnly
                    defaultValue={Data?.projectObjectiveId}
                    options={objective}
                    sx={{ maxHeight: "150px", overflowY: "auto" }}
                    renderInput={(params: any) => (
                      <TextField
                        {...params}
                        className="read-only-input"
                        label={
                          Data?.projectObjectiveId ? "" : "Select Objectives"
                        }
                        variant="outlined"
                      />
                    )}
                  />
                </FormControl> */}
              {/* </Grid> */}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="contained">
              ok
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
