import {
    <PERSON>ert,
    Dialog,
    <PERSON>alogA<PERSON>,
    DialogContent,
    DialogTitle,
    Button,
    TextareaAutosize,
  } from "@mui/material";
  import { useForm } from "react-hook-form";
  import { useState } from "react";
  import Swal from "sweetalert2";
  import { Post } from "../../../Services/Axios";
  import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
  import { AlertOption } from "../../../Models/Common/AlertOptions";
  
  const formField = ["Description", "ProjectId", "Status", "Percentage"];
  
  export const AddRequirementType = ({
    openDialog,
    setOpenDialog,
    SetReload,
    ProjectId,
  }: any) => {
    const { register, handleSubmit, resetField } = useForm();
    const [save, setSave] = useState<boolean>(false);
    const [errorMsg, setErrorMsg] = useState<any>({
      message: "",
      show: false,
    });
  
    function reset() {
      formField.map((e: string) => {
        resetField(e);
      });
    }
  
    const onSubmitHandler = async (data: any) => {
    
      setSave(true);
      const { error }: any = await Post("app/Requirement/AddRequirementType", data);
      var option: AlertOption;
      if (error) {
        option = {
          title: "Error",
          text: "Error Occured While Saving!",
          icon: "error",
        };
        reset();
      } else {
        option = {
          title: "Success",
          text: "Requirement Types Added Successfully!",
          icon: "success",
        };
        reset();
      }
  
      Swal.fire({
        ...option,
        confirmButtonColor: "#3085d6",
      }).then(() => {
        SetReload((prev: boolean) => !prev);
      });
      handleClose();
    };
  
    const handleClose = () => {
      reset();
      setOpenDialog({ add: false });
      setErrorMsg({
        message: "",
        show: false,
      });
      setSave(false);
    };
  
    return (
      <div className="w-50">
        <Dialog open={openDialog?.add} onClose={handleClose}>
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <div
              style={{
                backgroundColor: "#f0f0f0",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <DialogTitle style={{ color: "blue", flex: "1" }}>
                Add Requirement Type
              </DialogTitle>
              <CancelOutlinedIcon
                onClick={handleClose}
                sx={{
                  color: "red",
                  fontSize: "30px",
                  marginRight: "10px",
                  cursor: "pointer",
                }}
              />
            </div>
            <DialogContent
              className="row popup d-flex justify-content-center"
              sx={{
                width: 590,
              }}
            >
              {errorMsg.show && (
                <Alert severity="error" className="mb-3">
                  {errorMsg.message}. <strong>check it out!</strong>
                </Alert>
              )}
              <div className="row col-md-8">
                <TextareaAutosize
                  required
                  className="col m-2 form-control"
                  placeholder="Requirement Type"
                  {...register("requirementsType")}
                  style={{ height: 100 }}
                />
              </div>
              <input {...register("createdBy")} value="user" hidden />
              <input {...register("updatedBy")} value="user" hidden />
              <input {...register("projectId")} value={ProjectId} hidden />
            </DialogContent>
            <DialogActions>
              <Button onClick={handleClose} variant="contained" color="error">
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={save}
                variant="contained"
                color="success"
              >
                {save ? "Saving..." : "Save"}
              </Button>
            </DialogActions>
          </form>
        </Dialog>
      </div>
    );
  };
  