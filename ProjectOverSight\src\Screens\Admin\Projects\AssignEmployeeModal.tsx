import Multiselect from "multiselect-react-dropdown";
import { Employee } from "../../../Models/Employee/Employee";
import { Dispatch, SetStateAction, useState } from "react";
import { AssignmentMapper } from "../../../Models/Common/AssignmentMapper";
import { EmployeeProject } from "../../../Models/Employee/EmployeeProject";
import { Roles } from "../../../Constants/Common/Roles";
import Swal from "sweetalert2";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { Post } from "../../../Services/Axios";

type AssignEmployeeModalProps = {
  showModal: boolean;
  projectId: number;
  setShowModal: (prev: boolean) => void;
  lefts: EmployeeProject[];
  rights: EmployeeProject[];
  setRight: Dispatch<SetStateAction<EmployeeProject[]>>;
  setReload: Dispatch<SetStateAction<boolean>>;
};

export const AssignEmployeeModal = ({
  showModal,
  projectId,
  setShowModal,
  lefts,
  rights,
  setRight,
  setReload,
}: AssignEmployeeModalProps) => {
  const [assignmentMapper, setAssignmentMapper] = useState<AssignmentMapper>({
    assignedList: [],
    unassignedList: [],
  });
  var assignedList: EmployeeProject[] = [];
  var unassignedList: EmployeeProject[] = [];

  function toEmployeeProject(
    employee: Employee,
    projectId: number
  ): EmployeeProject {
    return {
      employeeId: employee.id,
      projectId: projectId,
      CreatedBy: Roles.ADMIN,
      UpdatedBy: Roles.ADMIN,
    };
  }

  function handleSelect(employees: Employee[], employee: Employee) {
    unassignedList = assignmentMapper.unassignedList.filter(
      (x) => x.employeeId !== employee.id
    );
    employees.map((employeeProject: Employee) => {
      assignedList.push(toEmployeeProject(employeeProject, projectId));
    });
    setAssignmentMapper({ assignedList, unassignedList });
  }

  function handleRemove(employees: Employee[], employee: Employee) {
    unassignedList = [...assignmentMapper.unassignedList];

    var deleteItem = unassignedList.find((x) => x.employeeId === employee.id);
    if (!deleteItem)
      unassignedList.push(toEmployeeProject(employee, projectId));

    employees.map((employeeProject: Employee) => {
      assignedList.push(toEmployeeProject(employeeProject, projectId));
    });
    setAssignmentMapper({ assignedList, unassignedList });
  }

  async function Save() {
    const { error }: any = await Post(
      "app/Project/AssignEmployeeProject",
      assignmentMapper
    );

    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Assigning!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Employee Assigned Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      showConfirmButton: true,
    });
    handleCloseModal();
  }

  const handleCloseModal = () => {
    setAssignmentMapper({ assignedList: [], unassignedList: [] });
    setShowModal(false);
    setRight([]);
    setReload((prev) => !prev);
  };

  return (
    <div
      className={`modal  fade ${showModal ? "show" : ""}`}
      style={{ display: showModal ? "block" : "none", marginTop: "7%" }}
      tabIndex={-1}
      aria-labelledby="exampleModalLabel"
      aria-hidden={!showModal}
    >
      <div className="modal-dialog">
        <div className="modal-content">
          <div className="modal-header">
            <h1
              className="modal-title"
              style={{ fontSize: "1.25rem", fontFamily: "Monospace" }}
            >
              Edit Assignee
            </h1>
            <button
              type="button"
              className="btn-close"
              onClick={handleCloseModal}
              aria-label="Close"
            ></button>
          </div>
          <div className="modal-body">
            <label className="mb-2 fs-8" style={{ fontWeight: "bolder" }}>
              Assign To:
            </label>
            <Multiselect
              options={lefts}
              selectedValues={rights}
              onSelect={(selectedList, selectedItem) =>
                handleSelect(selectedList, selectedItem)
              }
              onRemove={(selectedList, selectedItem) => {
                handleRemove(selectedList, selectedItem);
              }}
              displayValue="name"
              style={{
                chips: {
                  background: "#6777ef",
                },
                searchBox: {
                  border: "1px solid #e4e7ea!important;",
                  "padding-left": "10px",
                },
              }}
            />
          </div>
          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleCloseModal}
            >
              Close
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={() => Save()}
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
