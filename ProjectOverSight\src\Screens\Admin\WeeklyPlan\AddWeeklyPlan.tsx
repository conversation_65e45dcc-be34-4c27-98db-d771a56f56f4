import {
  Autocomplete,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  TextareaAutosize,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import "../../../StyleSheets/WeeklyPlan.css";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Project } from "../../../Models/Project/Project";
import { Get, Post } from "../../../Services/Axios";
import { CommonMaster } from "../../../Models/Common/CommonMaster";
import { PRIORITYTYPE } from "../../../Constants/Common/CommonMaster";
import { WEEKLYPLAN } from "../../../Constants/Common/CommonMaster";
import { Regex } from "../../../Constants/Regex/Regex";
import { ConvertToISO, WeekEndingDate } from "../../../Utilities/Utils";
import { useForm } from "react-hook-form";
import { IWeeklyPlan } from "../../../Models/WeeklyPlan/WeeklyPlan";
import { AlertOption, ModalAction } from "../../../Models/Common/AlertOptions";
import Swal from "sweetalert2";
import { Team } from "../../../Models/Team/Team";

type AddWeeklyPlanProps = {
  open: ModalAction | null;
  setOpen: (open: ModalAction) => void;
  setReload: Dispatch<SetStateAction<boolean>>;
};

interface TeamMember {
  id: number;
  name: string;
}

const formFields = [
  "project",
  "category",
  "description",
  "notes",
  "priority",
  "estimatedHours",
  "status",
  "dueDate",
  "teamId",
  "EmpId",
  "weekEndingDate",
  "CreatedBy",
  "UpdatedBy",
];

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

export const AddWeeklyPlan = ({
  open,
  setOpen,
  setReload,
}: AddWeeklyPlanProps) => {
  const [projects, setProjects] = useState<Array<Project>>([]);
  const [teamMemebers, setTeamMembers] = useState<any>([]);
  const [teams, setTeams] = useState([]);
  const [commonMaster, setCommonMaster] = useState<CommonMaster[]>([]);
  const [porjectId, setProjectId] = useState<number>(-1);
  const [teamId, setTeamId] = useState<number>(-1);
  const [employeeId, setEmployeeId] = useState<number[]>([]);
  const { register, handleSubmit, resetField } = useForm();

  async function fetchData() {
    const response: any = await Get("/app/Project/GetProjectList");
    const teamList: any = await Get("app/Team/GetTeamList");
    const responseMaster: any = await Get("app/CommonMaster/GetCodeTableList");
    setCommonMaster(responseMaster.data || []);
    setTeams(teamList.data || []);
    setProjects(response.data || []);
  }

  const handleTeamChange = (e: any, selectedOption: any) => {
    debugger
    if (!selectedOption || !selectedOption.id) {
      console.error("Selected option is null or undefined");
      return;
    }

    setEmployeeId([]);
    const { id } = selectedOption;
    setTeamId(id);

    Get(`/app/Team/GetTeamEmployeelist?teamId=${id}`)
      .then((response: unknown) => {
        const responseData = response as { data: TeamMember[] };
        setTeamMembers(responseData.data || []);
      })
      .catch((error) => {
        console.error("Error fetching team members:", error);
        setTeamMembers([]);
      });
    return e;
  };

  function reset() {
    formFields.map((e: string) => {
      resetField(e);
    });
  }

  useEffect(() => {
    fetchData();
  }, []);

  async function onSubmitHandler(data: any) {
    debugger
    var weeklyPlan: IWeeklyPlan = data;
    weeklyPlan.projectId = porjectId;
    weeklyPlan.teamId = teamId;
    weeklyPlan.empId = employeeId.join("|");
    weeklyPlan.status = "Pending";
    const employeeNames = teamMemebers
      .filter((employee: any) => employeeId.includes(employee.employeeId))
      .map((employee: any) => employee.employeeName);
    weeklyPlan.employees =  employeeId.length != 0 ?  employeeNames.join("|") : 0;
    const { error }: any = await Post("app/WeeklyPlan/AddWeeklyPlan", data);
    var option: AlertOption;
    if (error ) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Weekly Plan Added Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
      handleClose();
    });
  }

  function handleClose() {
    reset();
    setOpen({ add: false });
  }

  return (
    <Dialog open={open?.add ?? false}>
      <form onSubmit={handleSubmit(onSubmitHandler)}>
        <div className="dialog-title-container">
          <DialogTitle className="dialog-title">Add Weekly Plan</DialogTitle>
          <CancelOutlinedIcon
            className="close-icon"
            onClick={() => setOpen({ add: false })}
          />
        </div>
        <DialogContent className="row popup d-flex justify-content-center">
        <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
            <Autocomplete
              options={projects
                .filter((e: any) => e.isActive === true)
                .map((project: Project) => ({
                  label: project.name,
                  id: project.id,
                }))}
              onChange={(e: any, { id }: any) => {
                setProjectId(id);
                return e;
              }}
              // className="col-6  mb-2"
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Project"
                  required
                  {...register("project")}
                />
              )}
            />
            </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
            <Autocomplete
              options={teams
                .filter((e: any) => e.isActive === true)
                .map((team: Team) => ({
                  label: team.name,
                  id: team.id,
                }))}
              // className="col-6 mb-2"
              onChange={handleTeamChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Team"
                  required
                  {...register("teamId")}
                />
              )}
            />
            </FormControl>
         </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id="project-type" required>Employee</InputLabel>
              <Select
                labelId="employee"
                id="employees"
                multiple
                value={employeeId}
                {...register("TechStackId", {
                  onChange: (e: any) => {
                    setEmployeeId(e.target.value);
                  },
                })}
                input={<OutlinedInput label="Employee" />}
                renderValue={(selected) => (
                  <div>
                    {selected.map((value: number) => (
                      <span key={value} style={{ marginRight: 8 }}>
                        {
                          teamMemebers.find(
                            (option: any) => option.employeeId === value
                          )?.employeeName
                        }
                        ,
                      </span>
                    ))}
                  </div>
                )}
                MenuProps={MenuProps}
              >
                {teamMemebers.map((member: any) => (
                  <MenuItem key={member.employeeId} value={member.employeeId}>
                    <Checkbox
                      checked={employeeId.indexOf(member.employeeId) > -1}
                    />
                    <ListItemText primary={member.employeeName} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
            <TextField
              required
              // className="col m-2"
              label="Weekly Deliverable"
              {...register("notes", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                },
              })}
              type="text"
              variant="outlined"
            />
            </FormControl>
            </Grid>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
            <TextareaAutosize
              required
              // className="col  m-2 form-control"
              {...register("description", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                },
              })}
              placeholder="Description*"
              style={{ height: 100 }}
            />
            </FormControl>
         </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
            <TextField
              required
              // className="col m-2"
              label="Estimated Hours"
              {...register("estimatedHours", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.DECIMAL, "");
                },
              })}
              type="text"
              variant="outlined"
            />
            </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
            <Autocomplete
              options={commonMaster
                .filter((commonMaster) => commonMaster.codeType === WEEKLYPLAN)
                .sort((a, b) => a.codeValue.localeCompare(b.codeValue))
                .map((commonMaster: CommonMaster) => ({
                  label: commonMaster.codeValue,
                  id: commonMaster.id,
                }))}
              // className="col-6 mb-2 mt-2"
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Category"
                  required
                  {...register("category")}
                />
              )}
            />
            </FormControl>
          </Grid>
              <Grid item xs={12} md={6}>
              <InputLabel id="start-date">Due Date*</InputLabel>
              <FormControl fullWidth>
              <TextField
                required
                id="start-date"
                margin="dense"
                inputProps={{
                  min: new Date().toISOString().slice(0, 10),
                }}
                type="date"
                {...register("dueDate")}
                fullWidth
                variant="outlined"
              />
              </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              <InputLabel id="end-date">Week Ending Date*</InputLabel>
              <FormControl fullWidth>
              <TextField
                required
                id="end-date"
                margin="dense"
                defaultValue={ConvertToISO(WeekEndingDate())}
                inputProps={{
                  min: new Date().toISOString().slice(0, 10),
                }}
                {...register("weekEndingDate")}
                type="date"
                fullWidth
                variant="outlined"
              />
              </FormControl>
             </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
            <Autocomplete
              options={commonMaster
                .filter((x) => x.codeType === PRIORITYTYPE)
                .map((commonMaster: CommonMaster) => ({
                  label: commonMaster.codeValue,
                  id: commonMaster.codeValue,
                }))}
              // className="col-6 mb-2 mt-2"
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Priority"
                  required
                  {...register("priority")}
                />
              )}
            />
            </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
            <TextField
              defaultValue={"Pending"}
              disabled
              // className="col m-2"
              label="Status"
            />
          </FormControl>
          </Grid>
          </Grid>
          <input {...register("CreatedBy")} value="user" hidden />
          <input {...register("UpdatedBy")} value="user" hidden />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpen({ add: false })}
            size="medium"
            variant="contained"
            color="error"
          >
            Cancel
          </Button>
          <Button
            size="medium"
            variant="contained"
            color="success"
            type="submit"
          >
            Save
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
