import {
    Dialog,
    <PERSON>alogActions,
    DialogContent,
    DialogTitle,
    TextField,
    Button,
    FormControl,
    InputLabel,
    Grid,
    TextareaAutosize,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
export const ViewUserInterface = ({ openDialog, setOpenDialog, Data }: any) => {
    const handleClose = () => {
        setOpenDialog({ view: false });
    };

    return (
      <div>
        <Dialog open={openDialog?.view}>
          <form>
            <div
              style={{
                backgroundColor: "#f0f0f0",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <DialogTitle style={{ color: "blue", flex: "1" }}>
                View User Interface
              </DialogTitle>
              <CancelOutlinedIcon
                onClick={handleClose}
                sx={{
                  color: "red",
                  fontSize: "30px",
                  marginRight: "10px",
                  cursor: "pointer",
                }}
              />
            </div>
            <DialogContent className="row popup">
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <TextField
                  required
                  value={Data?.name}
                  label="User Interface Name"
                  type="text"
                  variant="outlined"
                  className="read-only-input"
                />
                 </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                  <TextField
                        id="outlined-read-only-input"
                        label="Status"
                        defaultValue={Data?.status}
                        className="read-only-input"
                        InputProps={{
                          readOnly: true,
                        }}
                      />
                 </FormControl>
            </Grid>
            <Grid item xs={12}>
            <InputLabel id="Description">Description</InputLabel>
            <FormControl fullWidth>
            <TextareaAutosize
                  required
                  defaultValue={Data?.description}
                  placeholder="Description"
                  style={{ height: 100 }}
                  className="read-only-input"
                  disabled
                />
                {/* <TextField
                  required
                  value={Data?.description}
                  margin="dense"
                  label="Description"
                  type="text"
                  fullWidth
                  variant="outlined"
                /> */}
              </FormControl>
            </Grid>
            {/* <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <TextField
                  required
                  value={Data?.status}
                  label="Status"
                  type="text"
                  variant="outlined"
                />
                 </FormControl>
            </Grid> */}
            <Grid item xs={12} md={6}>
            <InputLabel id="start-date">Start date</InputLabel>
            <FormControl fullWidth>
                  <TextField
                    required
                    id="start-date"
                    value={Data?.startDate?.slice(0, 10)}
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                    className="read-only-input"
                    disabled
                  />
               </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <InputLabel id="end-date">End date</InputLabel>
            <FormControl fullWidth>
                  <TextField
                    required
                    id="end-date"
                    value={Data?.endDate?.slice(0, 10)}
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                    className="read-only-input"
                    disabled
                  />
                 </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <TextField
                  required
                  value={Data?.percentage}
                  label="Percentage"
                  type="text"
                  fullWidth
                  variant="outlined"
                  className="read-only-input"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <TextField
                  required
                  value={Data?.complexity}
                  label="Complexity"
                  type="text"
                  fullWidth
                  variant="outlined"
                  className="read-only-input"
                />
               </FormControl>
            </Grid>
            </Grid>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={handleClose}
                size="medium"
                variant="contained"
                color="primary"
              >
                OK
              </Button>
            </DialogActions>
          </form>
        </Dialog>
      </div>
    );
};
