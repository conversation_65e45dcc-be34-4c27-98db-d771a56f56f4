import {
  Alert,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextareaAutosize,
  Grid,
} from "@mui/material";
import { useState } from "react";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import { Get, Post } from "../../../../Services/Axios";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import { Regex } from "../../../../Constants/Regex/Regex";

const formField = [
  "Name",
  "Description",
  "StartDate",
  "EndDate",
  "Status",
  "Percentage",
  "Complexity",
  "CreatedBy",
  "UpdatedBy",
  "ProjectId",
  "id",
];

export const EditUserInterface = ({
  openDialog,
  setOpenDialog,
  projectId,
  Data,
  setReload,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [save, setSave] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });

  const onSubmitHandler = async (data: any) => {
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",  
        show: true,
      });
      return;
    }
    data.status = Data.status;
    setSave(true);
    const { error }: any = await Post("app/Project/UpdateUserInterface", data);
    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "User Interface Updated Successfully!",
        icon: "success",
      };
    }
    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      let userStoryList = Get(
        `app/Project/GetUserInterfaceList?projectId=${projectId}`
      );
      userStoryList.then(() => {
        setReload((prev: boolean) => !prev);
      });
    });
    handleClose();
  };

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ edit: false });
  };

  return (
    <div>
      <Dialog open={openDialog?.edit}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Edit User Interface
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
           <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                defaultValue={Data?.name}
                {...register("Name")}
                onChange={(e:any) => {
                  e.target.value = e.target.value.replace(Regex.EMAIL_1,"");
                }}
                label="User Interface Name"
                type="text"
                variant="outlined"
              />
            </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                  id="outlined-read-only-input"
                  disabled
                  label="Status"
                  defaultValue={Data?.status}
                  InputProps={{
                    readOnly: true,
                  }}
                  {...register("Status")}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
            <InputLabel id="Description">Description</InputLabel>
            <FormControl fullWidth>
            <TextareaAutosize
                required
                defaultValue={Data?.description}
                {...register("Description")}
                onChange={(e:any) =>{ 
                  e.target.value = e.target.value.repalce(Regex.CHAR_NUM,"");
                }}
                style={{ height: 80 }}
              />
           </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <InputLabel id="Complexity">Complexity</InputLabel>
                <Select
                  required
                  labelId="Complexity"
                  defaultValue={Data?.complexity}
                  id="Complexity"
                  label="Complexity"
                  {...register("Complexity")}
                >
                  <MenuItem value="Low">Low</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="High">High</MenuItem>
                </Select>
                </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                required
                defaultValue={Data?.percentage}
                {...register("Percentage")}
                label="Percentage"
                type="type"
                fullWidth
                variant="outlined"
              />
           </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <InputLabel id="start-date">Start date</InputLabel>
            <FormControl fullWidth>
                <TextField
                  required
                  id="start-date"
                  defaultValue={Data?.startDate?.slice(0, 10)}
                  margin="dense"
                  {...register("StartDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <InputLabel id="end-date">End date</InputLabel>
            <FormControl fullWidth>
                <TextField
                  required
                  id="end-date"
                  defaultValue={Data?.endDate?.slice(0, 10)}
                  margin="dense"
                  {...register("EndDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            </Grid>
              <input {...register("CreatedBy")} value="user" hidden />
              <input {...register("UpdatedBy")} value="user" hidden />
              <input {...register("ProjectId")} value={projectId} hidden />
              <input {...register("id")} value={Data?.id} hidden />
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              color="success"
              disabled={save}
              type="submit"
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
