import { <PERSON>, <PERSON><PERSON>c<PERSON><PERSON>, <PERSON>ton, Grid, Typography } from "@mui/material";
import { SearchIcon } from "lucide-react";
import RefreshIcon from "@mui/icons-material/Refresh";
import { useRef, useState } from "react";
import { Link } from "react-router-dom";
import DataTable from "react-data-table-component";
import TakeScrum from "./TakeScrum";

function Scrum() {
  const projectRef = useRef<HTMLInputElement>(null);
  const statusRef = useRef<HTMLInputElement>(null);
  const priorityRef = useRef<HTMLInputElement>(null);
  const startDateRef = useRef<HTMLInputElement>(null);
  const endDateRef = useRef<HTMLInputElement>(null);
  const weekEndingDateRef = useRef<HTMLInputElement>(null);
  const [show, setShow] = useState(false);
  const [scrum, setscrum] = useState([]);

  const [filter, setfilter] = useState({
    Project: null,
    Status: null,
    Priority: null,
    StartDate: null,
    EndDate: null,
    WeekEndingDate: null,
  });

  const ApplyFilter = () => {
    console.log(filter);
  };

  const reset = () => {
    setfilter({
      Project: null,
      Status: null,
      Priority: null,
      StartDate: null,
      EndDate: null,
      WeekEndingDate: null,
    });
    if (projectRef.current) projectRef.current.value = "";
    if (statusRef.current) statusRef.current.value = "";
    if (priorityRef.current) priorityRef.current.value = "";
    if (startDateRef.current) startDateRef.current.value = "";
    if (endDateRef.current) endDateRef.current.value = "";
    if (weekEndingDateRef.current) weekEndingDateRef.current.value = "";
  };

  const columns: any = [
    {
      field: "action",
      name: "Action",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => <p className="tableStyle">{row.name}</p>,
    },
    {
      field: "Date",
      name: "Date",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => <p className="tableStyle">{row.name}</p>,
    },
    {
      field: "Week Ending Date",
      name: "Week Ending Date",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => <p className="tableStyle">{row.name}</p>,
    },
  ];
  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to="/Employee">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Scrum</Typography>
      </Breadcrumbs>
      <div className="well mx-auto mt-4">
        <div className="container">
          <div className="row">
            <div className="col-md-2">
              <div className="form-group">
                <label>Project</label>
                <input
                  id="project"
                  ref={projectRef}
                  placeholder="Project"
                  className="m-1 form-control col"
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        Project: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                />
              </div>
            </div>
            <div className="col-md-2">
              <div className="form-group">
                <label>Status</label>
                <input
                  id="status"
                  ref={statusRef}
                  placeholder="Status"
                  className="m-1 form-control col"
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        Status: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                />
              </div>
            </div>
            <div className="col-md-2">
              <div className="form-group">
                <label>Priority</label>
                <input
                  id="priority"
                  ref={priorityRef}
                  placeholder="Priority"
                  className="m-1 form-control col"
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        Priority: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                />
              </div>
            </div>
            <div className="col-md-2">
              <div className="form-group">
                <label className="mx-1">Start Date</label>
                <input
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        StartDate: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                  ref={startDateRef}
                  type="date"
                  id="estimated-start-date"
                  placeholder="Estimated Start Date"
                  className="m-1  form-control"
                />
              </div>
            </div>
            <div className="col-md-2">
              <div className="form-group">
                <label className="mx-1">End Date</label>
                <input
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        EndDate: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                  ref={endDateRef}
                  type="date"
                  id="estimated-End-date"
                  placeholder="Estimated End Date"
                  className="m-1  form-control"
                />
              </div>
            </div>
            <div className="col-md-2">
              <div className="form-group">
                <label className="mx-1">Week Ending Date</label>
                <input
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        WeekEndingDate:
                          e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                  ref={weekEndingDateRef}
                  type="date"
                  id="estimated-End-date"
                  placeholder="Estimated End Date"
                  className="m-1  form-control"
                />
              </div>
            </div>
            <div className="col-md-12">
              <div className="row justify-content-end">
                <div className="col-auto">
                  <Button
                    variant="contained"
                    endIcon={<SearchIcon />}
                    className="mx-2 mt-4"
                    onClick={() => ApplyFilter()}
                  >
                    Search
                  </Button>
                  <Button
                    variant="contained"
                    endIcon={<RefreshIcon />}
                    className="mx-2 mt-4"
                    onClick={() => reset()}
                  >
                    Reset
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center mb-5">
        <Grid item xs={12} sm={11}>
          <div className="col-5">
            <Grid>
              <Button
                variant="contained"
                className="mb-2 float-md-start"
                size="small"
                onClick={() => setShow(true)}
                sx={{ fontSize: { xs: "11px", md: "15px" } }}
              >
                Take Scrum
              </Button>
            </Grid>
          </div>
          <Box style={{ width: "94vw" }}>
            <DataTable
              columns={columns}
              fixedHeader
              responsive
              persistTableHead
              //   progressPending={loading}
              data={scrum}
              customStyles={{
                table: {
                  style: {
                    height: "80vh",

                    border: "1px solid rgba(0,0,0,0.1)",
                  },
                },

                headRow: {
                  style: {
                    background: "#1e97e8",
                    fontSize: "16px",
                    color: "white",
                    fontFamily: "inherit",
                  },
                },
              }}
              pagination
              paginationPerPage={50}
              paginationRowsPerPageOptions={[50, 100, 200]}
              pointerOnHover={true}
            />
          </Box>
        </Grid>
      </div>
      <TakeScrum show={show} setShow={setShow} />
    </>
  );
}

export default Scrum;
