import { Divider } from "@mui/material";
import { Dispatch, SetStateAction, useState } from "react";
import { Button, Form, Modal } from "react-bootstrap";
import YEARS from "../../../Utilities/Utils";
import { Get } from "../../../Services/Axios";
import { DownloadLeaveReport } from "../../../Services/TeamService";
import { MONTHS } from "../../../Constants/Common/Common";

const CURRENT_YEAR = new Date().getFullYear();
const CURRENT_MONTH = new Date().getMonth() + 1;

type GenerateReportProps = {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
};
export const GenerateReport = ({ open, setOpen }: GenerateReportProps) => {
  const [reload, setReload] = useState<boolean>(false);
  const [dateFilter, setDateFilter] = useState<any>({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  });

  async function generateReport() {
    setReload(true);
    const response: any = await Get(
      `app/Employee/generateAttendanceReport?year=${dateFilter.year}&month=${dateFilter.month}`
    );
    DownloadLeaveReport(
      response?.data?.teamLeaveDetails,
      dateLabels,
      response?.data?.days,
      "full"
    );
    setReload(false);
    setDateFilter({
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
    });
    setOpen(false);
  }

  const daysInMonth = new Date(dateFilter.year, dateFilter.month, 0).getDate();
  const dateLabels = Array.from({ length: daysInMonth }, (_, i) => {
    const currentDate = new Date(dateFilter.year, dateFilter.month - 1, i + 1);
    return currentDate.toLocaleDateString("en-US", {
      weekday: "short",
      year: "numeric",
      month: "short",
      day: "2-digit",
    });
  });

  function isDisabled(id: number): boolean {
    if (parseInt(dateFilter.year) === CURRENT_YEAR) {
      return id > CURRENT_MONTH;
    }
    return false;
  }

  function isSelected(id: number) {
    if (parseInt(dateFilter.year) === CURRENT_YEAR) {
      return id === CURRENT_MONTH;
    }
    return id === dateFilter.month;
  }

  function handleYearChange(year: string) {
    if (parseInt(year) === CURRENT_YEAR) {
      setDateFilter({ year: Number(year), month: CURRENT_MONTH });
    } else {
      setDateFilter({ ...dateFilter, year: Number(year) });
    }
  }

  return (
    <Modal show={open} onHide={() => setOpen(false)} centered>
      <Modal.Header closeButton>
        <Modal.Title>Generate Attendance Report</Modal.Title>
      </Modal.Header>
      <Divider />
      <Modal.Body>
        <Form.Group className="mb-3" controlId="exampleForm.ControlInput1">
          <Form.Label>Year*</Form.Label>
          <Form.Select
            placeholder="Year"
            required
            onChange={(e: any) => {
              handleYearChange(e.target.value);
            }}
          >
            {YEARS.map((year: number) => (
              <option value={year} key={year}>
                {year}
              </option>
            ))}
          </Form.Select>
        </Form.Group>
        <Form.Group className="mb-3" controlId="exampleForm.ControlTextarea1">
          <Form.Label>Month*</Form.Label>
          <Form.Select
            placeholder="Description"
            required
            onChange={async (e: any) => {
              setDateFilter({ ...dateFilter, month: e.target.value });
            }}
          >
            {MONTHS.map((month) => (
              <option
                key={month.id}
                value={month.id}
                selected={isSelected(month.id)}
                disabled={isDisabled(month.id)}
              >
                {month.name}
              </option>
            ))}
          </Form.Select>
        </Form.Group>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          disabled={reload}
          onClick={() => generateReport()}
        >
          Generate
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
