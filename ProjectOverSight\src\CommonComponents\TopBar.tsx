import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import MenuIcon from "@mui/icons-material/Menu";
import Container from "@mui/material/Container";
import { Link, useLocation, useNavigate } from "react-router-dom";
import Drawer from "@mui/material/Drawer";
import List from "@mui/material/List";
import { useEffect, useState } from "react";
import { ADMIN, CUSTOMER, EMPLOYEE, Roles } from "../Constants/Common/Roles";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import Divider from "@mui/material/Divider";
import {
  Grid,
  Hidden,
  Menu,
  MenuItem,
  Tab,
  Tabs,
  Tooltip,
  styled,
} from "@mui/material";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import LogoutIcon from "@mui/icons-material/Logout";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { SessionUser } from "../Models/Employee/Employee";
import { AdminPages, Admindrawer } from "../Constants/AdminPages";
import { EmployeeDrawer, EmployeePages } from "../Constants/EmployeePages";
import { CustomerPages, Customerdrawer } from "../Constants/CustomerPages";
import { useContextProvider } from "./Context";
import { ADMIN_ROUTES, EMPLOYEE_ROUTES ,CUSTOMER_ROUTES } from "../Constants/Common/Routes";
import { EditProfile } from "../Screens/Employee/EditProfile";
type Anchor = "top" | "left" | "bottom" | "right";

export const TopBar = () => {
  const [state, setState] = useState({
    top: false,
    left: false,
    bottom: false,
    right: false,
  });
  const [pages, setPages] = useState([]);
  const [drawer, setDrawer] = useState([]);
  const json: any = sessionStorage.getItem("user") || null;
  const sessionUser: SessionUser = JSON.parse(json);
  const [value, setValue] = useState<number | boolean>(-1);
  const { setLoading, role } = useContextProvider();
  const { pathname } = useLocation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [show, setShow] = useState(false);

  useEffect(() => {
    const routes = role === Roles.ADMIN ? ADMIN_ROUTES : ( role === Roles.CUSTOMER ? CUSTOMER_ROUTES : EMPLOYEE_ROUTES);
    const route = routes.indexOf(pathname);
    setValue(route >= 0 ? route : false);
  }, [pathname]);

  const DrawerHeader = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    padding: theme.spacing(0, 1),
    ...theme.mixins.toolbar,
    justifyContent: "flex-end",
  }));

  const navigate = useNavigate();

  useEffect(() => {
    var PAGES: any;
    var DRAWER: any;
    switch (sessionUser?.userRoles) {
      case ADMIN:
        PAGES = AdminPages;
        DRAWER = Admindrawer;
        break;
      case EMPLOYEE:
        PAGES = EmployeePages;
        DRAWER = EmployeeDrawer;
        break;
      case CUSTOMER:
        PAGES = CustomerPages;
        DRAWER = Customerdrawer;
        break;
      default:
        PAGES = [];
        DRAWER = [];
    }
    setPages(PAGES);
    setDrawer(DRAWER);
  }, [sessionUser]);

  const handleCloseNavMenu = (page: string) => {
    navigate(page);
  };

  const toggleDrawer = (anchor: Anchor, open: boolean) => {
    setState({ ...state, [anchor]: open });
  };

  const Logout = () => {
    sessionStorage.removeItem("user");
    setLoading((prev) => !prev);
    setPages([]);
    navigate("/Login");
  };

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (sessionUser.userRoles === ADMIN || sessionUser.userRoles == CUSTOMER) return;
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setShow(true);
    setAnchorEl(null);
  };

  return (
    <>
      <AppBar position="sticky" className="bg-light">
        <Container maxWidth="xl">
          <Toolbar disableGutters className="w-100 d-flex ">
            <div
              className="d-flex align-items-center w-100 justify-content-between"
              style={{
                borderRadius: "10px",
              }}
            >
              <Typography
                variant="h6"
                noWrap
                sx={{
                  fontFamily: "Product Sans",
                  fontWeight: 600,
                  fontSize: 26,
                  color: "white",
                  textDecoration: "none",
                }}
              >
                {sessionUser === null ? null : (
                  <MenuIcon
                    className="mx-3"
                    style={{ color: "rgb(107,122,144)", cursor: "pointer" }}
                    onClick={() => toggleDrawer("left", true)}
                  />
                )}
                <Hidden only={["xs", "sm"]}>
                  <Link
                    to={`/${sessionUser?.userRoles ?? "Login"}`}
                    style={{ color: "rgb(3, 108, 219)", fontSize: '22px', }}
                  >
                    Project Oversight
                  </Link>
                </Hidden>

                <Hidden only={["lg", "xl", "md"]}>
                  <Link
                    to={`/${sessionUser?.userRoles ?? "Login"}`}
                    style={{ color: "rgb(3, 108, 219)", fontSize: "16px" }}
                  >
                    Project Oversight
                  </Link>
                </Hidden>
              </Typography>

              <Hidden only={["xs", "sm", "md"]}>
                <Grid>
                  <Tabs
                    value={value}
                    onChange={handleChange}
                    sx={{
                      display: {
                        md: "hidden",
                      },
                    }}
                  >
                    {pages.map((page: any, index: number) => {
                      return (
                        <Tab
                          icon={<page.icon />}
                          key={page.name + index}
                          label={page?.name}
                          sx={{
                            fontSize: "small",
                            fontFamily: "Product Sans",
                            color: "#6B7A90",
                            fontWeight: "bold",
                          }}
                          className="mx-3"
                          onClick={() =>
                            handleCloseNavMenu(
                              `/${sessionUser.userRoles}/` +
                                page?.name?.replaceAll(" ", "")
                            )
                          }
                        />
                      );
                    })}
                  </Tabs>
                </Grid>
              </Hidden>
              {sessionUser === null ? null : (
                <div
                  className="d-flex align-items-center"
                  style={{
                    color: "#6B7A90",
                    fontWeight: "bold",
                    fontSize: "larger",
                  }}
                >
                  <button
                    aria-controls={open ? "basic-menu" : undefined}
                    aria-haspopup="true"
                    aria-expanded={open ? "true" : undefined}
                    onClick={handleClick}
                    className="border rounded-circle border-0 bg-light"
                    id="basic-button"
                  >
                    <AccountCircleIcon
                      className="mx-2"
                      style={{
                        color: "#6B7A90",
                      }}
                    />
                  </button>
                  <span>{sessionUser?.userName}</span>

                  <Tooltip title={"Logout"}>
                    <LogoutIcon
                      className="fs-4 mx-2"
                      onClick={() => Logout()}
                    />
                  </Tooltip>
                </div>
              )}
            </div>
          </Toolbar>
          <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            MenuListProps={{
              "aria-labelledby": "basic-button",
            }}
          >
            <MenuItem onClick={handleClose}>Edit Profile</MenuItem>
          </Menu>
          <EditProfile open={show} setOpen={setShow} />
        </Container>
      </AppBar>
      <Drawer
        sx={{
          width: 300,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: 300,
            boxSizing: "border-box",
          },
        }}
        anchor="left"
        open={state["left"]}
        onClose={() => toggleDrawer("left", false)}
      >
        <DrawerHeader className="d-flex align-items-center justify-content-between">
          <span className="mx-1 fw-bolder fs-5 text-primary">
            Project Oversight
          </span>
          <IconButton onClick={() => toggleDrawer("left", false)}>
            {state ? <ChevronLeftIcon /> : <ChevronRightIcon />}
          </IconButton>
        </DrawerHeader>
        <Divider />
        <Box onClick={() => toggleDrawer("left", false)}>
          <List>
            {drawer.map((text: any, index: number) => (
              <div key={text}>
                <ListItem disablePadding className="box-3">
                  <ListItemButton
                    className="btn-three"
                    onClick={(e: any) => {
                      handleChange(e, index);
                      handleCloseNavMenu(
                        `/${sessionUser?.userRoles}/` +
                          text?.name?.replaceAll(" ", "")
                      );
                    }}
                  >
                    <text.icon className="mx-2" />{" "}
                    <ListItemText primary={text?.name} />
                  </ListItemButton>
                </ListItem>
                <Divider />
              </div>
            ))}
          </List>
        </Box>
      </Drawer>
    </>
  );
};
