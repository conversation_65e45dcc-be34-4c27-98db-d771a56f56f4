import { ConvertTime, ConvertToISO } from "../Utilities/Utils";
import * as XLSX from "xlsx";
import { Day } from "../Models/Team/Day";

export function GetHours(inTime: string, outTime: string) {
  if (!outTime) return `${0}hr ${0}min`;
  const startDate = new Date(inTime);
  const endDate = new Date(outTime);
  const timeDifference = endDate.getTime() - startDate.getTime();
  const hours = Math.floor(timeDifference / (1000 * 60 * 60));
  const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));

  if (timeDifference < 0) return `0hr 0min`;
  return `${hours}hr ${minutes}min`;
}

function calculateAbsent(Days: any, present: number): number {
  return (
    Days.filter( (x: any) =>  x.date.slice(0, 10) <= ConvertToISO(new Date())).length -
    present -
    Days.filter(
      (x: any) =>
        x.holidayApplicable === true &&
        x.date.slice(0, 10) <= ConvertToISO(new Date())
    ).length
  );
}

function parseTimes(date: any):string {
  return ConvertTime(date.inTime, "") + " - " + ConvertTime(date.outTime, "");
}

function isPastDate(date: any,index:number):boolean {
  var year = new Date(date).getFullYear();
  var month = new Date(date).getMonth();

  if (year === new Date().getFullYear() && month === new Date().getMonth()) {
    return index + 1 <= new Date().getDate();
  }
  return true;  
}

export function DownloadLeaveReport(
  data: any,
  dateLabels: any,
  Days: Day[],
  type?: string
) {
  const downloadData: any = [];
  debugger
  data.forEach((e: any) => {
    let team: any = {
      EmployeeName: e.employeeName ?? "",
      EmployeeCode: e.employeeCode ?? "-",
      Location: e.location ?? "-",
      Manager:e.manager ?? "-"
    };
    var present = 0;
    dateLabels.map((label: any, index: number) => {
      var date = e.employeeTimes.find(
        (x: any) => new Date(x.inTime).getDate() === index + 1
      );
      if (date) {
        var day = new Date(date.inTime).getDay();
        if (day !== 0 && day !== 6) present++;
      }
   
      const isPastdate = isPastDate(Days[0].date,index)

      if (type === "full") {
        team[`${label}`] = date
          ? parseTimes(date)
          : Days[index]?.holidayApplicable
          ? Days[index].holidayName
          : !isPastdate  
          ? "-"
          : "Absent";
      } else {
        team[`${label}`] = date
          ? GetHours(date.inTime, date.outTime)
          : Days[index]?.holidayApplicable
          ? Days[index].holidayName
          : "Absent";
      }
    });

    team["Present"] = present
    team["Absent"] =
    calculateAbsent(Days, present) < 0 ? 0 : calculateAbsent(Days, present);
    team["Total Working Days"] =team["Absent"] + present

    downloadData.push(team);
  });

  const ws = XLSX.utils.json_to_sheet(downloadData);
  
  const columnWidths = [
    { wpx: 100 },
    ...dateLabels.map(() => Object({ wpx: 130 })),
    { wpx: 100 },
    { wpx: 120 },
  ];

  ws["!cols"] = columnWidths;
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, "LeaveReportList");
  XLSX.writeFile(wb, "LeaveReportList.xlsx");
}
