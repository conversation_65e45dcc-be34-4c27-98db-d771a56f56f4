// import PsychologyIcon from "@mui/icons-material/Psychology";
// import BadgeIcon from "@mui/icons-material/Badge";
import GroupsIcon from "@mui/icons-material/Groups";
import HourglassTopIcon from "@mui/icons-material/HourglassTop";
import ViewKanbanIcon from '@mui/icons-material/ViewKanban';
import EditNoteIcon from '@mui/icons-material/EditNote';
import TaskAltIcon from "@mui/icons-material/TaskAlt";
// import { Bug } from "lucide-react";
import { GanttChartSquare } from "lucide-react";
// import PersonIcon from "@mui/icons-material/Person";

export const CustomerPages = [
  { name: "Task", icon: TaskAltIcon },
  { name: "Weekly Plan", icon:GanttChartSquare},
  {name : "Requirement Matrix", icon:EditNoteIcon},
    // { name: "Bug / Enhancement", icon: Bug },
    // { name: "Sprint", icon:GanttChartSquare},
    { name: "Project", icon: ViewKanbanIcon },
    { name: "Team", icon: GroupsIcon },
    // { name: "Employee", icon: BadgeIcon },
    // { name: "Attendance", icon: PersonIcon },
    
];

export const Customerdrawer = [
  // { name: "Bug/Enhancement", icon: Bug },
  // { name: "Sprint", icon:GanttChartSquare},
  { name: "Task Progress", icon: HourglassTopIcon },
  { name: "Team", icon: GroupsIcon },
  { name: "Project", icon: ViewKanbanIcon },
  // { name: "Skill", icon: PsychologyIcon },
  // { name: "Employee", icon: BadgeIcon },
  //  { name: "Attendance", icon: PersonIcon },
];