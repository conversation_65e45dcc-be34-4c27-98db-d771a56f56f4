import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Box } from "@mui/material";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import { useLocation } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import Select from "react-select";
import DataTable from "react-data-table-component";
import AccordionDetails from "@mui/material/AccordionDetails";
import { Get } from "../../../Services/Axios";
import { useQuery } from "react-query";
import { Link } from "react-router-dom";
import { useContextProvider } from "../../../CommonComponents/Context";
import { ToolTip } from "../../../CommonComponents/ToolTip";
import { TaskFilter } from "../../../Models/Task/TaskFilter";
import { FILTER } from "../../../Constants/Task/Task";
import { UserStory } from "../../../Models/Project/UserStory";
import { UserInterface } from "../../../Models/Project/UserInterface";
import BackDrop from "../../../CommonComponents/BackDrop";

const Accordionstyle = {
  backgroundColor: "#3be3cf",
  color: "black",
  maxHeight: "2%",
  height: "85%",
  margintop: "2%",
  fontWeight: "bold",
  fontSize: "55%",
};

const headersStyle = {
  textAlign: "left",
  ml: "2%",
  color: "black",
  fontSize: "215%",
};

export const EmployeeDashboard = () => {
  const location = useLocation();
  const { role } = useContextProvider();
  const statusRef = useRef<any>();
  const projectNameRef = useRef<any>();
  const UserStoryRef = useRef<any>();
  const UserInterfaceNameRef = useRef<any>();   
  const [filterRows, setfilterRows] = useState<any>([]);
  const [rows, setRows] = useState<any>([]);
  const [filter, setfilter] = useState<TaskFilter>(FILTER);
  const [USfilter, USsetfilter] = useState<UserStory>({});
  const [UIfilter, UIsetfilter] = useState<UserInterface>({});
  var [Projects, setProjects] = useState<string[]>([]);

  const columns: any = [
    {
      name: "SI.No",
      width: "5rem",
      right: true,
      selector: (row: any, index: number) => (
        <p className={`tableStyle ${row}`}>{index + 1}</p>
      ),
    },
    {
      name: "Task Name",
      width: "11rem",
      selector: (row: any) => (
        <ToolTip title={row?.name}>
          <p className="tableStyle">{row?.name}</p>
        </ToolTip>
      ),
    },
    {
      name: "Description",
      width: "16rem",
      selector: (row: any) => (
        <ToolTip title={row?.taskDescription}>
          <p className="tableStyle">{row?.taskDescription || "-"}</p>
        </ToolTip>
      ),
    },
    {
      name: "User Story",
      width: "16rem",
      selector: (row: any) => (
        <ToolTip title={row?.userStory}>
          <p className="tableStyle">{row?.userStory || "-"}</p>
        </ToolTip>
      ),
    },
    {
      name: "User Interface",
      width: "15rem",
      selector: (row: any) => (
        <ToolTip title={row?.userInterface}>
          <p className="tableStyle">{row?.userInterface || "-"}</p>
        </ToolTip>
      ),
    },
    {
      name: "Project Name",
      width: "13rem",
      selector: (row: any) => (
        <ToolTip title={row?.userInterface}>
          <p className="tableStyle">{row?.projectName || "-"}</p>
        </ToolTip>
      ),
    },
    {
      name: "Task Status",
      width: "12rem",
      selector: (row: any) => (
        <p className="tableStyle">{row?.status || "-"}</p>
      ),
    },
  ];

  const currentDate = new Date();
  async function EmployeeDetails() {
    const totalattendance = await Get(
      `app/Employee/GetAttendanceByUserId?userId=${location.state.data}&month=${
        currentDate.getMonth() + 1
      }&year=${currentDate.getFullYear()}`
    );
    const EmployeeDetail = await Get(
      `app/Employee/GetEachEmployeeDashboards?employeeId=${location.state.employeeId}`
    );
    return { totalattendance, EmployeeDetail };
  }
  const { data, isLoading }: any = useQuery(
    "Employeetotaldetails",
    EmployeeDetails
  );

  useEffect(() => {
    if (data) {
      setRows(data?.EmployeeDetail?.data?.employeeDailyTask || []);
      setfilterRows(data?.EmployeeDetail?.data?.employeeDailyTask || []);
      setProjects(data?.EmployeeDetail?.data?.projectDetails || []);
    }
  }, [data]);

  const uniqueProjects = new Set();
  const uniqueStatus = new Set();
  const NumberofProject = data?.EmployeeDetail?.data?.projectDetails?.map(
    (e: any) => e?.id
  );
  const NumberofTask = data?.EmployeeDetail?.data?.employeeDailyTask?.map(
    (e: any) => e?.taskId
  );
  const NumberofProjectInprogress =
    data?.EmployeeDetail?.data?.projectDetails?.filter(
      (e: any) => e?.status === "In Progress"
    );
  const NumberofProjectCompleted =
    data?.EmployeeDetail?.data?.projectDetails?.filter(
      (e: any) => e?.status === "Completed"
    );
  const NumberofTaskInprogress =
    data?.EmployeeDetail?.data?.employeeDailyTask?.filter(
      (e: any) => e?.status === "In-Progress"
    );
  const NumberofTaskCompleted =
    data?.EmployeeDetail?.data?.employeeDailyTask?.filter(
      (e: any) => e?.status === "Completed"
    );
  const countInprogress = NumberofProjectInprogress?.length || 0;
  const countCompleted = NumberofProjectCompleted?.length || 0;
  const Taskstatus = data?.EmployeeDetail?.data?.employeeDailyTask?.filter(
    (e: any) => e?.status
  );

  const TotalActualHours = data?.EmployeeDetail?.data?.employeeDailyTask
    ?.filter((e: any) => e?.actTime)
    .reduce((sum: number, task: any) => sum + (task?.actTime || 0), 0);

  NumberofProject?.forEach((row: any) => {
    if (row) {
      uniqueProjects.add(row);
    }
  });
  Taskstatus?.forEach((row: any) => {
    if (row) {
      uniqueStatus.add(row);
    }
  });

  const uniqueStatusValues = Array.from(
    new Set(Taskstatus?.map((item: any) => item.status))
  );
  const uniqueuserStoryValues = Array.from(
    new Set(Taskstatus?.map((item: any) => item.userStory))
  );
  const uniqueuserInterfaceValues = Array.from(
    new Set(Taskstatus?.map((item: any) => item.userInterface))
  );

  const Statusoptions = uniqueStatusValues
    .slice()
    .sort((a: any, b) => a.localeCompare(b))
    .map((status) => ({
      value: status,
      label: status,
    }));

  const uniqueuserStoryValuesoptions = uniqueuserStoryValues
    .filter((status) => status !== null && status !== "")
    .sort((a: any, b) => a.localeCompare(b))
    .map((status) => ({
      value: status,
      label: status,
    }));

  const uniqueuserInterfaceoptions = uniqueuserInterfaceValues
    .filter((status) => status !== null && status !== "")
    .sort((a: any, b: any) => (a.label || "").localeCompare(b.label || ""))
    .map((status) => ({
      value: status,
      label: status,
    }));

  function ApplyFilter() {
    debugger;
    let temp: any = [];

    if (filter.weekEndingDate != null) {
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (rows[i].weekEndingDate?.slice(0, 10) == filter.weekEndingDate) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.status != null) {
      temp = temp.filter((e: any) => {
        if (typeof filter.status === "string")
          return (
            e.status.trim().toLowerCase() ===
            filter.status?.trim().toLowerCase()
          );
      });
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.projectName != null) {
      temp = temp.filter((e: any) => {
        if (typeof filter.projectName === "string")
          return (
            e.projectName
              .toLowerCase()
              .search(filter.projectName?.toLowerCase()) >= 0
          );
      });
      setfilterRows(temp);
    }

    if (USfilter && USfilter.name != null) {
      temp = temp.filter((e: any) => {
        const userStory = e.userStory || "";
        return userStory
          ?.toLowerCase()
          ?.includes(USfilter?.name?.toLowerCase());
      });
      setfilterRows(temp);
    }

    if (UIfilter && UIfilter.userInterfaceName != null) {
      temp = temp.filter((e: any) => {
        const userInterface = e.userInterface || "";
        return userInterface
          .toLowerCase()
          .includes(UIfilter?.userInterfaceName?.toLowerCase());
      });
      setfilterRows(temp);
    }
  }

  function reset() {
    setfilter(FILTER);
    USsetfilter({});
    UIsetfilter({});
    // USsetfilter((prevState: any) => ({ ...prevState, name: null }));
    // UIsetfilter((prevState: any) => ({ ...prevState, userInterfaceName: null }));
    if (projectNameRef.current) projectNameRef.current.clearValue();
    if (statusRef.current) statusRef.current.clearValue();
    if (UserStoryRef?.current) UserStoryRef?.current?.ClearValue();
    if (UserInterfaceNameRef?.current)
      UserInterfaceNameRef?.current?.ClearValue();
    setfilterRows(rows);
  }
  return (
    <>
      <div>
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link color="inherit" to={`/${role}/Employee`}>
            <Typography sx={{ fontWeight: "bold" }}>Employee</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>
            Employee Dashboard
          </Typography>
        </Breadcrumbs>

        <Grid container sx={{ mt: "1%", justifyContent: "space-between" }}>
          <Grid xs={6} md={3}>
            <Typography sx={{ fontSize: "105%", marginLeft: "8%" }}>
              Employee Name :{" "}
              <span style={{ fontWeight: "bold" }}>
                {location.state?.Employeename}
              </span>
            </Typography>
          </Grid>

          <Grid xs={6} md={3}>
            <Typography sx={{ fontSize: "105%" }}>
              Team Name :{" "}
              <span style={{ fontWeight: "bold" }}>
                {data?.EmployeeDetail?.data?.teamDetails?.at(0)?.teamName}
              </span>
            </Typography>
          </Grid>

          <Grid xs={6} md={3}>
            <Typography sx={{ fontSize: "105%", marginLeft: "8%" }}>
              Joining Date:{" "}
              <span style={{ fontWeight: "bold" }}>
                {data?.EmployeeDetail?.data?.joiningDate
                  ? new Date(
                      data.EmployeeDetail.data.joiningDate
                    ).toLocaleDateString("en-GB")
                  : "N/A"}
              </span>
            </Typography>
          </Grid>
          <Grid xs={6} md={3}>
            <Typography sx={{ fontSize: "105%", float: "right", mr: "8%" }}>
              Department :{" "}
              <span style={{ fontWeight: "bold" }}>
                {location.state?.Department}
              </span>
            </Typography>
          </Grid>
        </Grid>

        <div style={{ width: "96%", marginLeft: "2%", marginTop: "1%" }}>
          <div>
            <div
              className="card mt-2 pt-2 pb-2 mb-2 text-light text-center"
              style={Accordionstyle}
            >
              <Typography sx={headersStyle}>
                Employee General Details
              </Typography>
            </div>

            <div
              className="m-2 p-3 text-dark bg-light"
              style={{ border: "3px solid #f0e4cc", marginTop: "0.5%" }}
            >
              <Grid container className="p-3">
                <Grid xs={6} md={4} lg={2.4}>
                  <div className="d-flex" style={{ justifyContent: "center" }}>
                    <div
                      className="card m-2 pt-3 text-light text-center"
                      style={{
                        width: "85%",
                        backgroundColor: "#deaff0",
                        height: "90%",
                      }}
                    >
                      <span
                        className="m-0 fs-6 fw-bold text-center"
                        style={{ color: "#032552" }}
                      >
                        Average In Time
                      </span>
                      <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                        {data?.totalattendance?.data?.averageInTime || "-"}
                      </Typography>
                    </div>
                  </div>
                </Grid>

                <Grid xs={6} md={4} lg={2.4}>
                  <div className="d-flex" style={{ justifyContent: "center" }}>
                    <div
                      className="card m-2 pt-3 text-light text-center"
                      style={{
                        width: "85%",
                        backgroundColor: "#deaff0",
                        height: "90%",
                      }}
                    >
                      <span
                        className="m-0 fs-6 fw-bold text-center"
                        style={{ color: "#032552" }}
                      >
                        Average Out Time
                      </span>
                      <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                        {data?.totalattendance?.data?.averageOutTime || "-"}
                      </Typography>
                    </div>
                  </div>
                </Grid>

                <Grid xs={6} md={4} lg={2.4}>
                  <div className="d-flex" style={{ justifyContent: "center" }}>
                    <div
                      className="card m-2 pt-3 text-light text-center"
                      style={{
                        width: "85%",
                        backgroundColor: "#deaff0",
                        height: "90%",
                      }}
                    >
                      <span
                        className="m-0 fs-6 fw-bold text-center"
                        style={{ color: "#032552" }}
                      >
                        Total No. Leaves Taken
                      </span>
                      <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                        {data?.EmployeeDetail?.data?.employeedetailsleave?.at(0)
                          ?.leaveTaken || "0"}
                      </Typography>
                    </div>
                  </div>
                </Grid>

                <Grid xs={6} md={4} lg={2.4} className="">
                  <div className="d-flex" style={{ justifyContent: "center" }}>
                    <div
                      className="card m-2 pt-3 text-light text-center"
                      style={{
                        width: "85%",
                        backgroundColor: "#deaff0",
                        height: "90%",
                      }}
                    >
                      <span
                        className="m-0 fs-6 fw-bold text-center"
                        style={{ color: "#032552" }}
                      >
                        Total No. Leaves Pending
                      </span>
                      <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                        {data?.EmployeeDetail?.data?.employeedetailsleave?.at(0)
                          ?.leavePending || "0"}
                      </Typography>
                    </div>
                  </div>
                </Grid>

                <Grid xs={6} md={4} lg={2.4}>
                  <div className="d-flex" style={{ justifyContent: "center" }}>
                    <div
                      className="card m-2 pt-3 text-light text-center"
                      style={{
                        width: "85%",
                        backgroundColor: "#deaff0",
                        height: "90%",
                      }}
                    >
                      <span
                        className="m-0 fs-6 fw-bold text-center"
                        style={{ color: "#032552" }}
                      >
                        Employees Skills
                      </span>
                      <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                        {data?.EmployeeDetail?.data?.employeeskills?.length ||
                          "0"}
                      </Typography>
                    </div>
                  </div>
                </Grid>
              </Grid>
            </div>
          </div>

          <div
            className="card mt-2 pt-2 pb-2 mb-2 text-light text-center d-flex"
            style={Accordionstyle}
          >
            <Typography sx={headersStyle}>Employee Project Details</Typography>
          </div>

          <div className="m-2 p-3">
            <div
              className="m-2 p-3 text-dark bg-light"
              style={{ border: "3px solid #deaff0", marginTop: "0.5%" }}
            >
              <div
                className="d-flex"
                style={{
                  border: "6px sloid black",
                  justifyContent: "center",
                  flexWrap: "wrap",
                }}
              >
                <div className="m-2 p-2 col-3">
                  <div
                    className="card m-1 pt-2 text-light text-center"
                    style={{
                      width: "85%",
                      backgroundColor: "#deaff0",
                      height: "90%",
                    }}
                  >
                    <span
                      className="m-0 fs-6 fw-bold text-center"
                      style={{ color: "#032552" }}
                    >
                      Project Assigned
                    </span>
                    <Typography sx={{ fontSize: "55%", color: "#032552" }}>
                      {uniqueProjects.size || "0"}
                    </Typography>
                  </div>
                </div>

                <div className="m-2 p-2 col-3">
                  <div
                    className="card m-1 pt-2 text-light text-center"
                    style={{
                      width: "85%",
                      backgroundColor: "#deaff0",
                      height: "90%",
                    }}
                  >
                    <span
                      className="m-0 fs-6 fw-bold text-center"
                      style={{ color: "#032552" }}
                    >
                      No of Tasks Assigned
                    </span>
                    <Typography sx={{ fontSize: "55%", color: "#032552" }}>
                      {NumberofTask?.length || "0"}
                    </Typography>
                  </div>
                </div>
              </div>

              <Grid container>
                <Grid xs={2} md={2} lg={1.8}>
                  <Grid>
                    <div
                      className="card m-1 pt-1 text-light text-center"
                      style={{ height: "35%", width: "85%" }}
                    >
                      <Typography sx={{ fontSize: "27%", color: "#032552" }}>
                        Pending
                      </Typography>
                    </div>
                  </Grid>
                  <Grid>
                    <div
                      className="card m-1 pt-1 text-light text-center"
                      style={{ height: "35%", width: "85%" }}
                    >
                      <Typography sx={{ fontSize: "27%", color: "#032552" }}>
                        Completed
                      </Typography>
                    </div>
                  </Grid>
                </Grid>
                <Grid xs={5} md={4} lg={2.4}>
                  <Grid>
                    <div
                      className="card m-1 text-light text-center"
                      style={{ height: "35%", width: "85%" }}
                    >
                      <Typography
                        sx={{
                          fontSize: "30%",
                          color: "black",
                          fontWeight: "bold",
                        }}
                      >
                        {countInprogress}
                      </Typography>
                    </div>
                  </Grid>
                  <Grid>
                    <div
                      className="card m-1  text-light text-center"
                      style={{ height: "35%", width: "85%" }}
                    >
                      <Typography
                        sx={{
                          fontSize: "30%",
                          color: "black",
                          fontWeight: "bold",
                        }}
                      >
                        {countCompleted}
                      </Typography>
                    </div>
                  </Grid>
                </Grid>
                <Grid xs={5} md={4} lg={2.4}>
                  <Grid>
                    <div
                      className="card m-1 text-light text-center"
                      style={{ height: "35%", width: "85%" }}
                    >
                      <Typography
                        sx={{
                          fontSize: "30%",
                          color: "black",
                          fontWeight: "bold",
                        }}
                      >
                        {NumberofTaskInprogress?.length || 0}
                      </Typography>
                    </div>
                  </Grid>
                  <Grid>
                    <div
                      className="card m-1  text-light text-center"
                      style={{ height: "35%", width: "85%" }}
                    >
                      <Typography
                        sx={{
                          fontSize: "30%",
                          color: "black",
                          fontWeight: "bold",
                        }}
                      >
                        {NumberofTaskCompleted?.length || 0}
                      </Typography>
                    </div>
                  </Grid>
                </Grid>
              </Grid>
            </div>
          </div>

          <div style={{ marginTop: "1%" }}>
            <div
              className="card mt-2 pt-2 pb-2 mb-2 text-light text-center"
              style={Accordionstyle}
            >
              <Typography sx={headersStyle}>Employee Task Details</Typography>
            </div>
            <AccordionDetails>
              <Grid container>
                <div className="well mx-auto mt-1">
                  <div className="row">
                    <div className="col-sm-6 col-md-6 col-lg-2">
                      <div className="form-group">
                        <label>Task Status</label>
                        <Select
                          id="category-simple-select"
                          isClearable={true}
                          ref={statusRef}
                          className="col mt-1"
                          onChange={(selectedOption: any) => {
                            if (selectedOption) {
                              setfilter((prevState: any) => ({
                                ...prevState,
                                status:
                                  selectedOption.label.trim() === ""
                                    ? null
                                    : selectedOption.label,
                              }));
                            }
                          }}
                          options={Statusoptions}
                          styles={{
                            menu: (provided) => ({
                              ...provided,
                              zIndex: 1000,
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="col-sm-6 col-md-6 col-lg-2">
                      <div className="form-group">
                        <label>Project</label>
                        <Select
                          id="category-simple-select"
                          isClearable={true}
                          ref={projectNameRef}
                          className="col mt-1"
                          onChange={(selectedOption: any) => {
                            setfilter((prevState: TaskFilter) => {
                              return {
                                ...prevState,
                                projectName: selectedOption
                                  ? selectedOption.value
                                  : null,
                              };
                            });
                          }}
                          options={Projects.filter(
                            (e: any) => typeof e === "object" && e.name
                          )
                            .slice()
                            .sort((a: any, b: any) =>
                              (a.name || "").localeCompare(b.status || "")
                            )
                            .map((e: any) => ({
                              value: e.name,
                              label: e.name,
                            }))}
                          styles={{
                            menu: (provided) => ({
                              ...provided,
                              zIndex: 1000,
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="col-sm-6 col-md-6 col-lg-2">
                      <div className="form-group">
                        <label>User Story</label>
                        <Select
                          id="category-simple-select"
                          isClearable={true}
                          ref={UserStoryRef}
                          className="col mt-1"
                          onChange={(selectedOption: any) => {
                            USsetfilter((prevState: any) => {
                              return {
                                ...prevState,
                                name: selectedOption
                                  ? selectedOption.value
                                  : null,
                              };
                            });
                          }}
                          options={uniqueuserStoryValuesoptions}
                          styles={{
                            menu: (provided) => ({
                              ...provided,
                              zIndex: 1000,
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="col-sm-6 col-md-6 col-lg-2">
                      <div className="form-group">
                        <label>User Interface</label>
                        <Select
                          id="category-simple-select"
                          isClearable={true}
                          ref={UserInterfaceNameRef}
                          className="col mt-1"
                          onChange={(selectedOption: any) => {
                            if (selectedOption) {
                              UIsetfilter((prevState: any) => {
                                return {
                                  ...prevState,
                                  userInterfaceName: selectedOption
                                    ? selectedOption.value
                                    : null,
                                };
                              });
                            }
                          }}
                          options={uniqueuserInterfaceoptions}
                          styles={{
                            menu: (provided) => ({
                              ...provided,
                              zIndex: 1000,
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="container">
                      <div className="row justify-content-end">
                        <div className="col-auto">
                          <Button
                            variant="contained"
                            endIcon={<SearchIcon />}
                            className="mx-3 mt-3 "
                            onClick={() => ApplyFilter()}
                          >
                            Search
                          </Button>
                          <Button
                            variant="contained"
                            endIcon={<RefreshIcon />}
                            className="mx-3 mt-3"
                            onClick={() => reset()}
                          >
                            Reset
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Grid>
              <Grid container>
                <Grid md={6} xs={12} lg={3} className="mx-auto">
                  <div
                    className="card mt-3 mb-3 m-auto text-light text-center"
                    style={{
                      backgroundColor: "#deaff0",
                      marginLeft: "2%",
                    }}
                  >
                    <span
                      className="m-0 fs-6 fw-bold text-center"
                      style={{ color: "#032552" }}
                    >
                      Actual Hours Taken
                    </span>
                    <Typography sx={{ fontSize: "55%", color: "#032552" }}>
                      {TotalActualHours || "0"}
                    </Typography>
                  </div>
                </Grid>
              </Grid>
              <Grid container sx={{ mt: "4%" }}>
                <div className="col-3 col-s-3">
                  <Box style={{ width: "94vw", position: "relative" }}>
                    <DataTable
                      columns={columns}
                      fixedHeader={true}
                      responsive
                      persistTableHead
                      progressPending={isLoading}
                      data={filterRows || []}
                      customStyles={{
                        table: {
                          style: {
                            height: "80vh",
                            border: "1px solid rgba(0,0,0,0.1)",
                            overflowY: "scroll",
                            position: "relative",
                          },
                        },

                        headRow: {
                          style: {
                            background: "#1e97e8",
                            fontSize: "16px",
                            color: "white",
                            fontFamily: "inherit",
                          },
                        },
                        pagination: {
                          style: {
                            position: "absolute",
                            width: "94vw",
                            background: "#daeef0",
                            color: "black",
                            textAlign: "right",
                            top: -55,
                            borderRadius: "5px 5px 0 0",
                          },
                        },
                      }}
                      pagination
                      paginationPerPage={50}
                      paginationRowsPerPageOptions={[50, 100, 200]}
                      pointerOnHover={true}
                    />
                  </Box>
                </div>
              </Grid>
            </AccordionDetails>
          </div>
        </div>
      </div>
      <BackDrop open={isLoading} />
    </>
  );
};
