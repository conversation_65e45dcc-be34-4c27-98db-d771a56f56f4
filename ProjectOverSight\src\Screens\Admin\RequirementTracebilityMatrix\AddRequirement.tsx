import { useEffect, useRef, useState } from "react";
import {
  Grid,
  Alert,
  FormControl,
  TextField,
  TextareaAutosize,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Autocomplete,
} from "@mui/material";
import { Get, Post } from "../../../Services/Axios";
import { useForm } from "react-hook-form";
import { Regex } from "../../../Constants/Regex/Regex";
import AddIcon from "@mui/icons-material/Add";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import Swal from "sweetalert2";
import { useContextProvider } from "../../../CommonComponents/Context";
import { CommonMaster } from "../../../Models/Common/CommonMaster";
import { AddRequirementType } from "./AddRequirementType";

const formField = [
  "RequirementSource",
  "RequirementType",
  "RequirementDefinition",
  "SourceDocumentSpecification",
  "CodeFileName",
  "ProjectName",
  "Stages",
  "Status",
  "EstimatedReleaseDate",
  "ActualReleaseDate",
  "Design",
  "Development",
  "Testing",
  "ProjectObjectiveIds",
  "Department",
  "category",
];

const AddRequirement = ({
  openDialog,
  setOpenDialog,
  setReload,
}: // data,
any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [save, setSave] = useState<boolean>(false);
  const [Projects, setProjects] = useState<any>([]);
  const [requirementtype,setrequirementtype] = useState<any>([]);
  const [ProjectId, setprojectId] = useState<number>(-1);
  const { commonMaster } = useContextProvider();
  const subCategoryRef = useRef<any>(null);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  const [open, setOpen] = useState<any>({ add: false });

  function reset() {
    formField.map((e: any) => {
      resetField(e);
    });
  }
  

  const handleSave = async (data: any) => {
    debugger
    setSave(true);
    data.Stages = "Design";
    data.Status = "In Progress";
    data.Development = "In Progress";
    data.Testing = "In Progress";
    data.Design = "In Progress";
    data.ProjectId = ProjectId;
    data.EstimatedReleaseDate = data.EstimatedReleaseDate? data.EstimatedReleaseDate : null;

    const response: any = await Post("app/Requirement/AddRequirements", data);
    var option: AlertOption;
    if (response.error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Requirement Added Successfully!",
        icon: "success",
      };
    }
    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ add: false });
  };

  const handleProjectChange = async (e: any, selectedOption: any) => {
    if (!selectedOption || !selectedOption.id || !e) {
      console.error("Selected option is null or undefined");
      return;
    }

    const { id } = selectedOption;
    setSelectedProject(selectedOption);
    setprojectId(id);
  };

  useEffect(() => {
    async function getProject() {
      let projectGet: any = await Get(`app/Project/GetProjectList`);
      setProjects(projectGet.data);
    }
    getProject();
  }, []);

  useEffect(() => {
    if (selectedProject && selectedProject.id) {
        const fetchRequirementType = async () => {
            let Getrequirementtype: any = await Get(`app/Requirement/GetRequirementType?ProjectId=${selectedProject.id}`);
            setrequirementtype(Getrequirementtype?.data);
        };
        fetchRequirementType();
    }
}, [selectedProject,setReload,open]);



  return (
    <div>
      <Dialog open={openDialog?.add}>
        <form onSubmit={handleSubmit(handleSave)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Add Requirements
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <Autocomplete
                    id="project-name"
                    options={Projects? Projects.map((project: any) => ({
                      label: project?.name,
                      id: project?.id,
                    })) : []}
                    onChange={handleProjectChange}
                    renderInput={(params: any) => {
                      return (
                        <TextField
                          {...params}
                          {...register("ProjectName")}
                          label="Project Name"
                          variant="outlined"
                          required
                        />
                      );
                    }}
                  />
                </FormControl>
              </Grid>
              {/* <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    required
                    label="Requirement Type"
                    variant="outlined"
                    {...register("RequirementType")}
                  />
                </FormControl>
              </Grid> */}
               <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    {...register("RequirementSource")}
                    label="Requirement Source"
                    onChange={(e: any) => {
                      e.target.value = e.target.value.replace(
                        Regex.CHAR_SPACE,
                        ""
                      );
                    }}
                    type="text"
                    variant="outlined"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    label="Source Document"
                    type="text"
                    variant="outlined"
                    {...register("SourceDocument")}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    label="RequirementNumberAsPerSourceDocument"
                    type="Number"
                    variant="outlined"
                    {...register("RequirementNumberAsPerSourceDocument")}
                    onChange={(e: any) => {
                      e.target.value = e.target.value.repalce(
                        Regex.NUMBER,
                        ""
                      );
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <Autocomplete
                  options={requirementtype?.length > 0 ? (requirementtype?.map((objective: any) => ({
                    label: objective?.requirementsType,
                    id: objective?.id,
                  }))): []}
                  // options={[]}
                  {...register("RequirementType")}
                  // onChange={(e: any, value: any) => {
                  //   if (value || e){
                  //     const ids = value.map((obj: any) => obj.requirementsType);
                  //     setIds(ids);
                  //   }
                  // }}
                  renderInput={(params: any) => (
                    <TextField
                      {...params}
                      label="Requirement Type"
                      variant="outlined"
                      {...register("RequirementType")}
                    />
                  )}
                />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <Button
                disabled={!selectedProject?.id}
                variant="contained"
                onClick={() => setOpen({ add: true })}
              >
                Add Requirement Type
                <AddIcon className="mx-1" />
              </Button>
              </FormControl>
            </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <TextareaAutosize
                    required
                    className="form-control"
                    placeholder="Requirement Definition"
                    style={{ width: "100%", height: 100 }}
                    {...register("RequirementDefinition")}
                    onChange={(e: any) => {
                      e.target.value = e.target.value.replace(
                        Regex.CHAR_NUM,
                        ""
                      );
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <Autocomplete
                    id="Requirement"
                    options={commonMaster
                      ?.filter(
                        (x: CommonMaster) => x.codeType === "Requirement"
                      )
                      .filter(
                        (obj: any, index: number, self: any) =>
                          index ===
                          self.findIndex(
                            (t: any) => t.codeName === obj.codeName
                          )
                      )
                      .map((master: CommonMaster) => ({
                        label: master?.codeName,
                        id: master?.id,
                      }))}
                    // onChange={(e: any, value: any) =>
                    //   handleCategoryChange(e, value)
                    // }
                    renderInput={(params: any) => {
                      return (
                        <TextField
                          {...params}
                          {...register("Category")}
                          label="Requirement Category"
                          variant="outlined"
                          required
                        />
                      );
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <Autocomplete
                    ref={subCategoryRef} // Assign the ref directly here
                    fullWidth
                    options={[
                      {
                        label: "New Application",
                        value: "New Application",
                      },
                      {
                        label: "Renewal",
                        value: "Renewal",
                      },
                      {
                        label: "Review",
                        value: "Review",
                      },
                      {
                        label: "Seek clarification",
                        value: "Seek clarification",
                      },
                      {
                        label: "Administration",
                        value: "Administration",
                      },
                      {
                        label: "Others",
                        value: "Others",
                      },
                    ]}
                    renderInput={(params: any) => (
                      <TextField
                        {...params}
                        {...register("SubCategory")}
                        label="Requirement Sub Category"
                        variant="outlined"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              {/* <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <FileUpload
                    uploadedFiles={uploadedFiles}
                    setUploadedFiles={setUploadedFiles}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>ss
                  <TextField
                    required
                    label="Code File Name"
                    variant="outlined"
                    {...register("CodeFileName")}
                  />
                </FormControl>
              </Grid> */}
              {/* <Grid item xs={6}>
                <div className="col-6 w-100">
                  <div className="">
                    <Autocomplete
                      multiple
                      fullWidth
                      options={
                        (ProjectObjectives &&
                          ProjectObjectives.length > 0 &&
                          ProjectObjectives?.map((objective: any) => ({
                            label: objective?.description,
                            id: objective?.id,
                          }))) ||
                        []
                      }
                      {...register("ProjectObjectiveIds")}
                      onChange={(e: any, value: any) => {
                        if (value) var ids = [];
                        value.map((e: any) => {
                          ids.push(e.id);
                        });
                        setId(ids);
                        return e;
                      }}
                      renderInput={(params: any) => (
                        <TextField
                          {...params}
                          label="Select Objectives"
                          variant="outlined"
                        />
                      )}
                    />
                  </div>
                </div>
              </Grid> */}
              {/* <Grid container spacing={4}> */}
              {/* <Grid item xs={6}>
                  <Button
                    variant="contained"
                    className="mt-1 col w-100 mt-3"
                    onClick={() => setOpen({ add: true })}
                  >
                    Add Project Objective
                    <AddIcon className="mx-1" />
                  </Button>
                </Grid> */}
              {/* </Grid> */}
            <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    type="date"
                    label="Release Date"
                    variant="outlined"
                    {...register("EstimatedReleaseDate")}
                    InputLabelProps={{ shrink: true }}
                  />
                </FormControl>
              </Grid>
              {/* <Grid item xs={6}>
                <FormControl fullWidth className="mt-2">
                  <InputLabel id="Is-Active">Is Active</InputLabel>
                  <Select
                    labelId="is-active"
                    required
                    label="Is Active"
                    {...register("isActive", {
                      onChange: (e: any) => {
                        // setSelCat(e.target.value);
                        setErrorMsg({
                          message: "",
                          show: false,
                        });
                      },
                    })}
                  >
                    <MenuItem value={"Yes"}>Yes</MenuItem>
                    <MenuItem value={"No"}>No</MenuItem>
                  </Select>
                </FormControl>
              </Grid> */}
            </Grid>
            <div className="row">
              <input {...register("CreatedBy")} value="user" hidden />
              <input {...register("UpdatedBy")} value="user" hidden />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              color="success"
              type="submit"
              disabled={save}
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <AddRequirementType
        openDialog={open}
        setOpenDialog={setOpen}
        ProjectId={ProjectId}
        SetReload={setReload}
      />
    </div>
  );
};

export default AddRequirement;
