import { Employee } from "../Employee/Employee"
import { ProjectGroupMapper } from "../Project/ProjectGroup"
import { UserInterface } from "../Project/UserInterface"
import { TeamProject } from "../Team/TeamProject"

export type AssignmentMapper = {
    assignedList: Array<EmployeeProject | UserInterface | TeamProject | ProjectGroupMapper>,
    unassignedList:Array<EmployeeProject | UserInterface | TeamProject | ProjectGroupMapper>,
}

export { ProjectGroupMapper }
