.img-slider-img {
    /* object-fit: cover; */
    width: 60rem;
    height: 58vh;
    display: block;
    flex-shrink: 0;
    flex-grow: 0;
}

.img-slider-btn {
    /* all: unset; */
    display: block;
    position: absolute;
    top: 5;
    bottom: 0;
    padding: 0.5rem;
    cursor: pointer;
    transition:100ms ease-in-out;
    color: white;
}

.img-slider-btn:hover,
.img-slider-btn:focus-visible {
    transform: scale(0.9);
    color: white;
}

.img-slider-btn>* {
    stroke: white;
    fill: black;
    width: 2rem;
    height: 2rem;
}

@keyframes squish {
    50% {
        scale: 1.4 0.6;
    }
}

.img-slider-dot-btn {
    all: unset;
    display: block;
    cursor: pointer;
    width: 1rem;
    height: 1rem;
    transition: scale 100ms ease-in-out;
}

.img-slider-dot-btn:hover,
.img-slider-dot-btn:focus-visible {
    scale: 1.2;
}

.img-slider-dot-btn>* {
    stroke: white;
    fill: black;
    height: 100%;
    width: 100%;
}

.img-slider-dot-btn:focus-visible,
.img-slider-btn:focus-visible {
    outline: auto;
}

.skip-link {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    border: 0;
    clip: rect(0, 0, 0, 0);
}

.skip-link:focus-visible {
    top: 0;
    left: 0;
    border: 1px solid black;
    background-color: white;
    padding: 0.5rem;
    width: auto;
    height: auto;
    margin: 0;
    clip: unset;
    text-decoration: none;
    color: black;
    z-index: 100;
}

@media not (prefers-reduced-motion) {
    .img-slider-img {
        transition: translate 300ms ease-in-out;
    }

    .img-slider-btn:hover>*,
    .img-slider-btn:focus-visible>* {
        animation: squish 200ms ease-in-out;
    }
}

.add-task-modal{
    width: 70rem;
    height: 35rem;
    position: relative;
    right: 15rem;
}

.userStory-container{
    height: 20.8rem;
}

.userStory{
    height: 16.8rem;
    margin:2rem;
    background-color: rgb(216, 232, 232);
}