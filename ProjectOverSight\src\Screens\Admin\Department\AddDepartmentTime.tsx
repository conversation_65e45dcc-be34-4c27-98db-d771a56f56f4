import {
  <PERSON>ert,
  <PERSON><PERSON>,
  <PERSON>ton,
  InputLabel,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Get, Post } from "../../../Services/Axios";
import Swal from "sweetalert2";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../Models/Common/AlertOptions";

const formField = ["Category", "Department", "StartTime", "EndTime"];

export const AddDepartmentTime = ({
  openDialog,
  setOpenDialog,
  setReload,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [save, setSave] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  const CategoryRef = useRef<any>(null);
  const [categories, setcategories] = useState<any>([]);
  const [subCategories, setSubCategories] = useState<any>([]);
  const subCategoryRef = useRef<any>(null);

  const [userDto, setUserDto] = useState<any>({
    Department: "",
    Category: "",
  });
  const [modelError, setModelError] = useState<any>({
    Department: false,
    Category: false,
  });

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const onSubmitHandler = async (data: any) => {
    setSave(true);
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }

    const { error }: any = await Post(
      "app/DepartmentTime/AddDepartmentTime",
      data
    );
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "DepartmentTime Added Successfully!",
        icon: "success",
      };
    }
    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ add: false });
  };

  var Category: string[] = [];
  var categorySet = new Set<any>();
  useEffect(() => {
    let categoriesList = Get("app/CommonMaster/GetCodeTableList");
    categoriesList.then((response: any) => {
      var category = response?.data?.filter(
        (x: any) => x.codeType === "EmployeeCategory"
      );
      setcategories(category || []);
    });
  }, []);

  categories?.forEach((element: any) => {
    categorySet.add(element.codeName);
  });

  Category = [...categorySet];

  const handleCategoryChange = (event: any) => {
    if (subCategoryRef.current) subCategoryRef.current.value = "";
    const subCategories = categories.filter(
      (element: any) => element.codeName === event.target.value
    );
    setSubCategories(subCategories);
  };

  return (
    <div>
      <Dialog open={openDialog?.add} onClose={handleClose}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Add Team
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <div className="row mx-auto">
              <FormControl style={{ width: "40%" }}>
                <label>Category</label>
                <Select
                  id="Category-name"
                  className="col m-2"
                  required
                  inputRef={CategoryRef}
                  error={modelError?.Category}
                  onChange={(e: any) => {
                    handleCategoryChange(e);
                    setModelError({ ...modelError, Category: false });
                    setUserDto({ ...userDto, Category: e.target.value });
                  }}
                >
                  {Category.map((option: any) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl style={{ width: "40%" }}>
                <label>Department</label>
                <Select
                  labelId="department"
                  className="col m-2"
                  required
                  label="Department"
                  inputRef={subCategoryRef}
                  error={modelError?.Department}
                  onChange={(e: any) => {
                    setModelError({ ...modelError, Department: false });
                    setUserDto({
                      ...userDto,
                      Department: e.target.value,
                    });
                  }}
                >
                  {subCategories.map((option: any) => (
                    <MenuItem key={option.codeValue} value={option.codeValue}>
                      {option.codeValue}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>
            <div className="row col-md-8">
              <InputLabel id="Team-Name">Start Date</InputLabel>
              <TextField
                required
                {...register("startTime")}
                id="outlined-basic"
                label="Start Time"
                className="col m-2"
                {...register("StartDate")}
                type="date"
                variant="outlined"
              />
            </div>
            <div className="row col-md-8">
              <InputLabel id="Team-Name">End Date</InputLabel>
              <TextField
                required
                {...register("endTime")}
                id="outlined-basic"
                label="End Time"
                className="col m-2"
                {...register("EndDate")}
                type="date"
                variant="outlined"
              />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              color="success"
              type="submit"
              disabled={save}
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
          <input {...register("CreatedBy")} value="user" hidden />
          <input {...register("UpdatedBy")} value="user" hidden />
        </form>
      </Dialog>
    </div>
  );
};
