import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Grid,
  Button,
  Tooltip,
  FormControl,
  Autocomplete,
  TextField,
} from "@mui/material";
import { useEffect, useRef, useState, lazy } from "react";
import { Link, useLocation } from "react-router-dom";
import DataTable from "react-data-table-component";
import AddIcon from "@mui/icons-material/Add";
import Box from "@mui/material/Box";
import DownloadIcon from "@mui/icons-material/Download";
import { ConvertDate } from "../../../../Utilities/Utils";
import { Get, Post } from "../../../../Services/Axios";
import SearchIcon from "@mui/icons-material/Search";
import Select from "react-select";
import RefreshIcon from "@mui/icons-material/Refresh";
import { UserStory } from "../../../../Models/Project/UserStory";
import { DownloadUserStoryList } from "../../../../Services/ProjectService";
const AddUserStory = lazy(() => import("./AddUserStory"));
import { EditUserStory } from "./EditUserStory";
import { ViewUserStory } from "./ViewUserStory";
import { useQuery } from "react-query";
import VisibilityIcon from "@mui/icons-material/Visibility";
import EditIcon from "@mui/icons-material/Edit";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import AssignmentTurnedInIcon from "@mui/icons-material/AssignmentTurnedIn";
import Modal from "@mui/material/Modal";
import * as React from "react";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import DeleteIcon from "@mui/icons-material/Delete";
import StepConnector, {
  stepConnectorClasses,
} from "@mui/material/StepConnector";
import { StepIconProps } from "@mui/material/StepIcon";
import BackDrop from "../../../../CommonComponents/BackDrop";
import ChecklistIcon from "@mui/icons-material/Checklist";
import BookmarkAddedIcon from "@mui/icons-material/BookmarkAdded";
import RuleIcon from "@mui/icons-material/Rule";

const style = {
  position: "absolute" as "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  height: 700,
  width: 600,
  bgcolor: "White",
  border: "2px solid #000",
  boxShadow: 24,
  p: 4,
};
import { useContextProvider } from "../../../../CommonComponents/Context";
import { ADMIN } from "../../../../Constants/Common/Roles";
import { USER_STORY } from "../../../../Constants/UserStory/UserStory";
import { COMPLETED, PENDING } from "../../../../Constants/Common/Common";
import Swal from "sweetalert2";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import { Project } from "../../../../Models/Project/Project";
import PendingActionsIcon from "@mui/icons-material/PendingActions";
import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";

const ColorlibStepIconRoot = styled("div")<{
  ownerState: { completed?: boolean; active?: boolean };
}>(({ theme, ownerState }) => ({
  backgroundColor:
    theme.palette.mode === "dark" ? theme.palette.grey[700] : "#ccc",
  zIndex: 1,
  color: "#fff",
  width: 50,
  height: 50,
  display: "flex",
  borderRadius: "50%",
  justifyContent: "center",
  alignItems: "center",
  ...(ownerState.active && {
    backgroundImage:
      "linear-gradient( 136deg, rgb(242,113,33) 0%, rgb(233,64,87) 50%, rgb(138,35,135) 100%)",
    boxShadow: "0 4px 10px 0 rgba(0,0,0,.25)",
  }),
  ...(ownerState.completed && {
    backgroundImage:
      "linear-gradient( 136deg, rgb(242,113,33) 0%, rgb(233,64,87) 50%, rgb(138,35,135) 100%)",
  }),
}));

function ColorlibStepIcon(props: StepIconProps) {
  const { active, completed, className } = props;

  const icons: { [index: string]: React.ReactElement } = {};

  return (
    <ColorlibStepIconRoot
      ownerState={{ completed, active }}
      className={className}
    >
      {icons[String(props.icon)]}
    </ColorlibStepIconRoot>
  );
}

const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 22,
    left: 125,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage:
        "linear-gradient( 95deg,rgb(242,113,33) 0%,rgb(233,64,87) 50%,rgb(138,35,135) 100%)",
      Width: 10,
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage:
        "linear-gradient( 95deg,rgb(242,113,33) 0%,rgb(233,64,87) 50%,rgb(138,35,135) 100%)",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 65,
    width: 10,
    border: 0,
    backgroundColor:
      theme.palette.mode === "dark" ? theme.palette.grey[800] : "#eaeaf0",
    borderRadius: 1,
  },
}));

export type UserStorysdetails = {
  name: string;
  description: string;
  status: string;
  percentage: number;
};

const steps = ["Business Analysis", "Development", "QA", "UAT", "Production"];

export const UserStoryList = () => {
  const [open, setOpen] = React.useState(false);
  const handleClose = () => setOpen(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [reload, setReload] = useState<boolean>(true);
  const location = useLocation();
  const { projectReportRoute, status } = location.state;
  const [filter, setfilter] = useState<UserStory>(USER_STORY);
  const [rows, setRows] = useState<any>([]);
  const [userStorydata, setUserStorydata] = useState<any>();
  const [filterRows, setfilterRows] = useState<any>([]);
  const usNameRef = useRef<HTMLInputElement>(null);
  const statusRef = useRef<any>();
  const userStoryRef = useRef<any>();
  const percentageRef = useRef<HTMLInputElement>(null);
  const actStartDateRef = useRef<HTMLInputElement>(null);
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const descriptionRef = useRef<HTMLInputElement>(null);
  const { role } = useContextProvider();
  const [UserStorylist, SetUserStorylist] = useState<any>([]);
  const [userStoryView, setUserStoryView] = useState<any>({
    view: false,
    edit: false,
    add: false,
    assign: false,
  });
  const [projects, setProjects] = useState([]);
  const [Project, setProject] = useState<any>({
    ProjectId: location.state.projectId,
    ProjectName: location.state.projectName,
  });

  const activeStep = 0;

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      type: "Date",
      width: "15rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: UserStory) => {
        return (
          <>
            <Tooltip
              title="View"
              className="mx-2 bg-light"
              sx={{ zIndex: 99 }}
              onClick={() => {
                setUserStoryView({ view: true });
                setUserStorydata(row);
              }}
            >
              <VisibilityIcon className="fs-4 text-info" sx={{ zIndex: 99 }} />
            </Tooltip>
            {role === ADMIN && (
              <>
                <Tooltip
                  title="Edit"
                  className="mx-2"
                  onClick={() => {
                    setUserStoryView({ edit: true });
                    setUserStorydata(row);
                  }}
                >
                  <EditIcon className="fs-4 text-warning" />
                </Tooltip>
                <Tooltip title="Delete" className="mx-2">
                  <DeleteIcon
                    className="fs-4 text-danger"
                    onClick={() => {
                      row.isActive = false;
                      row.projectObjectiveIds = [];
                      deleteUserStory(row);
                    }}
                  />
                </Tooltip>
                <Link
                  className="mx-2"
                  to="/Admin/ManageRequirement"
                  state={{
                    ...location?.state,
                    UserStoryName: row.name,
                    UserStoryId: row.id,
                    projectId: Project.ProjectId,
                    projectName: Project.ProjectName,
                  }}
                  style={{ textDecoration: "none" }}
                >
                  <Tooltip title="Manage Requirement">
                    <BookmarkAddedIcon className="fs-4 text-info" />
                  </Tooltip>
                </Link>
              </>
            )}
            {role === ADMIN && !projectReportRoute && (
              <Link
                className="mx-2"
                to="/Admin/AssignUI"
                state={{
                  ...location?.state,
                  UserStoryName: row.name,
                  UserStoryId: row.id,
                  projectId: Project.ProjectId,
                  projectName: Project.ProjectName,
                }}
                style={{ textDecoration: "none" }}
              >
                <Tooltip title="Assign UI">
                  <AssignmentTurnedInIcon className="fs-4 text-primary" />
                </Tooltip>
              </Link>
            )}
          </>
        );
      },
    },
    {
      field: "name",
      name: "Name",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => (
        <Tooltip title={row.name}>
          {!projectReportRoute ? (
            <Link
              className="tableStyle"
              to={`/${role}/UserInterface`}
              state={{
                USid: row.id,
                UserStoryName: row.name,
                projectId: row?.projectId,
                projectName: location.state.projectName,
              }}
              style={{ textDecoration: "none" }}
            >
              {row.name}
            </Link>
          ) : (
            <p className="tableStyle">{row.name}</p>
          )}
        </Tooltip>
      ),
    },
    {
      field: "description",
      name: "Description",
      width: "30rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => (
        <Tooltip title={row.description}>
          <p className="tableStyle">{row.description}</p>
        </Tooltip>
      ),
    },
    {
      field: "status",
      name: "Status",
      width: "8rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.status}</p>,
    },
    {
      field: "percentage",
      name: "Percentage",
      type: "number",
      width: "10rem",
      align: "right",
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      right: true,
      selector: (row: any) => <p className="tableStyle">{row.percentage}</p>,
    },
    {
      field: "startDate",
      name: "Start Date",
      type: "Date",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => {
        const result = ConvertDate(row.startDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
    {
      field: "endDate",
      name: "End Date",
      type: "Date",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => {
        const result = ConvertDate(row.endDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
  ];

  async function fetchData() {
    setLoading(true);
    let userStoryList: any = await Get(
      `app/Project/GetUserStoryList?projectId=${Project.ProjectId}`
    );

    setRows(userStoryList?.data || []);
    SetUserStorylist(userStoryList?.data || []);

    if (projectReportRoute) {
      if (status === COMPLETED) {
        setfilterRows(
          userStoryList?.data?.filter((x: UserStory) => x.percentage === 100) ||
          []
        );
      } else if (status === PENDING) {
        setfilterRows(
          userStoryList?.data?.filter(
            (x: UserStory) => parseInt(`${x.percentage}`) < 100
          ) || []
        );
      } else {
        setfilterRows(userStoryList?.data || []);
      }
    } else {
      setfilterRows(userStoryList?.data || []);
    }

    const response: any = await Get("app/Project/GetProjectList");
    setProjects(response.data || []);
    setLoading(false);
  }
  const { data, refetch } = useQuery("AddUserStory", () => {
    var objective: any = Get(
      `app/Project/GetProjectObjective?ProjectId=${Project.ProjectId}`
    );
    return objective;
  });

  useEffect(() => {
    fetchData();
  }, [reload]);

  const handleClickOpen = () => {
    setUserStoryView({ add: true });
  };

  const handlePendingClick = (statusfilter: string) => {
    const filteredRow = rows.filter(
      (e: any) => e.status.toLowerCase() === statusfilter.toLowerCase()
    );
    setfilterRows(filteredRow);
  };

  async function deleteUserStory(userStory: UserStory) {

    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        debugger
        // userStory.documents = userStory?.documents?.map((e:any) => {
        //   (e.file == null) ? '': e.file 
        // });
        const { error }: any = await Post(
          "app/Project/UpdateUserStory",
          userStory
        );
        var option: AlertOption;
        if (error) {
          option = {
            title: "Error",
            text: "Error Occured While Deleting!",
            icon: "error",
          };
        } else {
          option = {
            title: "Success",
            text: "User Story has been deleted!",
            icon: "success",
          };
        }
        Swal.fire({
          ...option,
          confirmButtonColor: "#3085d6",
        });
        setReload((prev: boolean) => !prev);
      }
    });
  }

  function ApplyFilter() {
    let temp: any = rows;

    if (filter.startDate != null) {
      if (filter.endDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].startDate.slice(0, 10) >= filter.startDate &&
          rows[i].endDate.slice(0, 10) <= filter.endDate
        ) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.name != null) {
      temp = temp.filter((e: any) => {
        return e.name.toLowerCase().search(filter.name?.toLowerCase()) >= 0;
      });
      setfilterRows(temp);
    }

    if (filter.status != null) {
      temp = temp.filter((e: any) => {
        return e.status.toLowerCase() === filter.status?.toLowerCase();
      });
      setfilterRows(temp);
    }

    if (filter.percentage != null) {
      temp = temp.filter((e: any) => {
        return e.percentage === Number(filter.percentage);
      });
      setfilterRows(temp);
    }
  }

  function reset() {
    setfilter(USER_STORY);
    if (usNameRef.current) usNameRef.current.value = "";
    if (statusRef.current) statusRef.current.clearValue();
    if (percentageRef.current) percentageRef.current.value = "";
    if (userStoryRef.current) userStoryRef.current.clearValue();
    if (actStartDateRef.current) actStartDateRef.current.value = "";
    if (actEndDateRef.current) actEndDateRef.current.value = "";
    if (descriptionRef.current) descriptionRef.current.value = "";
    setfilterRows(rows);
  }

  return (
    <div>
      {!projectReportRoute && (
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link to={`/${role}/Project`}>
            <Typography sx={{ fontWeight: "bold" }}>Project</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>User Story</Typography>
        </Breadcrumbs>
      )}

      {projectReportRoute && (
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link color="inherit" to={`/${role}/Project`}>
            <Typography sx={{ fontWeight: "bold" }}>Projects</Typography>
          </Link>
          <Link
            color="inherit"
            to={`/${role}/ProjectQuadrant`}
            state={{ ...location.state }}
          >
            <Typography sx={{ fontWeight: "bold" }}>
              Project Quadrant
            </Typography>
          </Link>
          <Link
            color="inherit"
            to={`/${role}/ProjectQuadrant/ProjectReport`}
            state={{ ...location.state }}
          >
            <Typography sx={{ fontWeight: "bold" }}>Project Report</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>User Story</Typography>
        </Breadcrumbs>
      )}
      <div className="row">
      <div
          className="attce d-flex mt-3 m-auto flex-wrap "
          style={{ width: "80%", justifyContent: 'space-evenly' }}
        >
        <div className="col-4">
          <FormControl className="col-md-6 " >
            <Autocomplete
              options={projects
                .sort((a: Project, b: Project) =>
                  a.name!.localeCompare(b.name!)
                )
                .map((project: Project) => ({
                  label: project.name,
                  id: project.id,
                }))}
              onChange={(e: any, value: any) => {
                if (value)
                  setProject({
                    ProjectId: value.id,
                    ProjectName: value.label,
                  });
                setReload((prev) => !prev);
                return e;
              }}
              renderInput={(params: any) => (
                <TextField
                  {...params}
                  label="Select Project"
                  variant="filled"
                  required
                />
              )}
            />
          </FormControl>
        </div>
        <div className="col-8">
          <Typography
            align="center"
            className="fw-bolder fs-3"
            sx={{ float: "left", marginTop: "20px" }}
          >
            Project Name: {Project.ProjectName}
          </Typography>
        </div>
        </div>
      </div>
      <div className="row">
        <div
          className="attce d-flex mt-3 m-auto flex-wrap "
          style={{ width: "80%", justifyContent: 'space-evenly' }}
        >
          <div className="shadow d-flex align-items-center mt-2 px-4" onClick={() => reset()}>
            <div className="container">
              <div className="row justify-content-center">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#1f59c4",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-12"
                >
                  Total
                </div>
                <div className="col-md-4 col-sm-4 col-6 d-flex justify-content-center pt-2">
                  <FormatListBulletedIcon color="primary" sx={{ fontSize: "255%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-6 align-items-center px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                  }}
                >
                  {rows?.length}
                </div>
              </div>
            </div>
          </div>
          <div className="shadow d-flex align-items-center mt-2" onClick={() => handlePendingClick("Completed")}>
            <div className="container">
              <div className="row justify-content-center">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#48e022",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-12"
                >
                  Completed
                </div>
                <div className="col-md-4 col-sm-4 col-6 d-flex justify-content-center pt-2">
                  <ChecklistIcon color="success" sx={{ fontSize: "225%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-6 align-items-center px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                  }}
                >
                  {rows.filter((e: any) => e.status === "Completed").length ||
                    "0"}
                </div>
              </div>
            </div>
          </div>
          <div className="shadow d-flex align-items-center mt-2" onClick={() => handlePendingClick("In Progress")}>
            <div className="container">
              <div className="row justify-content-center">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#d16e1d",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-12"
                >
                  In Progress
                </div>
                <div className="col-md-4 col-sm-4 col-6 d-flex justify-content-center pt-2">
                  <RuleIcon color="warning" sx={{ fontSize: "225%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-6 align-items-center px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                  }}
                >
                  {rows.filter((e: any) => e.status === "In Progress")
                    .length || "0"}
                </div>
              </div>
            </div>
          </div>
          <div className="shadow d-flex align-items-center mt-2 px-4" onClick={() => handlePendingClick("pending")}>
            <div className="container">
              <div className="row justify-content-center">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#1f59c4",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-12"
                >
                  Pending
                </div>
                <div className="col-md-4 col-sm-4 col-6 d-flex justify-content-center pt-2">
                  <PendingActionsIcon color="error" sx={{ fontSize: "225%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-6 align-items-center px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                  }}
                >
                  {rows.filter((e: any) => e.status === "Pending").length ||
                    "0"}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="well mx-auto mt-4 ">
        <div className="row">
          <div className="col-sm-2">
            <div className="form-group">
              <label>User Story</label>
              <Select
                aria-label="Floating label select example"
                name="UserStory"
                ref={userStoryRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      name: code ? code.value : null,
                    };
                  });
                }}
                options={UserStorylist?.map((opt: any) => ({
                  label: opt?.name,
                  value: opt?.name,
                }))}
                placeholder="User Story"
                isSearchable={true}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    zIndex: 999,
                  }),
                  menu: (provided) => ({
                    ...provided,
                    maxHeight: 300,
                    zIndex: 999,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                aria-label="Floating label select example"
                name="status"
                ref={statusRef}
                className="select-dropdowns mt-1 col"
                onInputChange={(inputValue: string) => {
                  const alphabeticValue = inputValue.replace(
                    /[^A-Za-z\s]/g,
                    ""
                  );
                  return alphabeticValue;
                }}
                onChange={(selectedOption: any) => {
                  if (selectedOption) {
                    setfilter((prevState) => ({
                      ...prevState,
                      status:
                        selectedOption.label.trim() === ""
                          ? null
                          : selectedOption.label,
                    }));
                  }
                }}
                options={[
                  {
                    label: "Completed",
                    value: "Completed",
                  },
                  {
                    label: "In Progress",
                    value: "In Progress",
                  },
                  {
                    label: "Pending",
                    value: "Pending",
                  },
                ]}
                placeholder="Status"
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Percentage</label>
              <input
                id="percentage"
                placeholder="Percentage"
                className="m-1 form-control col"
                ref={percentageRef}
                onChange={(e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  setfilter((prevState) => ({
                    ...prevState,
                    percentage: parseInt(e.target.value),
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label className="mx-1">Start Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      startDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="start-date"
                placeholder="Start Date"
                ref={actStartDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label className="mx-1">End Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      endDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="end-date"
                placeholder="End Date"
                ref={actEndDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="container">
            <div className="row justify-content-end">
              <div className="col-auto">
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => ApplyFilter()}
                >
                  search
                </Button>
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => reset()}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center mt-4">
        <div className="col-11 col-s-4">
          <Grid>
            {role === ADMIN && (
              <Button
                variant="contained"
                className="mb-2 float-md-start"
                onClick={handleClickOpen}
                sx={{ ml: "3%" }}
              >
                Add User Story
                <AddIcon className="mx-1" />
              </Button>
            )}
            <Button
              variant="contained"
              className="mb-2 float-md-end"
              onClick={() => {
                DownloadUserStoryList(filterRows);
              }}
              sx={{ mr: "3%" }}
            >
              Download
              <DownloadIcon className="mx-1" />
            </Button>
          </Grid>
        </div>
        <Grid item xs={12} sm={11} sx={{ mt: "4%" }}>
          <div className="responsive-div">
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                fixedHeader={true}
                responsive
                persistTableHead
                progressPending={loading}
                data={filterRows || []}
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0, 0, 0, 0.1)",
                      overflowY: "scroll",
                    },
                  },
                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                      position: "sticky",
                      left: 10,
                      top: 0,
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
              />
              <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
              >
                <Box sx={style}>
                  <Typography
                    id="modal-modal-title"
                    variant="h6"
                    component="h2"
                    sx={{ textAlign: "center", color: "blue" }}
                  >
                    User Story Stages
                  </Typography>
                  <Box sx={{ maxWidth: 900, mt: 2 }}>
                    <Stepper
                      activeStep={activeStep}
                      connector={<ColorlibConnector />}
                      orientation={steps.length < 2 ? "horizontal" : "vertical"}
                    >
                      {steps.map((label) => (
                        <Step key={label}>
                          <StepLabel StepIconComponent={ColorlibStepIcon}>
                            {label}
                          </StepLabel>
                        </Step>
                      ))}
                    </Stepper>
                    {activeStep === steps.length && (
                      <Paper square elevation={4} sx={{ p: 3 }}>
                        <Typography>
                          All steps completed - you&apos;re finished
                        </Typography>
                      </Paper>
                    )}
                  </Box>
                </Box>
              </Modal>
            </Box>
          </div>
        </Grid>
      </div>
      <React.Suspense>
        <AddUserStory
          openDialog={userStoryView}
          data={data ?? []}
          setOpenDialog={setUserStoryView}
          setRows={setRows}
          setfilterRows={setfilterRows}
          SetReload={setReload}
          projectId={Project.ProjectId}
          refetch={refetch}
        />
      </React.Suspense>
      {userStoryView?.edit && (
        <EditUserStory
          openDialog={userStoryView}
          data={data ?? []}
          setOpenDialog={setUserStoryView}
          setRows={setRows}
          SetReload={setReload}
          Data={userStorydata}
          setfilterRows={setfilterRows}
          projectId={location.state.projectId}
        />
      )}
      <ViewUserStory
        openDialog={userStoryView}
        data={data ?? []}
        setOpenDialog={setUserStoryView}
        Data={userStorydata}
      />
      <BackDrop open={loading} />
    </div>
  );
};
