import {
  FormControl,
  InputLabel,
  Breadcrumbs,
  Typography,
  TextField,
  Button,
  TextareaAutosize,
  Divider,
  Checkbox,
} from "@mui/material";
import { Link } from "react-router-dom";
import "../../../StyleSheets/ReleaseNotes.css";
import { useEffect, useRef, useState } from "react";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { Get, Post } from "../../../Services/Axios";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import { ConvertDate } from "../../../Utilities/Utils";
import Autocomplete from "@mui/material/Autocomplete";
import { Project } from "../../../Models/Project/Project";

export const ReleaseNotes = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [Project, setProject] = useState<any>({
    ProjectId: 0,
    ProjectName: "",
  });
  const [selectedTaskIds, setSelectedTaskIds] = useState([]);
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [projects, setProjects] = useState([]);
  const [tasks, setTasks] = useState([]);
  const { register, handleSubmit, resetField } = useForm();
  const versionRef = useRef<HTMLInputElement>();
  const [Version, SetVersion] = useState<string>("0.0");
  const [ReleaseNotes, setReleaseNotes] = useState<any>([]);

  async function GetProjects() {
    const response: any = await Get("app/Project/GetAllProjectlist");
    setProjects(response.data || []);
  }

  async function GetProjectTasks() {
    const response: any = await Get(
      `app/ReleaseNotes/GetReadyForUATTaskList?projectId=${Project.ProjectId}`
    );
    setTasks(response.data || []);
  }

  async function GetReleaseNotes() {
    const response: any = await Get(
      `app/ReleaseNotes/GetReleaseNotes?projectId=${Project.ProjectId}`
    );

    const response2: any = await Get(
      `app/ReleaseNotes/GetReleaseNotesHistory?projectId=${Project.ProjectId}`
    );

    setReleaseNotes(response2.data || []);

    if (versionRef.current)
      versionRef.current.value = response.data.version || "0.0";
    SetVersion(response.data.version || "0.0");
  }

  useEffect(() => {
    GetProjects();
    GetProjectTasks();
    GetReleaseNotes();
  }, [loading]);

  const handleAutocompleteChange = (_: any, value: any) => {
    debugger;
    const selectedIds = value.map((val: any) => val.id);
    setSelectedTaskIds(selectedIds);
    setSelectedTasks(value);
  };

  const onSubmit = async (data: any) => {
    if (selectedTaskIds.length == 0) {
      Swal.fire({
        icon: "error",
        title: "Please Select Release Notes!",
        showConfirmButton: true,
      });
      return;
    }

    if (Version == data.Version || data.Version == "0.0") {
      Swal.fire({
        icon: "error",
        title: "Please Change Version!",
        showConfirmButton: true,
      });
      return;
    } else if (data.Version < Version) {
      Swal.fire({
        icon: "error",
        title: "Version Should be Greater Than Current Version!",
        showConfirmButton: true,
      });
      return;
    }

    data.ProjectId = Project.ProjectId;
    data.TaskIdList = selectedTaskIds;
    await Post("app/ReleaseNotes/AddReleaseNotes", data);
    setLoading(!loading);
    resetField("ReleasedDate");
    resetField("Description");
    Swal.fire({
      icon: "success",
      title: "Release Notes Added!",
      showConfirmButton: true,
    });
  };

  return (
    <div>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to="/Admin">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Release Notes</Typography>
      </Breadcrumbs>
      <div className="row col-md-6 m-5">
        <FormControl className="col-md-4">
          <Autocomplete
            options={projects
              .sort((a: Project, b: Project) => a.name!.localeCompare(b.name!))
              .map((project: Project) => ({
                label: project.name,
                id: project.id,
              }))}
            onChange={(e: any, value: any) => {
              if (value)
                setProject({
                  ProjectId: value.id,
                  ProjectName: value.name,
                });
              setLoading(!loading);
              return e;
            }}
            renderInput={(params: any) => (
              <TextField
                {...params}
                label="Select Project"
                variant="outlined"
                required
              />
            )}
          />
        </FormControl>
      </div>
      <div className="d-flex mx-auto flex-box-container">
        <div className="ReleaseNotes w-50 border border-4 m-1">
          <div className="d-flex ">
            <h3 className="m-2 p-2">Release Notes</h3>
            <div
              className="m-4"
              style={{
                width: "60%",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Autocomplete
                multiple
                id="check-boxes"
                options={tasks.map((e: any) => {
                  return {
                    value: e.description,
                    label: e.description,
                    id: e.id,
                  };
                })}
                // disableCloseOnSelect
                onChange={handleAutocompleteChange}
                getOptionLabel={(option: any) => option.label}
                renderOption={(props, option: any, { selected }) => (
                  <li {...props}>
                    <Checkbox style={{ marginRight: 8 }} checked={selected} />
                    {option.label}
                  </li>
                )}
                style={{ width: "100%" }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Task*"
                    placeholder="Task Required"
                  />
                )}
                value={selectedTasks}
              />
            </div>
          </div>
          <div
            className="m-3 overflow-scroll scroll border-bottom-white"
            style={{ height: 300 }}
          >
            {selectedTasks.length > 0 ? (
              selectedTasks.map((task: any, index) => (
                <div key={index} className="d-flex">
                  <div>
                    <Checkbox checked={true} />
                  </div>
                  <p style={{ cursor: "pointer" }}>{task.label}</p>
                </div>
              ))
            ) : (
              <>
                <div className="mt-5 fs-4 w-100 d-flex align-items-center justify-content-center">
                  <ErrorOutlineIcon className="fs-2 mx-2" />
                  {Project.ProjectId !== 0 ? "No Data" : "Select Project"}
                </div>
              </>
            )}
          </div>
        </div>
        <form
          className="inputs w-50 border border-4 m-1 p-3"
          onSubmit={handleSubmit(onSubmit)}
        >
          <InputLabel className="m-2">
            Version<span className="text-danger">*</span>
          </InputLabel>
          <TextField
            required
            className="col-md-4 m-2"
            type="text"
            fullWidth
            inputRef={versionRef}
            defaultValue={"0.0"}
            {...register("Version")}
            variant="outlined"
          />
          <InputLabel className="m-2">Description*</InputLabel>
          <TextareaAutosize
            required
            {...register("Description")}
            className="col m-2 text-area form-control"
            style={{ height: 100, width: 695 }}
          />
          <InputLabel className="m-2">Released Date*</InputLabel>
          <TextField
            required
            className="col-md-4 m-2"
            type="date"
            {...register("ReleasedDate")}
            fullWidth
            variant="outlined"
          />
          <input
            type="number"
            defaultValue={Project.ProjectId}
            {...register("ProjectId")}
            hidden
          />
          <input {...register("CreatedBy")} value="user" hidden />
          <input {...register("UpdatedBy")} value="user" hidden />
          <Button
            variant="contained"
            color="success"
            className="m-2 float-end"
            type="submit"
          >
            Save
          </Button>
        </form>
      </div>
      <Typography variant="h4" className="mx-4">
        Released Notes
      </Typography>
      <div
        className="border border-2 m-4 overflow-hidden"
        style={{ height: "62vh" }}
      >
        <Typography variant="h5" className="mx-4 mt-3">
          Project Name: <b>{Project.ProjectName}</b>
        </Typography>
        <Divider className="m-3" />
        <div
          className="mx-4 mt-3  overflow-scroll scroll"
          style={{ height: "60vh", marginBottom: "10px" }}
        >
          {ReleaseNotes?.map((notes: any, index: number) => {
            return (
              <div key={index}>
                <h5>
                  Version: <b>{notes?.version}</b>
                </h5>
                <h5>
                  Released Date: <b>{ConvertDate(notes?.releasedDate)}</b>
                </h5>
                <h5 className="mb-3">
                  Description: <b>{notes?.description}</b>
                </h5>
                {notes?.tasks?.map((task: any, index: number) => (
                  <>
                    <span key={index + "P"}>
                      {index + 1}. {task.description}
                    </span>
                    <br />
                  </>
                ))}
                <hr />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
