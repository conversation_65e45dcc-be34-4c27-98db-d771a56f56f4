import { Grid } from "@mui/material";
import { ProjectQuadrant } from "./ProjectQuadrant";
import NewUserStoryList from "./UserStory/newUserSrorylis";
import { NewUserInterfacelist } from "./UserInterface/newUserInterface";
import { useState, useEffect } from "react";
import { Breadcrumbs, Typography } from "@mui/material";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useContextProvider } from "../../../CommonComponents/Context";
import { EditProject } from "./EditProject";
import { Get } from "../../../Services/Axios";
import { AddUserInterface } from "./UserInterface/AddUserInterface";

export const ProjectOverView = () => {
  // const [rows, setRows] = useState<any>([]);
  const [activeTab, setActiveTab] = useState("ProjectQuadrant");
  const [additionalData,setadditionalData] = useState<any>([]);
  const location = useLocation();
  const { role } = useContextProvider();
  const navigate = useNavigate();
  // const [projects, setProjects] = useState([]);
  const [reload, SetReload] = useState<boolean>(true);
  const [data, setData] = useState([]);
  const handleTabClicks = (tab: any) => {
    setActiveTab(tab);
  };

  const handleTabClick = (tab: string, additionalData: any) => {
    setActiveTab(tab);
  setadditionalData(additionalData)
  };

  
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [projectView, setProjectView] = useState<any>({
    view: false,
    edit: false,
    add: false,
    viewObj: false,
  });
  const [UserInterfaceView, setUserInterfaceView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });

  useEffect(() => {
    var objective: any = Get(
      `app/Project/GetProjectObjective?ProjectId=${location.state?.projectId}`
    );
    setData(objective?.data?.projectObjectives || []);
  }, [reload,activeTab]);

  const handleEditClick = (project: any) => {
    SetReload(!reload);
    setSelectedProject({ ...project, isActive: location.state.isActive });
  };

  const ProjectList = () => {
    navigate(`/${role}/Project`);
  };

  return (
    <>
      <Grid container>
        <Grid>
          {location.state.route === "projectList" && (
            <Breadcrumbs className="mt-3 mx-3" separator=">">
              <Link color="inherit" to={`/${role}`}>
                <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
              </Link>
              <Link color="inherit" to={`/${role}/Project`}>
                <Typography sx={{ fontWeight: "bold" }}>Project</Typography>
              </Link>
              <Typography sx={{ fontWeight: "bold" }}>
                Project Overview
              </Typography>
            </Breadcrumbs>
          )}
          {location.state.route === "adminDashboard" && (
            <Breadcrumbs className="mt-3 mx-3" separator=">">
              <Link color="inherit" to={`/${role}`}>
                <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
              </Link>
              <Typography sx={{ fontWeight: "bold" }}>
                Project Overview
              </Typography>
            </Breadcrumbs>
          )}
        </Grid>
        <div
          className="container shadow bg-light"
          style={{
            maxHeight: "510vh",
            borderTopLeftRadius: "10px",
            borderTopRightRadius: "10px",
            marginTop: "1%",
          }}
        >
          <Grid
            container
            sx={{
              pt: "1%",
              pb: "1%",
              mb: "1%",
              borderBottom: "2px solid skyblue",
            }}
          >
            <Grid item xs={8}>
              <Typography
                className="fw-bolder fs-3"
                sx={{ mr: "45%", textAlign: "center", color: "#52a9eb" }}
              >
                Project Name : {location.state?.projectName}
              </Typography>
            </Grid>
            <Grid xs={2.5} md={3} item>
              <button
                type="button"
                className="btn btn-outline-warning float-end"
                style={{ fontWeight: "bold" }}
                onClick={() => {
                  handleEditClick(location.state);
                  setProjectView({ edit: true });
                }}
              >
                Edit
              </button>
            </Grid>
            <Grid xs={1.5} md={1} item>
              <button
                type="button"
                className="btn btn-outline-secondary float-end"
                style={{ fontWeight: "bold" }}
                onClick={ProjectList}
              >
                Back
              </button>
            </Grid>
          </Grid>
          <nav>
            <div className="nav nav-tabs" id="nav-tab" role="tablist">
              <button
                className={`col-md-2 nav-link ${
                  activeTab === "ProjectQuadrant"
                    ? "active text-bold border-2"
                    : "border-2"
                }`}
                id="nav-ProjectQuadrant-tab"
                data-bs-toggle="tab"
                data-bs-target="#nav-ProjectQuadrant "
                type="button"
                role="tab"
                aria-controls="ProjectQuadrant-tab-pane"
                aria-selected="true"
                onClick={() => handleTabClicks("ProjectQuadrant")}
                style={{ cursor: "pointer" }}
              >
                Project Quadrant
              </button>
              <button
                className={`col-md-2 nav-link ${
                  activeTab === "UserStory" ? "active border-2" : "border-2"
                }`}
                id="nav-UserStory-tab"
                data-bs-toggle="tab"
                data-bs-target="#nav-UserStory"
                type="button"
                role="tab"
                aria-controls="UserStory-tab-pane"
                aria-selected="true"
                onClick={() => handleTabClicks("UserStory")}
              >
                User Story
              </button>
              <button
                className={`col-md-2 nav-link ${
                  activeTab === "UserInterface" ? "active border-2" : "border-2"
                }`}
                id="nav-UserInterface-tab"
                data-bs-toggle="tab"
                data-bs-target="#nav-UserInterface"
                type="button"
                role="tab"
                aria-controls="UserInterface-tab-pane"
                aria-selected="true"
                onClick={() => handleTabClicks("UserInterface")}
              >
                User Interface
              </button>
            </div>
          </nav>
          <div className="tab-content" id="nav-tabContent">
            <div
              className="tab-pane fade show active"
              id="nav-ProjectQuadrant"
              role="tabpanel"
              aria-labelledby="nav-ProjectQuadrant-tab"
            >
              <ProjectQuadrant />
            </div>
            <div
              className={`tab-pane fade ${activeTab === "UserStory" ? "show active" : ""}`}
              id="nav-UserStory"
              role="tabpanel"
              aria-labelledby="nav-UserStory-tab"
            >
              <NewUserStoryList handleTabClick={(tab, additionalData:any) => handleTabClick(tab, additionalData)} />

            </div>
            <div
            className={`tab-pane fade ${activeTab === "UserInterface" ? "show active" : ""}`}
              id="nav-UserInterface"
              role="tabpanel"
              aria-labelledby="nav-UserInterface-tab"
            >
              <NewUserInterfacelist  additionalData={additionalData}/>
            </div>
          </div>
        </div>
      </Grid>
      {projectView?.edit && (
        <EditProject
          openDialog={projectView}
          setOpenDialog={setProjectView}
          Data={selectedProject}
          setReload={SetReload}
        />
      )}
      <AddUserInterface
        openDialog={UserInterfaceView}
        data1={data?.length > 0 ? data : []}
        setOpenDialog={setUserInterfaceView}
        SetReload={SetReload}
        projectId={location.state?.projectId}
      />
    </>
  );
};
