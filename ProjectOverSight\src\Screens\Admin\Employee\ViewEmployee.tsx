import { Box, Card, Grid, Modal, Typography } from "@mui/material";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import { FILTER } from "../../../Constants/Task/Task";
import { styled } from "@mui/material/styles";
import * as React from "react";
import { useEffect } from "react";
import { Post, Get } from "../../../Services/Axios";
import CancelIcon from "@mui/icons-material/Cancel";
import { ConvertDate } from "../../../Utilities/Utils";
import { EmployeeSkillSet } from "../../../Models/Employee/EmployeeSkillSet";
import Slide from "@mui/material/Slide";

import { ConvertTime } from "../../../Utilities/Utils";

const style = {
  position: "absolute" as "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 400,
  bgcolor: "background.paper",
  boxShadow: 24,
  pt: 2,
  px: 4,
  pb: 3,
};

const Demo = styled("div")(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
}));

export const ViewEmp = ({ viewEmployee, setviewEmployee, data }: any) => {
  const [rows, setRows] = React.useState<any>([]);
  const [empSkills, setempSkills] = React.useState<any>([]);
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1;
  const day = currentDate.getDate();
  const [Tasklist, setTasklist] = React.useState<any>([]);
  const filter = FILTER;
  const handleClose = () => {
    setviewEmployee({ view: false });
  };
  const dateFilter = {
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  };

  const daysInMonth = new Date(dateFilter.year, dateFilter.month, 0).getDate();
  const dateLabels = Array.from({ length: daysInMonth }, (_, i) => {
    const currentDate = new Date(dateFilter.year, dateFilter.month - 1, i + 1);
    return currentDate.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "short",
      day: "2-digit",
    });
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [
          attendanceResponse,
          empSkillListResponse,
          skillListResponse,
          taskListResponse,
        ]: any = await Promise.all([
          Get(
            `app/Employee/GetAttendanceByUserId?userId=${data?.userId}&month=${dateFilter.month}&year=${dateFilter.year}`
          ),
          Get(`app/Employee/GetEmployeeSkillById?employeeId=${data?.userId}`),
          Get("app/Skillset/GetSkillsetList"),
          Post(
            `app/Task/GetEmployeeTaskList?employeeId=${data?.userId}`,
            filter
          ),
        ]);

        setRows(attendanceResponse?.data?.employeeAttendances || []);

        let empSkill: EmployeeSkillSet[] =
          empSkillListResponse?.data?.map((item: any) => ({
            Category: skillListResponse?.data?.find(
              (skill: any) => skill.id === item.id
            )?.category,
            SkillSetId: item.id,
            EmployeeId: data?.userId,
          })) || [];

        setempSkills(empSkill);
        setTasklist(taskListResponse?.data || []);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, [data, dateFilter]);

  const estimatedTask = Tasklist.map((e: any) => {
    const formattedDate = `${day < 10 ? "0" + day : day}-${
      month < 10 ? "0" + month : month
    }-${year}`;
    const Todaydate = ConvertDate(e.estimateStartDate);
    return Todaydate === formattedDate ? "OK" : "Not_OK";
  });

  const status = Tasklist.filter((e: any) => e.status === "Assigned");
  const UnAssigned = Tasklist.filter((e: any) => e.status === "Unassigned");

  return (
    <>
      <Grid container>
        <Modal
          open={viewEmployee?.view}
          onClose={handleClose}
          aria-labelledby="parent-modal-title"
          aria-describedby="parent-modal-description"
        >
          <Box
            sx={{
              ...style,
              flexGrow: 1,
              width: "80%",
              height: "90%",
              overflowY: "scroll",
            }}
          >
            <Typography
              gutterBottom
              variant="h6"
              component="div"
              sx={{
                fontSize: ["110%", "125%"],
                fontWeight: "bold",
                color: "#0e38c4",
              }}
            >
              Welcome {data?.user?.name}
              <CancelIcon
                onClick={handleClose}
                sx={{
                  float: "right",
                  color: "#c40e2c",
                  cursor: "pointer",
                  fontSize: "30px",
                }}
              />
            </Typography>
            <Grid container>
              <Grid item xs={4} md={3}>
                <Slide direction="down" in={true} mountOnEnter unmountOnExit>
                  <Card
                    sx={{
                      border: "solid 0.5px lightgrey",
                      backgroundColor: "#97c2f0",
                      borderRadius: "20px",
                      textAlign: "center",
                    }}
                  >
                    <Typography
                      gutterBottom
                      sx={{
                        color: "#0d4e94",
                        fontFamily: "monospace",
                        fontWeight: "bold",
                      }}
                    >
                      Assigned Tasks
                    </Typography>
                    <Typography sx={{ fontSize: "30px", color: "#945a0d" }}>
                      {status.length}
                    </Typography>
                  </Card>
                </Slide>
              </Grid>
              <Grid item xs={4} md={3}>
                <Slide direction="down" in={true} mountOnEnter unmountOnExit>
                  <Card
                    sx={{
                      border: "solid 0.5px lightgrey",
                      backgroundColor: "#97c2f0",
                      borderRadius: "20px",
                      textAlign: "center",
                      ml: "5% ",
                    }}
                  >
                    <Typography
                      gutterBottom
                      sx={{
                        color: "#0d4e94",
                        fontFamily: "monospace",
                        fontWeight: "bold",
                      }}
                    >
                      Un-Assigned Tasks
                    </Typography>
                    <Typography sx={{ fontSize: "30px", color: "#945a0d" }}>
                      {UnAssigned.length}
                    </Typography>
                  </Card>
                </Slide>
              </Grid>
            </Grid>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6} sx={{ mt: "2%" }}>
                <Slide direction="down" in={true} mountOnEnter unmountOnExit>
                  <Card
                    sx={{
                      border: "solid 0.5px lightgrey",
                      backgroundColor: "#e4e6ed",
                      borderRadius: "20px",
                      textAlign: "center",
                    }}
                  >
                    <Typography
                      sx={{ mt: 1, mb: 1, color: "#053b69" }}
                      variant="h5"
                      component="div"
                    >
                      Task
                    </Typography>
                    <Demo>
                      <List sx={{ overflowY: "scroll", height: "180px" }}>
                        {Tasklist.length ? (
                          Tasklist?.map((e: any) => {
                            return (
                              <>
                                <ListItem>
                                  <ListItemText
                                    primary={`${e.name}`}
                                    secondary={`${e.category}`}
                                  />
                                  <span style={{ color: "blue" }}>
                                    {e.projectName}
                                  </span>
                                </ListItem>
                                {/* <Divider orientation="horizontal" /> */}
                              </>
                            );
                          })
                        ) : (
                          <p className="fs-3 mt-5">No Task</p>
                        )}
                      </List>
                    </Demo>
                  </Card>
                </Slide>
              </Grid>
              <Grid item xs={12} md={6} sx={{ mt: "2%" }}>
                <Slide direction="down" in={true} mountOnEnter unmountOnExit>
                  <Card
                    sx={{
                      border: "solid 0.5px lightgrey",
                      backgroundColor: "#e4e6ed",
                      borderRadius: "20px",
                      textAlign: "center",
                    }}
                  >
                    <Typography
                      sx={{ mt: 1, mb: 1, color: "#053b69" }}
                      variant="h5"
                      component="div"
                    >
                      Task Due Today
                    </Typography>
                    <Demo>
                      <List sx={{ overflowY: "scroll", height: "180px" }}>
                        {estimatedTask.includes("OK") ? (
                          Tasklist?.map((e: any) => {
                            const TodayTask = ConvertDate(e?.estimateStartDate);
                            const formattedDate = `${
                              day < 10 ? "0" + day : day
                            }-${month < 10 ? "0" + month : month}-${year}`;
                            if (TodayTask === formattedDate) {
                              return (
                                <>
                                  <ListItem>
                                    <ListItemText
                                      primary={`${e.name}`}
                                      secondary={
                                        <Typography sx={{ color: "#1a3857" }}>
                                          {e.category}
                                        </Typography>
                                      }
                                    />
                                    <span style={{ color: "#f09a37" }}>
                                      {e.category}
                                    </span>
                                  </ListItem>
                                  {/* <Divider orientation="horizontal" /> */}
                                </>
                              );
                            }
                          })
                        ) : (
                          <p className="fs-3 mt-5">No Due Today</p>
                        )}
                      </List>
                    </Demo>
                  </Card>
                </Slide>
              </Grid>
            </Grid>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6} sx={{ mt: "2%" }}>
                <Slide direction="up" in={true} mountOnEnter unmountOnExit>
                  <Card
                    sx={{
                      border: "solid 0.5px lightgrey",
                      backgroundColor: "#e4e6ed",
                      borderRadius: "20px",
                      textAlign: "center",
                    }}
                  >
                    <Typography
                      sx={{ mt: 1, mb: 1, color: "#053b69" }}
                      variant="h5"
                      component="div"
                    >
                      Overdue Task
                    </Typography>
                    <Demo>
                      <List sx={{ overflowY: "scroll", height: "180px" }}>
                        {Tasklist.length ? (
                          Tasklist?.map((e: any) => {
                            if (
                              e.status === "Assigned" ||
                              e.status === "In Progress" ||
                              e.status === "Unassigned"
                            ) {
                              const estimateStartDate = new Date(
                                e.estimateStartDate
                              );
                              const today = new Date();
                              const timeDiff =
                                estimateStartDate.getTime() - today.getTime();
                              const dayDiff = -Math.ceil(
                                timeDiff / (1000 * 3600 * 24)
                              );
                              const statusText =
                                dayDiff > 700
                                  ? "More than 2 Year ago"
                                  : `${dayDiff} days ago`;
                              return (
                                <>
                                  <ListItem>
                                    <ListItemText
                                      primary={
                                        <Typography
                                          sx={{ color: "#146fc9" }}
                                        >{`${e.name}`}</Typography>
                                      }
                                      secondary={
                                        <Typography
                                          sx={{ color: "#1a3857" }}
                                        >{`${e.category} - ${e.status}`}</Typography>
                                      }
                                      sx={{ width: "50%" }}
                                    />
                                    <span style={{ color: "#f09a37" }}>
                                      {e.status === "Unassigned"
                                        ? "Not Assigned "
                                        : statusText}
                                    </span>
                                  </ListItem>
                                  {/* <Divider orientation="horizontal" /> */}
                                </>
                              );
                            }
                          })
                        ) : (
                          <p className="fs-3 mt-5">No Task</p>
                        )}
                      </List>
                    </Demo>
                  </Card>
                </Slide>
              </Grid>
              <Grid item xs={12} md={6} sx={{ mt: "2%" }}>
                <Slide direction="up" in={true} mountOnEnter unmountOnExit>
                  <Card
                    sx={{
                      border: "solid 0.5px lightgrey",
                      backgroundColor: "#e4e6ed",
                      borderRadius: "20px",
                      textAlign: "center",
                    }}
                  >
                    <Typography
                      sx={{ mt: 1, mb: 1, color: "#053b69" }}
                      variant="h5"
                      component="div"
                    >
                      TimeSheet
                    </Typography>
                    <Demo>
                      <List sx={{ overflowY: "scroll", height: "180px" }}>
                        {rows
                          ?.filter((a: any) => a?.inTime != null)
                          .map((e: any, index: any) => {
                            const day =
                              dateLabels[rows.length - index - 1].split(",")[0];
                            const isWeekend = (day: number): boolean =>
                              day === 0 || day === 6;
                            const dayOfWeek = new Date(e?.date).getDay();
                            return (
                              <React.Fragment key={index}>
                                <Grid container>
                                  <ListItem>
                                    <Grid xs={4}>
                                      <ListItemText
                                        sx={{
                                          color: isWeekend(dayOfWeek)
                                            ? "red"
                                            : "#8904d6",
                                        }}
                                        primary={`${ConvertDate(e?.date)}`}
                                        secondary={
                                          isWeekend(dayOfWeek)
                                            ? "Holiday"
                                            : `${day}`
                                        }
                                      />
                                    </Grid>
                                    <Grid xs={4}>
                                      <ListItemText
                                        sx={{
                                          color:
                                            e?.inTime === null
                                              ? "gray"
                                              : ConvertTime(e?.inTime, "AM") >
                                                ConvertTime("10:05", "AM")
                                              ? "green"
                                              : "#fc0330",
                                          textAlign: "center",
                                        }}
                                        primary={`${
                                          ConvertTime(e?.inTime, "AM")
                                            ? ConvertTime(e?.inTime, "AM")
                                            : "No Time"
                                        }`}
                                        secondary={`${
                                          ConvertTime(e?.outTime, "PM")
                                            ? ConvertTime(e?.outTime, "PM")
                                            : "No Time"
                                        }`}
                                      />
                                    </Grid>
                                    <Grid xs={4}>
                                      <ListItemText
                                        sx={{
                                          color:
                                            e?.inTime === null
                                              ? "gray"
                                              : ConvertTime(e?.inTime, "AM") >
                                                ConvertTime("10:05", "AM")
                                              ? "green"
                                              : "#fc0330",
                                          float: "right",
                                        }}
                                        primary={`In-Time`}
                                        secondary={`Out-Time`}
                                      />
                                    </Grid>
                                  </ListItem>
                                </Grid>
                                {/* <Divider orientation="horizontal" /> */}
                              </React.Fragment>
                            );
                          })}

                        {rows?.every((a: any) => a?.inTime === null) && (
                          <p className="fs-3 mt-5">No Time Sheet</p>
                        )}
                      </List>
                    </Demo>
                  </Card>
                </Slide>
              </Grid>
            </Grid>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6} sx={{ mt: "2%" }}>
                <Slide direction="up" in={true} mountOnEnter unmountOnExit>
                  <Card
                    sx={{
                      border: "solid 0.5px lightgrey",
                      backgroundColor: "#e4e6ed",
                      borderRadius: "20px",
                      textAlign: "center",
                    }}
                  >
                    <Typography
                      sx={{ mt: 1, mb: 1, color: "#053b69" }}
                      variant="h5"
                      component="div"
                    >
                      Skills
                    </Typography>
                    <Demo>
                      <List sx={{ overflowY: "scroll", height: "180px" }}>
                        {empSkills.length ? (
                          empSkills?.map((e: any) => {
                            return (
                              <>
                                <ListItem>
                                  <ListItemText
                                    sx={{ color: "#d95007" }}
                                    primary={`${e?.Category}`}
                                    // secondary={secondary ? 'Secondary text' : null}
                                  />
                                </ListItem>
                                {/* <Divider orientation="horizontal" /> */}
                              </>
                            );
                          })
                        ) : (
                          <p className="fs-3 mt-5">No Skills</p>
                        )}
                      </List>
                    </Demo>
                  </Card>
                </Slide>
              </Grid>
            </Grid>
          </Box>
        </Modal>
      </Grid>
    </>
  );
};
