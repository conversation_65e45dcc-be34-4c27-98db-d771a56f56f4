import {
  <PERSON><PERSON><PERSON>rum<PERSON>,
  <PERSON>rid,
  <PERSON>po<PERSON>,
  Box,
  Button,
  Tooltip,
} from "@mui/material";
import { <PERSON> } from "react-router-dom";
import { IWeeklyPlan } from "../../../Models/WeeklyPlan/WeeklyPlan";
import { useEffect, useRef, useState } from "react";
import { Get } from "../../../Services/Axios";
import DataTable from "react-data-table-component";
import AddIcon from "@mui/icons-material/Add";
import { ConvertDate } from "../../../Utilities/Utils";
import { AddWeeklyPlan } from "./AddWeeklyPlan";
import Select from "react-select";
import { Project } from "reactflow";
import { useContextProvider } from "../../../CommonComponents/Context";
import { Category, CommonMaster } from "../../../Models/Common/CommonMaster";
import { PRIORITYTYPE } from "../../../Constants/Common/CommonMaster";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { Regex } from "../../../Constants/Regex/Regex";
import SearchIcon from "@mui/icons-material/Search";
import EditIcon from "@mui/icons-material/Edit";
import RefreshIcon from "@mui/icons-material/Refresh";

export const WeeklyPlan = () => {
  const [weeklyPlan, setWeeklyPlan] = useState<IWeeklyPlan[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [reload, setReload] = useState<boolean>(false);
  const { category, commonMaster } = useContextProvider();
  const projectNameRef = useRef<any>();
  const categoryRef = useRef<any>();
  const priorityRef = useRef<any>();
  const percentageRef = useRef<any>();
  const dueDateRef = useRef<HTMLInputElement>(null);
  const weekendingRef = useRef<HTMLInputElement>(null);
  const statusRef = useRef<any>(null);

  async function getWeeklyPlan() {
    const response1: any = await Get("app/WeeklyPlan/GetWeeklyPlans");
    const response2: any = await Get("/app/Project/GetEmployeeProjectlist");
    setProjects(response2?.data || []);
    setWeeklyPlan(response1?.data);
  }

  useEffect(() => {
    getWeeklyPlan();
  }, [reload]);

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      type: "Date",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: () => {
        return (
          <>
            <Tooltip
              title="View"
              className="mx-1"
              // onClick={() => {
              //   setTeamView({ view: true });
              //   setviewTeamData(row);
              // }}
            >
              <VisibilityIcon className="fs-4 text-info" />
            </Tooltip>
            <Tooltip
              title="Edit"
              className="mx-1"
              // onClick={() => {
              //   setTeamView({ edit: true });
              //   setviewTeamData(row);
              // }}
            >
              <EditIcon className="fs-4 text-warning" />
            </Tooltip>
          </>
        );
      },
    },
    {
      field: "project",
      name: "Project",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.project}</p>
      ),
    },
    {
      field: "category",
      name: "Category",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.category}</p>
      ),
    },
    {
      field: "description",
      name: "Description",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.description}</p>
      ),
    },
    {
      field: "notes",
      name: "Notes",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => <p className="tableStyle">{row.notes}</p>,
    },
    // {
    //   field: "assignedTo",
    //   name: "Assigned To",
    //   width: "13rem",
    //   editable: false,
    //   headerAlign: "left",
    //   align: "left",
    //   selector: (row: IWeeklyPlan) => (
    //     <p className="tableStyle">{row.assignedTo}</p>
    //   ),
    // },
    {
      field: "priority",
      name: "Priority",
      width: "10rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.priority}</p>
      ),
    },
    {
      field: "estimatedHours",
      name: "Estimated Hours",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.estimatedHours}</p>
      ),
    },
    {
      field: "dueDate",
      name: "Due Date",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{ConvertDate(`${row.dueDate}`)}</p>
      ),
    },
    {
      field: "status",
      name: "Status",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.status}</p>
      ),
    },
    {
      field: "percentage",
      name: "Percentage",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.status}</p>
      ),
    },
    {
      field: "weekEndingDate",
      name: "Week Ending Date",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{ConvertDate(`${row.weekEndingDate}`)}</p>
      ),
    },
  ];

  function applyFilter() {}

  function reset() {
    projectNameRef.current.clearValue();
    categoryRef.current.clearValue();
    priorityRef.current.clearValue();
    statusRef.current.clearValue();
    if (dueDateRef.current) dueDateRef.current.value = "";
    if (weekendingRef.current) weekendingRef.current.value = "";
    if (percentageRef) percentageRef.current.value = "";
  }

  return (
    <div>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link to="/Employee">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Weekly Plan</Typography>
      </Breadcrumbs>
      <div className="well mx-auto mt-4">
        <div className="row">
          <div className="col-sm-2">
            <div className="form-group">
              <label>Project Name</label>
              <Select
                id="project-name"
                isClearable={true}
                ref={projectNameRef}
                className="col mt-1 custom-select"
                onChange={() => {
                  // setfilter((prevState: any) => {
                  //   return {
                  //     ...prevState,
                  //     projectName: selectedOption ? selectedOption.value : null,
                  //   };
                  // });
                }}
                options={projects
                  ?.sort((a: Project, b: Project) =>
                    a.name.localeCompare(b.name)
                  )
                  ?.map((e: Project) => ({
                    label: e.name,
                    value: e.name,
                  }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Category</label>
              <Select
                id="category"
                isClearable={true}
                ref={categoryRef}
                className="col mt-1 custom-select"
                onChange={() => {
                  // setfilter((prevState: any) => {
                  //   return {
                  //     ...prevState,
                  //     projectName: selectedOption ? selectedOption.value : null,
                  //   };
                  // });
                }}
                options={category
                  .sort((a: Category, b: Category) =>
                    a.subCategory.localeCompare(b.subCategory)
                  )
                  .map((e: Category) => ({
                    label: e.subCategory,
                    value: e.subCategory,
                  }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Priority</label>
              <Select
                id="priority"
                isClearable={true}
                ref={priorityRef}
                className="col mt-1 custom-select"
                onChange={() => {
                  // setfilter((prevState: any) => {
                  //   return {
                  //     ...prevState,
                  //     projectName: selectedOption ? selectedOption.value : null,
                  //   };
                  // });
                }}
                options={commonMaster
                  .filter((x) => x.codeType === PRIORITYTYPE)
                  .map((commonMaster: CommonMaster) => ({
                    label: commonMaster.codeValue,
                    id: commonMaster.codeValue,
                  }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                id="priority"
                isClearable={true}
                ref={statusRef}
                className="col mt-1 custom-select"
                onChange={() => {
                  // setfilter((prevState: any) => {
                  //   return {
                  //     ...prevState,
                  //     projectName: selectedOption ? selectedOption.value : null,
                  //   };
                  // });
                }}
                options={["Not Started", "In Progress", "Completed"].map(
                  (status: any) => ({
                    label: status,
                    id: status,
                  })
                )}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Percentage</label>
              <input
                maxLength={3}
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.NUMBER, "");
                  // setfilter((prevState: TaskFilter) => {
                  //   return {
                  //     ...prevState,
                  //     estStartDate:
                  //       e.target.value == "" ? null : e.target.value,
                  //   };
                  // });
                }}
                ref={percentageRef}
                type="input"
                id="Percentage"
                placeholder="Due Date"
                className="m-1  form-control"
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Due Date</label>
              <input
                onChange={() => {
                  // setfilter((prevState: TaskFilter) => {
                  //   return {
                  //     ...prevState,
                  //     estStartDate:
                  //       e.target.value == "" ? null : e.target.value,
                  //   };
                  // });
                }}
                ref={dueDateRef}
                type="date"
                id="dueDate"
                placeholder="Due Date"
                className="m-1  form-control"
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Week Ending Date</label>
              <input
                onChange={() => {
                  // setfilter((prevState: TaskFilter) => {
                  //   return {
                  //     ...prevState,
                  //     estStartDate:
                  //       e.target.value == "" ? null : e.target.value,
                  //   };
                  // });
                }}
                ref={weekendingRef}
                type="date"
                id="dueDate"
                placeholder="Due Date"
                className="m-1  form-control"
              />
            </div>
          </div>
          <div className="col-md-10">
            <div className="row justify-content-end">
              <div className="col-auto">
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-3 mt-4"
                  onClick={() => applyFilter()}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-3 mt-4"
                  onClick={() => reset()}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center">
        <div className="col-11 col-s-4">
          <Grid>
            <Button
              variant="contained"
              className="mb-2 float-md-start"
              sx={{ ml: "3%" }}
              onClick={() => setOpen(true)}
            >
              Add Weekly Plan
              <AddIcon className="mx-1" />
            </Button>
          </Grid>
        </div>
        <div
          className="responsive-div"
          style={{ marginTop: "4%", width: "94vw" }}
        >
          <Grid item xs={12} sm={11}>
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                fixedHeader={true}
                responsive
                persistTableHead
                data={weeklyPlan || []}
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },

                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
              />
            </Box>
          </Grid>
        </div>
      </div>
      <AddWeeklyPlan open={open} setOpen={setOpen} setReload={setReload} />
    </div>
  );
};
