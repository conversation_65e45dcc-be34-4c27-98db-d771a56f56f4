import {
  <PERSON>ert,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextareaAutosize,
  Autocomplete,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import { Get, Post, PostFiles } from "../../../../Services/Axios";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import { EditFileUpload } from "../../../../CommonComponents/EditFileUpload";
import { Document } from "../../../../Models/Project/UserStory";
import AddIcon from "@mui/icons-material/Add";
import { Regex } from "../../../../Constants/Regex/Regex";
import { AddProjectObjective } from "../ProjectObjective/AddProjectObjective";

const formField = [
  "Name",
  "Description",
  "StartDate",
  "EndDate",
  "Status",
  "Percentage",
  "CreatedBy",
  "UpdatedBy",
  "ProjectId",
  "id",
];

export const EditUserStory = ({
  openDialog,
  setOpenDialog,
  setRows,
  projectId,
  setfilterRows,
  SetReload,
  Data,
  data,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [save, setSave] = useState<boolean>(false);
  const [uploadedFiles, setUploadedFiles] = useState<any>([]);
  const [selectedFiles, setSelectedFiles] = useState<any>([]);
  const [open, setOpen] = useState<any>({ add: false });
  const [deleteFileIds, setDeleteFileIds] = useState<Array<number>>([]);
  const [projectobjective, setprojectobjective] = useState<any>([]);
  const [IDs, setId] = useState<any>([]);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });

  console.log(Data);

  useEffect(() => {
    setUploadedFiles(Data?.documents ?? []);
  }, [openDialog?.edit]);
  var objective: any[] = [];

  data?.data?.projectObjectiveMappings?.forEach((e: any) => {
    if (e.userStoryId === Data?.id)
      objective.push({ label: e.description, id: e.projectObjectiveId });
  });

  const onSubmitHandler = async (data: any) => {
    if (typeof data.ProjectObjectiveIds === "string") {
      data.ProjectObjectiveIds = data.ProjectObjectiveIds.split(",");
    }

    setSave(true);
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }
    data.status = Data.status;
    data.ProjectObjectiveIds = IDs;
    data.resourceId = Data.resourceId;
    const { error }: any = await Post("app/Project/UpdateUserStory", data);

    if (!error && selectedFiles.length > 0) {
      selectedFiles.forEach(async (file: any) => {
        var document: Document = {
          TableName: "UserStory",
          AttributeId: data.id,
          ProjectId: data.ProjectId,
          DocType: data.DocType,
          FileName: file.name,
          FileType: file.type,
          File: file,
          IsActive: true,
          CreatedBy: "user",
          UpdatedBy: "user",
        };
        await PostFiles("app/Project/UploadFiles", document);
      });
    }

    if (deleteFileIds.length > 0) {
      deleteFileIds.forEach((_id: number) => {
        Post(`app/Project/DeleteFile?id=${_id}`, "");
      });
    }

    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "User Story Updated Successfully!",
        icon: "success",
      };
    }
    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      let userStoryList = Get(
        `app/Project/GetUserStoryList?projectId=${projectId}`
      );
      userStoryList.then((response: any) => {
        setRows(response?.data);
        setfilterRows(response?.data || []);
      });
    });
    handleClose();
  };

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  useEffect(() => {
    async function getobjectives() {
      let objective: any = await Get(
        `app/Project/GetProjectObjective?ProjectId=${projectId}`
      );
      setprojectobjective(objective?.data?.projectObjectives || []);
    }
    getobjectives();
  }, [projectId, open, openDialog?.edit]);

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ edit: false });
    setSelectedFiles([]);
    setDeleteFileIds([]);
  };

  return (
    <div>
      <Dialog open={openDialog?.edit}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue" }}>Edit User Story</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <div className="row mx-auto">
              <TextField
                required
                defaultValue={Data?.name}
                className="col m-2"
                {...register("Name")}
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_SPACE, "");
                }}
                label="User Story Name"
                type="text"
                variant="outlined"
              />
              <FormControl className="col m-2">
                <TextField
                  id="outlined-read-only-input"
                  label="Status"
                  disabled
                  defaultValue={Data?.status}
                  InputProps={{
                    readOnly: true,
                  }}
                  {...register("Status")}
                />
              </FormControl>
            </div>
            <div className="row mx-auto">
              <InputLabel className="mx-3" id="Description">
                Description
              </InputLabel>
              <TextareaAutosize
                required
                className="col m-2 mb-3 form-control"
                defaultValue={Data?.description}
                placeholder="Description"
                {...register("Description")}
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                }}
                style={{ height: 100 }}
              />
            </div>
            <div className="row mx-auto">
              <div className="col">
                <InputLabel id="start-date">Start date</InputLabel>
                <TextField
                  required
                  id="start-date"
                  defaultValue={Data?.startDate?.slice(0, 10)}
                  margin="dense"
                  {...register("StartDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </div>
              <div className="col">
                <InputLabel id="end-date">End date</InputLabel>
                <TextField
                  required
                  id="end-date"
                  defaultValue={Data?.endDate?.slice(0, 10)}
                  margin="dense"
                  {...register("EndDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </div>

              <input {...register("CreatedBy")} value="user" hidden />
              <input {...register("UpdatedBy")} value="user" hidden />
              <input {...register("Percentage")} value="0" hidden />
              <input {...register("ProjectId")} value={projectId} hidden />
              <input {...register("id")} value={Data?.id} hidden />
            </div>

            <div className="row mx-auto">
              <FormControl className="col m-2">
                <EditFileUpload
                  uploadedFiles={uploadedFiles}
                  setUploadedFiles={setUploadedFiles}
                  selectedFiles={selectedFiles}
                  setSelectedFiles={setSelectedFiles}
                  setDeleteFileIds={setDeleteFileIds}
                  deleteFileIds={deleteFileIds}
                />
              </FormControl>
              <FormControl className="col m-2">
                <InputLabel id="Status">Document Type</InputLabel>
                <Select
                  labelId="Document Type"
                  id="DocumentType"
                  label="Document Type"
                  defaultValue={Data?.documents[0]?.docType}
                  required={uploadedFiles.length > 0}
                  {...register("DocType")}
                >
                  <MenuItem value="Input">Input</MenuItem>
                  <MenuItem value="Process">Process</MenuItem>
                  <MenuItem value="Output">Output</MenuItem>
                  <MenuItem value="Sample Code">Sample Code</MenuItem>
                </Select>
              </FormControl>
            </div>
            <div className="row mx-auto">
            {objective.map((e:any)=> e.label) && <InputLabel className="mx-3" id="projectobjective">Project Objectives</InputLabel>}
              <div
                className="col-md-6 mt-2"
                style={{ maxHeight: "100px", overflowY: "auto" }}
              >
                <Autocomplete
                  multiple
                  defaultValue={objective.map((e:any)=> e.label)}
                  options={projectobjective.map((objective: any) => ({
                    label: objective.description,
                    id: objective.id,
                  }))}
                  {...register("ProjectObjectiveIds")}
                  onChange={(e: any, value: any) => {
                    if (value) var ids = [];
                    value.map((e: any) => {
                      ids.push(e.id);
                    });
                    setId(ids);
                    return e;
                  }}
                  renderInput={(params: any) => (
                    <TextField
                      {...params}
                      label={objective.map((e:any)=> e.label)? "" : "Select Objectives"}
                      variant="outlined"
                      style={{ height: "50%" }}
                    />
                  )}
                />
              </div>
              <div className="col-md-6 mt-2">
                <Button
                  variant="contained"
                  className="mt-2 m-2 col"
                  onClick={() => setOpen({ add: true })}
                >
                  Add Project Objective
                  <AddIcon className="mx-1" />
                </Button>
              </div>
            </div>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="contained" color="error">
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="success"
              disabled={save}
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <AddProjectObjective
        openDialog={open}
        setOpenDialog={setOpen}
        ProjectId={projectId}
        SetReload={SetReload}
      />
    </div>
  );
};
