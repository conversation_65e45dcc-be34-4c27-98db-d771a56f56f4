import {
  <PERSON>readc<PERSON>bs,
  Select,
  FormControl,
  Button,
  Typography,
  Box,
  MenuItem,
} from "@mui/material";
import { Link } from "react-router-dom";
import Paper from "@mui/material/Paper";
import Stack from "@mui/material/Stack";
import { styled } from "@mui/material/styles";
// import Select from "react-select";
import { useEffect, useRef, useState } from "react";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";

import { Get } from "../../../Services/Axios";

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === "dark" ? "#1A2027" : "#fff",
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  color: theme.palette.text.secondary,
  border: "1px solid #ffbf00",
}));

const Items = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === "dark" ? "#1A2027" : "#fff",
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  color: theme.palette.text.secondary,
}));

export const DepartmentTime1 = () => {
  const CategoryRef = useRef<any>(null);
  const subCategoryRef = useRef<any>(null);
  const [categories, setcategories] = useState<any>([]);
  const [subCategories, setSubCategories] = useState<any>([]);
  const [userDto, setUserDto] = useState<any>({
    Department: "",
    Category: "",
  });

  const [modelError, setModelError] = useState<any>({
    Department: false,
    Category: false,
  });

  var Category: string[] = [];
  var categorySet = new Set<any>();
  useEffect(() => {
    let categoriesList = Get("app/CommonMaster/GetCodeTableList");
    categoriesList.then((response: any) => {
      var category = response?.data?.filter(
        (x: any) => x.codeType === "EmployeeCategory"
      );
      setcategories(category || []);
    });
  }, []);

  categories?.forEach((element: any) => {
    categorySet.add(element.codeName);
  });

  Category = [...categorySet];
  //   type userDtoType = keyof typeof userDto;

  const handleCategoryChange = (event: any) => {
    if (subCategoryRef.current) subCategoryRef.current.value = "";
    const subCategories = categories.filter(
      (element: any) => element.codeName === event.target.value
    );
    setSubCategories(subCategories);
  };

  return (
    <div className="main-container ">
      <div>
        <Breadcrumbs className="mt-3 mx-3 " separator=">">
          <Link to="/Admin">
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          {/* <Link to="/Admin/R"> */}
          <Typography sx={{ fontWeight: "bold", color: "grey" }}>
            Department Time
          </Typography>
          {/* </Link> */}
        </Breadcrumbs>

        <Box sx={{ width: "80%", margin: "auto" }}>
          <Stack spacing={2}>
            <Item>
              <Typography
                color="whitesmoke"
                sx={{
                  float: "left",
                  fontWeight: "bold",
                  width: "20%",
                  height: "20%",
                  backgroundColor: "Highlight",
                  borderRadius: "5px",
                }}
              >
                Select Department
              </Typography>
              <div
                className="top-div mx-auto shadow-container"
                style={{
                  border: "2px solid #3498db",
                  marginTop: "4%",
                  borderRadius: "20px",
                  padding: "30px",
                  width: "80%",
                  backgroundColor: "white",
                  transition: "box-shadow 0.3s ease",
                }}
              >
                <div
                  className="First row"
                  style={{ display: "flex", alignItems: "center" }}
                >
                  <div className="col-sm-6 " style={{ display: "flex" }}>
                    <FormControl required fullWidth className="mx-3 w-100 ">
                      <label
                        style={{
                          marginRight: "35px",
                          fontWeight: "bold",
                          fontSize: "20px",
                        }}
                      >
                        Category
                      </label>
                      {/* <Select
                id="Category-name"
                isClearable={true}
                ref={CategoryRef}
                placeholder="Select the Category"
                className="col mt-1 custom-select"
                // error={modelError?.Category}
                onChange={(e: any) => {
                  handleCategoryChange(e);
                  setModelError({ ...modelError, Category: false });
                  setUserDto({ ...userDto, Category: e.target.value });
                }}
                // onChange={(selectedOption: any) => {
                //   setFilter((prevState: CommonMaster) => {
                //     return {
                //       ...prevState,
                //       Category: selectedOption ? selectedOption.value : null,
                //     };
                //   });
                // }}
                options={Category?.map((e: any) => ({
                    label: e,
                    value: e,
                  }))}
                styles={{
                  menu: (provided: any) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              >
              </Select> */}
                      <Select
                        id="Category-name"
                        className="col mt-1 custom-select"
                        required
                        inputRef={CategoryRef}
                        error={modelError?.Category}
                        onChange={(e: any) => {
                          handleCategoryChange(e);
                          setModelError({ ...modelError, Category: false });
                          setUserDto({ ...userDto, Category: e.target.value });
                        }}
                        // label="Category"
                      >
                        {Category.map((option: any) => (
                          <MenuItem key={option} value={option}>
                            {option}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                  <div className="col-sm-6 " style={{ display: "flex" }}>
                    <FormControl fullWidth className="mx-2 w-100">
                      <label
                        style={{
                          marginRight: "35px",
                          fontWeight: "bold",
                          fontSize: "20px",
                        }}
                      >
                        Department
                      </label>
                      {/* <Select
                id="Category-name"
                isClearable={true}
                ref={CategoryRef}
                placeholder="Select the Department"
                className="col mt-1 custom-select"
                onChange={(selectedOption: any) => {
                  setFilter((prevState: CommonMaster) => {
                    return {
                      ...prevState,
                      Category: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                options={subCategories?.map((e: any) => ({
                    label: e.codeValue,
                    value: e.codeValue,
                  }))}
                styles={{
                  menu: (provided: any) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              >
                
              </Select> */}
                      <Select
                        labelId="department"
                        required
                        inputRef={subCategoryRef}
                        error={modelError?.Department}
                        onChange={(e: any) => {
                          setModelError({ ...modelError, Department: false });
                          setUserDto({
                            ...userDto,
                            Department: e.target.value,
                          });
                        }}
                      >
                        {subCategories.map((option: any) => (
                          <MenuItem
                            key={option.codeValue}
                            value={option.codeValue}
                          >
                            {option.codeValue}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                </div>
              </div>
            </Item>
            {/* </Item> */}
            <Item>
              <Typography
                color="whitesmoke"
                sx={{
                  float: "left",
                  fontWeight: "bold",
                  width: "20%",
                  height: "20%",
                  backgroundColor: "Highlight",
                  borderRadius: "5px",
                }}
              >
                Set Department Time
              </Typography>
              <div
                className="top-div mx-auto shadow-container"
                style={{
                  border: "2px solid #3498db",
                  marginTop: "4%",
                  borderRadius: "20px",
                  padding: "30px",
                  width: "80%",
                  backgroundColor: "white",
                  transition: "box-shadow 0.3s ease",
                }}
              >
                <div
                  className="First row"
                  style={{ display: "flex", alignItems: "center" }}
                >
                  <div className="col-sm-6 " style={{ display: "flex" }}>
                    <div className="w-100" style={{ marginLeft: "145px" }}>
                      <label
                        style={{
                          marginRight: "35px",
                          fontWeight: "bold",
                          fontSize: "20px",
                        }}
                      >
                        Start Time
                      </label>
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DemoContainer components={["TimePicker"]}>
                          <TimePicker label="Start time picker" />
                        </DemoContainer>
                      </LocalizationProvider>
                    </div>
                  </div>
                  <div className="col-sm-6 " style={{ display: "flex" }}>
                    <div className="w-100" style={{ marginRight: "145px" }}>
                      <label
                        style={{
                          marginRight: "35px",
                          fontWeight: "bold",
                          fontSize: "20px",
                        }}
                      >
                        End Time
                      </label>
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DemoContainer components={["TimePicker"]}>
                          <TimePicker label="End time picker" />
                        </DemoContainer>
                      </LocalizationProvider>
                    </div>
                  </div>
                </div>
              </div>
            </Item>
            <Items>
              <Button variant="contained" sx={{ float: "left" }}>
                {" "}
                Save
              </Button>
              <Button variant="contained" sx={{ float: "right" }}>
                {" "}
                Reset
              </Button>
            </Items>
          </Stack>
        </Box>
      </div>
    </div>
  );
};
