import { IBase, Nullable } from "../Common/BaseModel";

export interface Requirement {
  TableName: string;
    ProjectId: number;
    ProjectObjectiveId: number | null;
    RequirementSource: string;
    RequirementType: string;
    RequirementDefinition: string;
    SourceDocumentSpecification: string;
    CodeFileName: string;
    Stages: string;
    Status: string;
    Design: string;
    Development: string;
    Testing: string;
    EstimatedReleaseDate: Date | null;
    ActualReleaseDate: Date | null;
    Documents?: Document[];
  }


export interface Document extends IBase {
  TableName: string;
  AttributeId: number;
  ProjectId: number | null;
  DocType: string;
  FileName: string;
  FileType: string;
  File: File;
  IsActive: boolean;
  IsActive: boolean,
  CreatedBy:string;
  UpdatedBy:string;
}