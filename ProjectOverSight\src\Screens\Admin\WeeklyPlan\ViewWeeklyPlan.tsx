import {
  Autocomplete,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  InputLabel,
  TextField,
  TextareaAutosize,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { ConvertToISO } from "../../../Utilities/Utils";
import { ModalAction } from "../../../Models/Common/AlertOptions";
import { IWeeklyPlan } from "../../../Models/WeeklyPlan/WeeklyPlan";
// import { Employee } from "../../../Models/Employee/Employee";
// import { Regex } from "../../../Constants/Regex/Regex";

type ViewWeeklyPlanrops = {
  open: ModalAction | null;
  setOpen: (open: ModalAction) => void;
  weeklyPlan: IWeeklyPlan | null;
};

export const ViewWeeklyPlan = ({
  open,
  setOpen,
  weeklyPlan,
}: ViewWeeklyPlanrops) => {
  return (
    <Dialog open={open?.view ?? false}>
      <div className="dialog-title-container">
        <DialogTitle className="dialog-title">Weekly Plan</DialogTitle>
        <CancelOutlinedIcon
          className="close-icon"
          onClick={() => setOpen({ view: false })}
        />
      </div>
      <DialogContent className="row popup d-flex justify-content-center">
      <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
          <Autocomplete
            options={[]}
            defaultValue={{
              label: weeklyPlan?.project,
              id: weeklyPlan?.projectId,
            }}
            // className="col-6 mb-2 "
            renderInput={(params) => (
              <TextField {...params} label="Project" className="read-only-input" InputProps={{
                readOnly: true,
              }}/>
            )}
          />
          </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
          <Autocomplete
            options={[]}
            defaultValue={weeklyPlan?.team}
            // className="col mb-2"
            renderInput={(params) => (
              <TextField {...params} label="Team" className="read-only-input"  InputProps={{
                readOnly: true,
              }}/>
            )}
          />
      </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
          <TextField
            className="read-only-input"
            label="Employee"
            defaultValue={weeklyPlan?.employees}            
            variant="outlined"
            InputProps={{
              readOnly: true,
            }}
          />
          </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
          <TextField
            className="col m-2 read-only-input"
            label="Weekly deliverable"
            defaultValue={`${weeklyPlan?.notes}`}
            type="text"
            variant="outlined"
            InputProps={{
              readOnly: true,
            }}
          />
      </FormControl>
            </Grid>
              <Grid item xs={12} md={12}>
            <FormControl fullWidth>
          <TextareaAutosize
            disabled
            // className="col  m-2 form-control"
            defaultValue={`${weeklyPlan?.description}`}
            placeholder="Description*"
            style={{ height: 100 }}
          />
       </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
          <TextField
            className="read-only-input"
            label="Estimated Hours"
            defaultValue={`${weeklyPlan?.estimatedHours}`}
            type="text"
            variant="outlined"
            InputProps={{
              readOnly: true,
            }}
          />
          </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
          <Autocomplete
            options={[]}
            // className="col-6 mb-2 mt-2"
            defaultValue={{ label: weeklyPlan?.category, id: 0 }}
            renderInput={(params) => <TextField {...params} label="Category" className="read-only-input"  InputProps={{
              readOnly: true,
            }}/>}
          />
        </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <InputLabel id="start-date">Due Date*</InputLabel>
            <FormControl fullWidth>
            <TextField
              id="start-date"
              margin="dense"
              className="read-only-input"
              defaultValue={ConvertToISO(`${weeklyPlan?.dueDate}`)}
              inputProps={{
                min: ConvertToISO(`${weeklyPlan?.dueDate}`),
                readOnly: true,
              }}
              type="date"
              fullWidth
              variant="outlined"
            />
          </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <InputLabel id="end-date">Week Ending Date*</InputLabel>
            <FormControl fullWidth>
            <TextField
              id="end-date"
              margin="dense"
              className="read-only-input"
              defaultValue={ConvertToISO(`${weeklyPlan?.weekEndingDate}`)}
              inputProps={{
                min: new Date().toISOString().slice(0, 10),
                readOnly: true,
              }}
              type="date"
              fullWidth
              variant="outlined"
            />
         </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
          <Autocomplete
            options={[]}
            defaultValue={{ label: `${weeklyPlan?.priority}`, id: "0" }}
            // className="col-6 mb-2 mt-2"
            renderInput={(params) => <TextField {...params} label="Priority" className="read-only-input"  InputProps={{
              readOnly: true,
            }} />}
          />
          </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
          <TextField
            defaultValue={weeklyPlan?.status}
            className="read-only-input"
            label="Status"
            InputProps={{
              readOnly: true,
            }}
          />
        </FormControl>
            </Grid>
            </Grid>
      </DialogContent>
      <DialogActions>
        <Button
          size="medium"
          variant="contained"
          color="success"
          onClick={() => setOpen({ view: false })}
        >
          Ok
        </Button>
      </DialogActions>
    </Dialog>
  );
};
