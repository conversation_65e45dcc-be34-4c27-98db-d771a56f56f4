import { IBase } from "../Common/BaseModel";

  export interface TProjectGroup extends IBase{
    name: string;
    status?: string | null;
    description: string;
    imagePath?: string | null;
    isActive?: boolean | null;
    file: File;
    image: any;
    projects:Array<Project>;
}
  
export interface ProjectGroupMapper extends IBase {
  projectId?: number | undefined | null;
  projectGroupId: number;
}