import Swal from "sweetalert2";
import { PasswordUpdateDto } from "../Models/Employee/User";
import { Regex } from "../Constants/Regex/Regex";

export function validatePassword(passwordDto: PasswordUpdateDto): boolean {
  if (passwordDto.email.length === 0) {
    Swal.fire({
      title: "Error",
      text: "Please enter email address.",
      icon: "error",
    });
    return false;
  }

  if (passwordDto.password.length < 8) {
    Swal.fire({
      title: "Error",
      text: "Password should be at least 8 characters.",
      icon: "error",
    });
    return false;
  }

  if (!Regex.PASSWORD_PATTERN.test(`${passwordDto.password}`)) {
    Swal.fire({
      title: "Error",
      text: "Password must contain atleast one special character,number and captial letter",
      icon: "error",
    });
    return false;
  }

  if (passwordDto.password !== passwordDto.confirmPassword) {
    Swal.fire({
      title: "Error",
      text: "Passwords does not match.",
      icon: "error",
    });
    return false;
  }

  return true;
}
