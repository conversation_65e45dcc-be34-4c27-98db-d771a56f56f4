//  public async Task<DashboardDto> GetDashboardData(User sessionUser, DateTime weekEndingDate)
//         {
//             try
//             {
//                 var day = _dbContext.Day.FirstOrDefault(x => x.Date.Date == DateTime.Now.Date);
//                 DateTime OfficeTime = new DateTime(1, 1, 1, 10, 10, 0);

//                 var empTask = _dbContext.EmployeeTask.Where(x => x.WeekEndingDate.Date == weekEndingDate.Date).AsEnumerable();
//                 var projects = _dbContext.Project.ToList();

//                 var teamList = (from team in _dbContext.Team
//                                 join teamEmployee in _dbContext.TeamEmployee
//                                 on team.Id equals teamEmployee.TeamId into teamEmpJoin
//                                 from teamEmployee in teamEmpJoin.DefaultIfEmpty()
//                                 join employeeTask in empTask
//                                 on teamEmployee.EmployeeId equals employeeTask.EmployeeId into taskJoin
//                                 from employeeTask in taskJoin.DefaultIfEmpty()
//                                 where teamEmployee.EndDate == null
//                                 group new { employeeTask, team } by new { team.Name, team.Id, team.IsActive } into g
//                                 select new TeamVM()
//                                 {
//                                     Name = g.Key.Name,
//                                     TeamId = g.Key.Id,
//                                     IsActive = (bool)g.Key.IsActive,
//                                     AssignedHours = g.Where(x => x.employeeTask.WeekEndingDate.Date == weekEndingDate.Date).Sum(t => t.employeeTask.EstTime),
//                                     UnAssignedHours = _dbContext.TeamEmployee.Where(x => x.TeamId == g.FirstOrDefault().team.Id && x.EndDate == null).Count() * 40 - g.Where(x => x.employeeTask.WeekEndingDate.Date == weekEndingDate.Date).Sum(t => t.employeeTask.EstTime),
//                                 }).OrderByDescending(t => t.UnAssignedHours).ToList();

//                 var teamProjects = (from project in _dbContext.Project
//                                     join teamProject in _dbContext.TeamProject on project.Id equals teamProject.ProjectId
//                                     where teamProject.EndDate == null
//                                     select new TeamProjectDto()
//                                     {
//                                         TeamId = teamProject.TeamId,
//                                         ProjectName = project.Name,
//                                         ProjectId = project.Id,
//                                         Status = project.Status,
//                                         StartDate = project.StartDate,
//                                         EndDate = project.EndDate,
//                                         Percentage = (int)project.Percentage,
//                                         IsActive = project.IsActive,
//                                         Hours = _dbContext.Task.Where(x => x.ProjectId == teamProject.ProjectId && x.WeekEndingDate.Date == weekEndingDate.Date).Sum(x => x.ActTime),
//                                         priorities = _dbContext.ProjectPriority.Where(x => x.ProjectId == teamProject.ProjectId).Select(x => x.Priority).FirstOrDefault(),
//                                     }).Distinct().OrderByDescending(x => x.Percentage).ToList();

//                 var teamEmployees = (from employee in _dbContext.Employee
//                                      join teamEmployee in _dbContext.TeamEmployee on employee.Id equals teamEmployee.EmployeeId
//                                      join users in _dbContext.Users on employee.UserId equals users.Id
//                                      where teamEmployee.EndDate == null
//                                      select new TeamEmployeeDto()
//                                      {
//                                          TeamId = teamEmployee.TeamId,
//                                          EmployeeId = employee.Id,
//                                          EmployeeName = users.Name,
//                                          AssignedHours = _dbContext.EmployeeTask.Where(x => x.EmployeeId == employee.Id && x.WeekEndingDate == weekEndingDate.Date).Sum(x => x.EstTime),
//                                          UnassignedHours = 40 - _dbContext.EmployeeTask.Where(x => x.EmployeeId == employee.Id && x.WeekEndingDate == weekEndingDate.Date).Sum(x => x.EstTime),
//                                      }).Distinct().ToList();

//                 var employeeTimes = (from employee in _dbContext.Employee
//                                      join teamEmployee in _dbContext.TeamEmployee on employee.Id equals teamEmployee.EmployeeId
//                                      join users in _dbContext.Users on employee.UserId equals users.Id
//                                      where teamEmployee.EndDate == null
//                                      select new EmployeeTimeDto()
//                                      {
//                                          UserId = employee.UserId,
//                                          TeamId = teamEmployee.TeamId,
//                                          EmployeeId = employee.Id,
//                                          EmployeeName = users.Name,
//                                          InTime = _dbContext.EmployeeTime.FirstOrDefault(x => x.EmployeeId == employee.Id && x.DayId == day.Id).InTime,
//                                          OutTime = _dbContext.EmployeeTime.FirstOrDefault(x => x.EmployeeId == employee.Id && x.DayId == day.Id).OutTime,
//                                      }).ToList();

//                 var teamWeeklyPlan = (from weeklyPlan in _dbContext.WeeklyPlan
//                                       join project in _dbContext.Project on weeklyPlan.ProjectId equals project.Id
//                                       where weeklyPlan.WeekEndingDate.Date == weekEndingDate.Date
//                                       select new WeeklyPlanDto()
//                                       {
//                                           ProjectName = project.Name,
//                                           Description = weeklyPlan.Description,
//                                           ProjectId = project.Id,
//                                           EmployeeId = weeklyPlan.EmpId,
//                                           TeamId = weeklyPlan.TeamId,
//                                           Status = weeklyPlan.Status,
//                                           DueDate = weeklyPlan.DueDate
//                                       }).ToList();

//                 foreach (var plan in teamWeeklyPlan)
//                 {
//                     var employees = plan.EmployeeId.Split("|").Select(int.Parse).ToList();
//                     plan.Employee = await _dbContext.Employee.Where(x => employees.Any(e => e == x.Id)).ToListAsync();
//                 }

//                 foreach (var emp in teamEmployees)
//                 {
//                     var query = (from et in _dbContext.EmployeeTask
//                                  join p in _dbContext.Project on et.ProjectId equals p.Id
//                                  where et.EmployeeId == emp.EmployeeId && et.WeekEndingDate.Date == weekEndingDate.Date
//                                  group et by new { p.Id, p.Name } into g
//                                  select new ProjectHours
//                                  {
//                                      Project = g.Key.Name,
//                                      Hours = g.Sum(x => x.EstTime)
//                                  }).ToList();
//                     emp.ProjectHours = query;
//                 }

//                 foreach (var obj in employeeTimes)
//                 {
//                     var totalTicks = _dbContext.EmployeeTime
//                                     .Where(x => x.EmployeeId == obj.EmployeeId && x.InTime != null)
//                                     .OrderByDescending(x => x.Id)
//                                     .Take(30)
//                                     .Select(x => x.InTime.Value.TimeOfDay.Ticks)
//                                     .DefaultIfEmpty()
//                                     .AsEnumerable()
//                                     .Average();
//                     obj.AvgInTime = new TimeSpan((long)totalTicks);
//                 }

//                 if (sessionUser.UserType == "Customer")
//                 {
//                     var customer = _dbContext.Employee.FirstOrDefault(x => x.UserId == sessionUser.Id);
//                     var customerProject = await _dbContext.CustomerProject.Where(x => x.EmployeeId == customer.Id).Select(x => x.ProjectId).ToListAsync();
//                     teamProjects = teamProjects.Where(x => customerProject.Any(id => id == x.ProjectId)).ToList();
//                     employeeTimes = employeeTimes.Where(x => teamProjects.Any(tp => tp.TeamId == x.TeamId)).ToList();
//                     teamList = teamList.Where(x => teamProjects.Any(tp => tp.TeamId == x.TeamId)).ToList();
//                     teamEmployees = teamEmployees.Where(x => teamProjects.Any(tp => tp.TeamId == x.TeamId)).ToList();
//                     projects = projects.Where(x => customerProject.Any(id => id == x.Id)).ToList();
//                 }

//                 var completedProject = projects.Where(x => x.Percentage == 100).Count();
//                 var onGoing = projects.Where(x => x.Percentage < 100 && x.Percentage > 0).Count();

//                 var dashboarDto = new DashboardDto()
//                 {
//                     EmployeeTime = employeeTimes,
//                     totalProject = projects.Count,
//                     completedProject = completedProject,
//                     onGoingProject = onGoing,
//                     TeamList = teamList,
//                     TeamEmployees = teamEmployees,
//                     TeamProjects = teamProjects,
//                     WeeklyPlan = teamWeeklyPlan
//                 };
//                 return dashboarDto;
//             }
//             catch (Exception ex)
//             {
//                 throw ex;
//             }
//         }
