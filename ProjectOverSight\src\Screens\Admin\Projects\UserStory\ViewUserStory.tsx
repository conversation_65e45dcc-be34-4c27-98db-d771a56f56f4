import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Autocomplete,
  Grid,
  TextareaAutosize,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

export const ViewUserStory = ({
  openDialog,
  setOpenDialog,
  Data,
  data,
}: any) => {
  const handleClose = () => {
    setOpenDialog({ view: false });
  };

  var objective: any[] = [];
  data?.data?.projectObjectiveMappings?.forEach((e: any) => {
    if (e.userStoryId === Data?.id)
      objective.push({ label: e.description, id: e.projectObjectiveId });
  });

  return (
    <div>
      <Dialog open={openDialog?.view}>
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue" }}>View User Story</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
          <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                value={Data?.name}
                className="read-only-input"
                label="User Story Name"
                type="text"
                disabled={Data?.name? false : true}
                variant="outlined"
              />
               </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <TextField
                  id="outlined-read-only-input"
                  label="Status"
                  className="read-only-input"
                  disabled={Data?.endDate? false : true}
                  defaultValue={Data?.status}
                  InputProps={{
                    readOnly: true,
                  }}
                />
             </FormControl>
            </Grid>
              <Grid item xs={12} >
              <InputLabel id="start-date">Description</InputLabel>
            <FormControl fullWidth>
            <TextareaAutosize
                required
                className="read-only-input"
                value={Data?.description}
                disabled={Data?.description? false : true}
                placeholder="Description"
                style={{ height: 100 }}
              />
            </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              <InputLabel id="start-date">Start date</InputLabel>
            <FormControl fullWidth>
                <TextField
                  disabled={Data?.startDate? false : true}
                  id="start-date"
                  className="read-only-input"
                  value={Data?.startDate?.slice(0, 10)}
                  margin="dense"
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              <InputLabel id="end-date">End date</InputLabel>
            <FormControl fullWidth>
                <TextField
                  disabled={Data?.endDate? false : true}
                  id="end-date"
                  className="read-only-input"
                  value={Data?.endDate?.slice(0, 10)}
                  margin="dense"
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              { objective[0] && <InputLabel className="mx-3" id="projectobjective">Project Objectives</InputLabel>}
            <FormControl fullWidth>
                <Autocomplete
  multiple
  disabled={objective.map((e:any) => e.label)? false : true}
  readOnly
  defaultValue={objective.map((e:any) => e.label)}
  options={[]} 
  sx={{ maxHeight: '150px', overflowY: 'auto' }}
  renderInput={(params: any) => (
    <TextField
      {...params}
      className="read-only-input"
      label={objective[0]? "" : "Select Objectives"}
      variant="outlined"
    />
  )}
/>

                {/* <Select
                  labelId="Project-Objective"
                  defaultValue={objetive?.id}
                  id="Status"
                  label="Project Objective"
                >
                  {data?.data?.length > 0 &&
                    data?.data?.map((e: any) => {
                      return (
                        <MenuItem value={e.id} key={e.id}>
                          {e.description}
                        </MenuItem>
                      );
                    })}
                </Select> */}
               </FormControl>
            </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="contained">
              ok
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
