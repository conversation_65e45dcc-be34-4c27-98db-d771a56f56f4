import {
  Dialog,
  InputLabel,
  DialogActions,
  Select,
  FormControl,
  DialogContent,
  Grid,
  MenuItem,
  Button,
  Typography,
} from "@mui/material";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { AlertOption, ModalAction } from "../../../Models/Common/AlertOptions";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { Get, Post } from "../../../Services/Axios";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

type AddWeeklyPlanProps = {
  open: ModalAction | null;
  setOpen: (open: ModalAction) => void;
  setReload: Dispatch<SetStateAction<boolean>>;
};

const formFields = ["ProjectList", "StartDate", "EndDate"];

export const Projectpriority = ({
  open,
  setOpen,
  setReload,
}: AddWeeklyPlanProps) => {
  const [ProjectList, setProjectList] = useState([]);
  const [ProjectPriority, setProjectPriority] = useState([]);
  const [PriorityId, setPriorityId] = useState([]);
  const { register, handleSubmit, resetField } = useForm();
  const [selectedProjects, setSelectedProjects] = useState([]);

  const handleProjectSelection = (_: any, value: any) => {
    const selectedIds = value.map((name: any) => {
      const project: any = ProjectList.find(
        (project: any) => project.name === name
      );
      return project ? project.id : null;
    });

    setSelectedProjects(selectedIds);
  };

  useEffect(() => {
    let projectList = Get("app/Project/GetProjectList");
    let GetProjectPriority = Get("app/WeeklyPlan/GetProjectPriority");
    projectList.then((response: any) => {
      setProjectList(response?.data || []);
    });
    GetProjectPriority.then((response: any) => {
      setProjectPriority(response?.data || []);
    });
  }, []);

  function reset() {
    formFields.map((e: string) => {
      resetField(e);
    });
  }
  const currentDate = new Date();
  const formattedDate = currentDate.toISOString();

  async function onSubmitHandler(data: any) {
    data.ProJectId = selectedProjects;
    const Id = ProjectPriority.filter((x: any) =>
      data.ProJectId.includes(x.projectId)
    );

    var ProjectDetails = {
      Ids: Id.map((item: any) => item.id),
      projectIds: data.ProJectId,
      startDate: data.startDate,
      endDate: data.endDate,
      priority: data.priority,
      CreatedBy: formattedDate,
      UpdatedBy: formattedDate,
    };

    const { error }: any = await Post(
      "app/WeeklyPlan/AddProjectPriority",
      ProjectDetails
    );
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Projects Priority Added Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
      handleClose();
    });
    formFields.map((e: string) => {
      resetField(e);
    });
    setPriorityId([]);
  }

  function handleClose() {
    reset();
    setOpen({ add: false });
  }

  return (
    <>
      <Dialog open={open?.Priority ?? false}>
        <Grid container>
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <div className="dialog-title-container">
              <Typography sx={{ pt: 2, pb: 2, pl: 2 }}>
                Project Priority
              </Typography>
              <CancelOutlinedIcon
                className="close-icon"
                onClick={() => setOpen({ add: false })}
              />
            </div>
            <DialogContent className="row popup d-flex justify-content-center">
              <div className="row">
                <Autocomplete
                  multiple
                  id="checkboxes-tags-demo"
                  options={ProjectList.filter(
                    (x: any) => x.isActive === true
                  ).map((e: any) => e.name)}
                  disableCloseOnSelect
                  className="col"
                  {...register("projectIds")}
                  onChange={handleProjectSelection}
                  getOptionLabel={(option) => option}
                  renderOption={(props, option, { selected }) => (
                    <li {...props}>
                      <Checkbox
                        icon={icon}
                        checkedIcon={checkedIcon}
                        style={{ marginRight: 8 }}
                        checked={selected}
                      />
                      {option}
                    </li>
                  )}
                  style={{ width: 500 }}
                  renderInput={(params) => (
                    <>
                      {/* <InputLabel id="start-date">Select Projects</InputLabel> */}
                      <TextField
                        {...params}
                        label="Select Project"
                        placeholder="Select Multiple Projects"
                        margin="dense"
                      />
                    </>
                  )}
                />
              </div>
              <div className="row">
                <div className="col">
                  <InputLabel id="start-date">Start Date</InputLabel>
                  <TextField
                    required
                    id="start-date"
                    margin="dense"
                    inputProps={{
                      min: new Date().toISOString().slice(0, 10),
                    }}
                    type="date"
                    {...register("startDate")}
                    fullWidth
                    variant="outlined"
                  />
                </div>
                <div className="col">
                  <InputLabel id="End-date">End Date</InputLabel>
                  <TextField
                    required
                    id="start-date"
                    margin="dense"
                    inputProps={{
                      min: new Date().toISOString().slice(0, 10),
                    }}
                    type="date"
                    {...register("endDate")}
                    fullWidth
                    variant="outlined"
                  />
                </div>
              </div>
              <div className="row">
                <FormControl className="col float-start mt-2 mx-2">
                  <InputLabel id="project-type">Priority</InputLabel>
                  <Select
                    labelId="Priority"
                    id="Prioritys"
                    label="Priority"
                    value={PriorityId}
                    {...register("priority")}
                    onChange={(e: any) => setPriorityId(e.target.value)}
                  >
                    <MenuItem value="High">High</MenuItem>
                    <MenuItem value="Medium">Medium</MenuItem>
                    <MenuItem value="Low">Low</MenuItem>
                  </Select>
                </FormControl>
              </div>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setOpen({ add: false })}
                size="medium"
                variant="contained"
                color="error"
              >
                Cancel
              </Button>
              <Button
                size="medium"
                variant="contained"
                color="success"
                type="submit"
              >
                Save
              </Button>
            </DialogActions>
          </form>
        </Grid>
      </Dialog>
    </>
  );
};
