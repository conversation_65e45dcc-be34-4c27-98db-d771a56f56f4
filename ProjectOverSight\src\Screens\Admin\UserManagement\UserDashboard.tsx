import { useEffect, useState } from "react";
import PersonAddAlt1Icon from "@mui/icons-material/PersonAddAlt1";
import GroupsIcon from "@mui/icons-material/Groups";
import PersonAddAltRoundedIcon from "@mui/icons-material/PersonAddAltRounded";
import PersonRemoveIcon from "@mui/icons-material/PersonRemove";
import { styled } from "@mui/material/styles";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { Get } from "../../../Services/Axios";
import EditIcon from "@mui/icons-material/Edit";
import EditUserList from "./EditUserList";
import "../../../StyleSheets/Navbar.css";

const UserDashboard = () => {
  const [users, setUsers] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [viewUserData, setviewUserData] = useState<any>({});
  const [userListView, setuserListView] = useState<any>({
    edit: false,
  });

  
  async function fetchData() {
    debugger;
    let UserList: any = await Get("app/UserManagement/GetAllUserslist");
    setUsers(UserList?.data || []);
  }

  useEffect(() => {
    fetchData();
  }, [loading]);

  const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
      backgroundColor: theme.palette.common.black,
      color: theme.palette.common.white,
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 14,
    },
  }));
  const StyledTableRow = styled(TableRow)(({ theme }) => ({
    "&:nth-of-type(odd)": {
      backgroundColor: theme.palette.action.hover,
    },
    // hide last border
    "&:last-child td, &:last-child th": {
      border: 0,
    },
  }));

  const totalUsers = users.length;
  const activeUsers = users.filter(
    (user: { isActive: any }) => user.isActive
  ).length;
  const inactiveUsers = users.filter(
    (user: { isActive: any }) => !user.isActive
  ).length;

  return (
    <>
      <div
        className="Main-body"
        style={{
          marginLeft: "10%",
          marginTop: "5%",
          width: "85%",
          height: "800px",
        }}
      >
        <div className="dashboard-div">
          <p
            style={{
              fontSize: "xx-large",
              height: "fit-content",
              color: "rgb(3, 108, 219)",
            }}
          >
            Dashboard
          </p>

          <div
            className="usermain-div"
            style={{
              display: "flex",
              justifyContent: "space-evenly",
              height: "fit-content",
              border: "1px solid lightgrey",
              borderTop: "3px solid rgb(3, 108, 219)",
              padding: "20px",
              borderRadius: "10px",
            }}
          >
            <div
              className="users-div d-flex"
              style={{
                backgroundColor: "#00a65a",
                color: "white",
                alignItems: "center",
                justifyContent: "space-around",
              }}
            >
              <div className="num-text">
                <h1>1</h1>
                <p>Online Users</p>
              </div>
              <div>
                <PersonAddAlt1Icon
                  sx={{
                    fontSize: "100px",
                    color: "#019451",
                  }}
                />
              </div>
            </div>

            <div
              className="users-div d-flex"
              style={{
                backgroundColor: "#00c0ef",
                color: "white",
                alignItems: "center",
                justifyContent: "space-around",
              }}
            >
              <div className="num-text">
                <h1>{totalUsers}</h1>
                <p>Total Users</p>
              </div>
              <div>
                <GroupsIcon
                  sx={{
                    fontSize: "100px",
                    color: "#02aad4",
                  }}
                />
              </div>
            </div>

            <div
              className="users-div d-flex"
              style={{
                backgroundColor: "#ffc107",
                color: "white",
                alignItems: "center",
                justifyContent: "space-around",
              }}
            >
              <div className="num-text">
                <h1>{activeUsers}</h1>
                <p>Active Users</p>
              </div>
              <div>
                <PersonAddAltRoundedIcon
                  sx={{
                    fontSize: "100px",
                    color: "#e0a904",
                  }}
                />
              </div>
            </div>

            <div
              className="users-div d-flex"
              style={{
                backgroundColor: "#de3343",
                color: "white",
                alignItems: "center",
                justifyContent: "space-around",
              }}
            >
              <div className="num-text">
                <h1>{inactiveUsers}</h1>
                <p>Inactive Users</p>
              </div>
              <div>
                <PersonRemoveIcon
                  sx={{
                    fontSize: "100px",
                    color: "#ba2f3c",
                  }}
                />
              </div>
            </div>
          </div>

          <div
            className="second-body"
            style={{
              width: "100%",
              height: "fit-content",
              marginTop: "40px",
            }}
          >
            <div
              className="d-flex"
              style={{
                justifyContent: "space-between",
                color: "rgb(3, 108, 219)",
              }}
            >
              <div style={{ flex: 1, marginLeft: "15px" }}>
                <h4>Recently Registerd</h4>
              </div>
              {/* <div style={{flex:1,textAlign:'center'}}>
                  <h4>Online Users</h4>
                  </div> */}
            </div>
            <div
              className="data-div "
              style={{ justifyContent: "space-between" }}
            >
              <div
                className="data-list"
                style={{
                  backgroundColor: "blue",
                  margin: "1%",
                  borderRadius: "5px",
                }}
              >
                <TableContainer component={Paper}>
                  <Table sx={{ minWidth: 700 }} aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <StyledTableCell align="center">Action</StyledTableCell>
                        <StyledTableCell align="center">Email </StyledTableCell>
                        <StyledTableCell align="center">
                          Full Name
                        </StyledTableCell>
                        <StyledTableCell align="center">Phone</StyledTableCell>
                        <StyledTableCell align="center">Status</StyledTableCell>
                      </TableRow>
                    </TableHead>

                    <TableBody>
                      {users.map((row: any, index: number) => (
                        <StyledTableRow key={index}>
                          <StyledTableCell align="center">
                            <EditIcon
                              className="fs-4 text-warning mx-2"
                              onClick={() => {
                                setuserListView({ edit: true});
                                setviewUserData(row);
                              }}
                            />
                          </StyledTableCell>
                          <StyledTableCell align="center">
                            {row.email}
                          </StyledTableCell>
                          <StyledTableCell align="center">
                            {row.userName}
                          </StyledTableCell>
                          <StyledTableCell align="center">
                            {row.phoneNumber}
                          </StyledTableCell>
                          <StyledTableCell align="center">
                            {row.isActive ? "Active" : "Inactive"}
                          </StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </div>
            </div>
          </div>
        </div>
      </div>
      <EditUserList
       openDialog={userListView}
       setOpenDialog={setuserListView}     
       Data={viewUserData}
       setLoading={setLoading}
      />
    </>
  );
};

export default UserDashboard;
