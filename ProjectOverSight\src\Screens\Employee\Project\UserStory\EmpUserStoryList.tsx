import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  FormControl,
  Autocomplete,
  TextField,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import DataTable from "react-data-table-component";
import AddIcon from "@mui/icons-material/Add";
import Box from "@mui/material/Box";
import DownloadIcon from "@mui/icons-material/Download";
import { ConvertDate } from "../../../../Utilities/Utils";
import { Get } from "../../../../Services/Axios";
import SearchIcon from "@mui/icons-material/Search";
import Select from "react-select";
import RefreshIcon from "@mui/icons-material/Refresh";
import { UserStory } from "../../../../Models/Project/UserStory";
import { DownloadUserStoryList } from "../../../../Services/ProjectService";
import { AddUserStory } from "./AddUserStory";
import { EditUserStory } from "./EditUserStory";
import { ViewUserStory } from "./ViewUserStory";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { Tooltip } from "@mui/material";
import AssignmentTurnedInIcon from "@mui/icons-material/AssignmentTurnedIn";
import { useQuery } from "react-query";
import { Project } from "../../../../Models/Project/Project";

export const EmpUserStoryList = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const location = useLocation();
  const [reload, setReload] = useState<boolean>(false);
  const [filter, setfilter] = useState<UserStory>({});
  const [rows, setRows] = useState<any>([]);
  const [userStorydata, setUserStorydata] = useState<any>();
  const [filterRows, setfilterRows] = useState<any>([]);
  const usNameRef = useRef<HTMLInputElement>(null);
  const statusRef = useRef<any>();
  const percentageRef = useRef<HTMLInputElement>(null);
  const actStartDateRef = useRef<HTMLInputElement>(null);
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const descriptionRef = useRef<HTMLInputElement>(null);
  const [userStoryView, setUserStoryView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });
  const [projects, setProjects] = useState([]);
  const [Project, setProject] = useState<any>({
    ProjectId: location.state.projectId,
    ProjectName: location.state.projectName,
  });

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      type: "Date",
      width: "15rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (params: any) => {
        return (
          <>
            <Tooltip
              className="mx-2"
              title="View"
              onClick={() => {
                setUserStoryView({ view: true });
                setUserStorydata(params);
              }}
            >
              <VisibilityIcon className="fs-4 text-info" />
            </Tooltip>

            <Tooltip
              className="mx-2"
              title="Edit"
              onClick={() => {
                setUserStoryView({ edit: true });
                setUserStorydata(params);
              }}
            >
              <EditIcon className="fs-4 text-warning" />
            </Tooltip>

            <Link
              className="mx-2"
              to="/Employee/AssignUI"
              state={{
                ...location?.state,
                UserStoryName: params.name,
                UserStoryId: params.id,
              }}
              style={{ textDecoration: "none" }}
            >
              <Tooltip title="Assign UI">
                <AssignmentTurnedInIcon className="fs-4 text-primary" />
              </Tooltip>
            </Link>
          </>
        );
      },
    },
    {
      field: "name",
      name: "Name",
      width: "15rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => <p className="tableStyle">{row.name}</p>,
    },
    {
      field: "description",
      name: "Description",
      width: "25rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => (
        <Tooltip title={row.description} style={{ textDecoration: "none" }}>
          <p className="tableStyle">
            {row.description.slice(0, 45)}
            {row.description.length > 45 ? "..." : null}
          </p>
        </Tooltip>
      ),
    },
    {
      field: "status",
      name: "Status",
      width: 135,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.status}</p>,
    },
    {
      field: "percentage",
      name: "Percentage",
      type: "number",
      width: 150,
      align: "right",
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.percentage}</p>,
    },
    {
      field: "startDate",
      name: "Start Date",
      type: "Date",
      width: 150,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (params: any) => {
        const result = ConvertDate(params?.startDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
    {
      field: "endDate",
      name: "End Date",
      type: "Date",
      width: 150,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (params: any) => {
        const result = ConvertDate(params?.endDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
  ];

  const { data, refetch } = useQuery("AddUserStory", () => {
    var objective: any = Get(
      `app/Project/GetProjectObjective?ProjectId=${Project.ProjectId}`
    );
    return objective;
  });

  useEffect(() => {
    setLoading(true);
    let userStoryList = Get(
      `app/Project/GetUserStoryList?projectId=${Project.ProjectId}`
    );
    userStoryList.then((response: any) => {
      setRows(response?.data || []);
      setfilterRows(response?.data || []);
    });

    let projectList = Get("app/Project/GetEmployeeProjectlist");
    projectList.then((response: any) => {
      setProjects(response.data || []);
      setLoading(false);
    });
  }, [reload]);

  const handleClickOpen = () => {
    setUserStoryView({ add: true });
  };

  function ApplyFilter() {
    let temp: any = [];

    if (filter.startDate != null) {
      if (filter.endDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].startDate.slice(0, 10) >= filter.startDate &&
          rows[i].endDate.slice(0, 10) <= filter.endDate
        ) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.name != null) {
      temp = temp.filter((e: any) => {
        return e.name.toLowerCase().search(filter.name?.toLowerCase()) >= 0;
      });
      setfilterRows(temp);
    }

    if (filter.description != null) {
      temp = temp.filter((e: any) => {
        return (
          e.description.toLowerCase() === filter.description?.toLowerCase()
        );
      });
      setfilterRows(temp);
    }
    if (filter.status != null) {
      temp = temp.filter((e: any) => {
        return e.status.toLowerCase() === filter.status?.toLowerCase();
      });
      setfilterRows(temp);
    }

    if (filter.percentage != null) {
      temp = temp.filter((e: any) => {
        return e.percentage === Number(filter.percentage);
      });
      setfilterRows(temp);
    }
  }

  function reset() {
    setfilter({});
    if (usNameRef.current) usNameRef.current.value = "";
    if (statusRef.current) statusRef.current.clearValue();
    if (percentageRef.current) percentageRef.current.value = "";
    if (actStartDateRef.current) actStartDateRef.current.value = "";
    if (actEndDateRef.current) actEndDateRef.current.value = "";
    if (descriptionRef.current) descriptionRef.current.value = "";

    setfilterRows(rows);
  }

  return (
    <div>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to="/Employee">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Link to="/Employee/Project">
          <Typography sx={{ fontWeight: "bold" }}>Project</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>User Story</Typography>
      </Breadcrumbs>
      <Grid container sx={{ mt: "2%" }}>
        <Grid xs={2}>
          <FormControl fullWidth sx={{ ml: "20%" }}>
            <Autocomplete
              options={projects
                .sort((a: Project, b: Project) =>
                  a.name!.localeCompare(b.name!)
                )
                .map((project: Project) => ({
                  label: project.name,
                  id: project.id,
                }))}
              onChange={(e: any, value: any) => {
                if (value)
                  setProject({
                    ProjectId: value.id,
                    ProjectName: value.label,
                  });
                setReload((prev) => !prev);
                return e;
              }}
              renderInput={(params: any) => (
                <TextField
                  {...params}
                  label="Select Project"
                  variant="filled"
                  required
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid xs={10}>
          <Typography align="center" className="fw-bolder fs-3">
            Project Name: {Project.ProjectName}
          </Typography>
        </Grid>
      </Grid>
      <div className="well mx-auto mt-4">
        <div className="row">
          <div className="col-sm-2">
            <div className="form-group">
              <label>User Story Name</label>
              <input
                id="User-Story-Name"
                placeholder="User Story Namee"
                ref={usNameRef}
                className="m-1 form-control col"
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const alphabeticValue = inputValue.replace(/[^A-Za-z]/g, ""); // Remove non-alphabetic characters
                  setfilter((prevState) => ({
                    ...prevState,
                    name: alphabeticValue === "" ? undefined : alphabeticValue,
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Description</label>
              <input
                id="Description"
                placeholder="Description"
                ref={descriptionRef}
                className="m-1 form-control col"
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const alphabeticValue = inputValue.replace(/[^A-Za-z]/g, ""); // Remove non-alphabetic characters
                  setfilter((prevState) => ({
                    ...prevState,
                    description:
                      alphabeticValue === "" ? undefined : alphabeticValue,
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="status"
                ref={statusRef}
                className="select-dropdowns mt-1 col"
                onInputChange={(inputValue: string) => {
                  const alphabeticValue = inputValue.replace(
                    /[^A-Za-z\s]/g,
                    ""
                  ); // Remove non-alphabetic characters
                  return alphabeticValue;
                }}
                onChange={(selectedOption: any) => {
                  if (selectedOption) {
                    setfilter((prevState) => ({
                      ...prevState,
                      status:
                        selectedOption.label.trim() === ""
                          ? null
                          : selectedOption.label,
                    }));
                  }
                }}
                options={[
                  {
                    label: "Active",
                    value: "Active",
                  },
                  {
                    label: "In Active",
                    value: "In Active",
                  },
                ]}
                placeholder="Status"
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Percentage</label>
              <input
                id="percentage"
                placeholder="Percentage"
                className="m-1 form-control col"
                ref={percentageRef}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const numericValue = parseFloat(inputValue);

                  if (!isNaN(numericValue) || inputValue === "") {
                    setfilter((prevState) => ({
                      ...prevState,
                      percentage: isNaN(numericValue)
                        ? undefined
                        : numericValue,
                    }));
                  }
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label className="mx-1">Start Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      startDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="start-date"
                placeholder="Start Date"
                ref={actStartDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label className="mx-1">End Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      endDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="end-date"
                placeholder="End Date"
                ref={actEndDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="container">
            <div className="row justify-content-end">
              <div className="col-auto">
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => ApplyFilter()}
                >
                  search
                </Button>
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => reset()}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center">
        <div className="col-11 col-s-4">
          <Grid>
            <Button
              variant="contained"
              className="mb-2 float-md-start"
              onClick={handleClickOpen}
              sx={{ ml: "3%" }}
            >
              Add User Story
              <AddIcon className="mx-1" />
            </Button>
            <Button
              variant="contained"
              className="mb-2 float-md-end"
              onClick={() => {
                DownloadUserStoryList(filterRows);
              }}
              sx={{ mr: "3%" }}
            >
              Download
              <DownloadIcon className="mx-1" />
            </Button>
          </Grid>
        </div>
        <Grid item xs={12} sm={11} sx={{ mt: "4%" }}>
          <div className="responsive-div col-3 col-s-3">
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                data={filterRows || []}
                fixedHeader={true}
                responsive
                persistTableHead
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },
                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                progressPending={loading}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[10, 20, 30, 40, 50]}
                pointerOnHover={true}
              />
            </Box>
          </div>
        </Grid>
      </div>
      <AddUserStory
        openDialog={userStoryView}
        setOpenDialog={setUserStoryView}
        setReload={setReload}
        data={data}
        projectId={Project.ProjectId}
        refetch={refetch}
      />
      <EditUserStory
        data={data ?? []}
        openDialog={userStoryView}
        setOpenDialog={setUserStoryView}
        Data={userStorydata}
        setReload={setReload}
        projectId={location.state.projectId}
      />
      <ViewUserStory
        openDialog={userStoryView}
        setOpenDialog={setUserStoryView}
        Data={userStorydata}
        data={data}
      />
    </div>
  );
};
