import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>rid,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
} from "@mui/material";
import { Link } from "react-router-dom";
import { Category } from "../../../Models/Common/CommonMaster";
import EditIcon from "@mui/icons-material/Edit";
import AddIcon from "@mui/icons-material/Add";
import { useContextProvider } from "../../../CommonComponents/Context";
import DataTable from "react-data-table-component";
import { UpsertCategory } from "./UpsertCategory";
import { useState } from "react";

function Categories() {
  const { category, setLoading } = useContextProvider();
  const [_category, setCategory] = useState<Category>();
  const [open, setOpen] = useState(false);
  const [action, setAction] = useState<"CREATE" | "EDIT">("CREATE");

  const columns: any = [
    {
      name: "Action",
      width: "10rem",
      selector: (row: Category) => {
        return (
          <>
            <Tooltip className="mx-1" title="Edit">
              <EditIcon
                className="fs-4 text-warning"
                onClick={() => {
                  setOpen(true);
                  setAction("EDIT");
                  setCategory(row);
                }}
              />
            </Tooltip>
          </>
        );
      },
    },
    {
      field: "categories",
      name: "Categories",
      width: "15rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: Category) => (
        <p className="tableStyle">{row.categories}</p>
      ),
    },
    {
      field: "subCategory",
      name: "Sub Category",
      width: "15rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: Category) => (
        <p className="tableStyle">{row.subCategory}</p>
      ),
    },
    {
      field: "uiApplicable",
      name: "UI Applicable",
      width: "15rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: Category) => (
        <p className="tableStyle">
          {row.uiApplicable ? "Applicable" : "Not Applicable"}
        </p>
      ),
    },
    {
      field: "userStoryApplicable ",
      name: "User Story Applicable",
      width: "12rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      align: "right",
      selector: (row: Category) => (
        <p className="tableStyle">
          {row.userStoryApplicable ? "Applicable" : "Not Applicable"}
        </p>
      ),
    },
    {
      field: "categorySequence",
      name: "Category Sequence",
      width: "12rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      align: "right",
      editable: false,
      right: true,
      selector: (row: Category) => (
        <p className="tableStyle">{row.categorySequence}</p>
      ),
    },
    {
      field: "subCategorySequence",
      name: "Sub Category Sequence",
      width: "12rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      align: "right",
      editable: false,
      right: true,
      selector: (row: Category) => (
        <p className="tableStyle">{row.subCategorySequence}</p>
      ),
    },
    {
      field: "isActive",
      name: "Status",
      width: "12rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      align: "right",
      editable: false,
      right: true,
      selector: (row: Category) => (
        <p className="tableStyle">{row.isActive ? "Active" : "In Active"}</p>
      ),
    },
  ];
  return (
    <div>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to="/Admin">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Categories</Typography>
      </Breadcrumbs>
      <div className="mt-5">
        <div className="d-flex flex-column justify-content-center align-items-center">
          <div className="col-11 col-s-4">
            <Grid>
              <Button
                variant="contained"
                className="mb-2 float-md-start"
                sx={{ ml: "3%" }}
                onClick={() => {
                  setOpen(true);
                  setAction("CREATE");
                  setCategory(undefined);
                }}
              >
                Add Category
                <AddIcon className="mx-1" />
              </Button>
            </Grid>
          </div>
          <Grid item xs={12} sm={11} sx={{ mt: "4%" }}>
            <div className="col-3 col-s-3">
              <Box style={{ width: "94vw", position: "relative" }}>
                <DataTable
                  columns={columns}
                  fixedHeader
                  responsive
                  persistTableHead
                  data={category || []}
                  customStyles={{
                    table: {
                      style: {
                        height: "80vh",
                        border: "1px solid rgba(0,0,0,0.1)",
                        overflowY: "scroll",
                      },
                    },

                    headRow: {
                      style: {
                        background: "#1e97e8",
                        fontSize: "16px",
                        color: "white",
                        fontFamily: "inherit",
                      },
                    },
                    pagination: {
                      style: {
                        position: "absolute",
                        width: "94vw",
                        background: "#daeef0",
                        color: "black",
                        textAlign: "right",
                        top: -55,
                        borderRadius: "5px 5px 0 0",
                      },
                    },
                  }}
                  pagination
                  paginationPerPage={50}
                  paginationRowsPerPageOptions={[50, 100, 200]}
                  pointerOnHover={true}
                />
              </Box>
            </div>
          </Grid>
        </div>
      </div>
      <UpsertCategory
        open={open}
        setOpen={setOpen}
        action={action}
        setLoading={setLoading}
        category={_category}
      />
    </div>
  );
}

export default Categories;
