import { useState, useEffect, useRef } from "react";
import Box from "@mui/material/Box";
import DataTable from "react-data-table-component";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import { Typography, Grid, Tooltip } from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import { Get, Post } from "../../../Services/Axios";
import Button from "@mui/material/Button";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import { TaskFilter } from "../../../Models/Task/TaskFilter";
import Select from "react-select";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { DownloadTaskList } from "../../../Services/TaskService";
import { ConvertDate, ConvertToISO } from "../../../Utilities/Utils";
import Swal from "sweetalert2";
import { AssignTask } from "./AssignTask";
import { EditTask } from "./EditTask";
import { ViewTask } from "./ViewTask";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import Assign from "../../../assets/Assign.png";
import Completed from "../../../assets/Completed.png";
import { useContextProvider } from "../../../CommonComponents/Context";
import { CUSTOMER, Roles } from "../../../Constants/Common/Roles";
import { FILTER } from "../../../Constants/Task/Task";
import { Team } from "../../../Models/Team/Team";
import { Employee } from "../../../Models/Employee/Employee";
import { CommonMaster } from "../../../Models/Common/CommonMaster";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import { SquareCard } from "../../../CommonComponents/Card";
import { COMPLETED, PENDING } from "../../../Constants/Common/Common";
import { PRIORITYTYPE } from "../../../Constants/Common/CommonMaster";
import "../../../App.css";
import DeleteIcon from "@mui/icons-material/Delete";
import { Task } from "../../../Models/Task/Task";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import BackDrop from "../../../CommonComponents/BackDrop";
import { Regex } from "../../../Constants/Regex/Regex";
import AddIcon from "@mui/icons-material/Add";
import { CreateTask } from "../../Employee/Task/AddTask/CreateTask";
const UNASSIGNED = "unassigned";

export const TaskList = () => {
  const [rows, setRows] = useState<any>([]);
  const [resetrows, setresetrows] = useState<any>([]);
  const [selecteRow, setSelectRow] = useState([]);
  const [categories, setcategories] = useState<any>([]);
  const [subCategories, setSubCategories] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [open, setOpen] = useState<boolean>(false);
  const [teamNames, setTeamNames] = useState<string[]>([]);
  const [employees, setEmployees] = useState<Array<Employee>>([]);
  const nameRef = useRef<HTMLInputElement>(null);
  const projectNameRef = useRef<any>(null);
  const statusRef = useRef<any>();
  const percentageRef = useRef<HTMLInputElement>(null);
  const categoryRef = useRef<any>();
  const subCategoryRef = useRef<any>();
  const priorityRef = useRef<any>();
  const actTimeRef = useRef<HTMLInputElement>(null);
  const estTimeRef = useRef<HTMLInputElement>(null);
  const actStartDateRef = useRef<HTMLInputElement>(null);
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const weekEndDateRef = useRef<HTMLInputElement>(null);
  const estStartDateRef = useRef<HTMLInputElement>(null);
  const estEndDateRef = useRef<HTMLInputElement>(null);
  const [filter, setfilter] = useState<TaskFilter>(FILTER);
  const teamNameRef = useRef<any>(null);
  const empNameRef = useRef<any>(null);
  const [taskdata, settaskdata] = useState<any>();
  const location = useLocation();
  const [totalActTime, setTotalActTime] = useState(0);
  const [totalEstTime, setTotalEstTime] = useState(0);
  const [addTask, setAddTask] = useState(false);
  const [projectGroup, setProjectGroup] = useState([]);
  const [Page, setPage] = useState(0);
  // const [rowsPerPage, setRowsPerPage] = useState(50);
  const { state } = useLocation();
  const navigate = useNavigate();

  const [taskListView, setTaskListView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });
  var categorySet = new Set<any>();
  var ProjectSet = new Set<any>();
  var category: string[] = [];
  var [Projects, setProjects] = useState<string[]>([]);

  const { role, commonMaster } = useContextProvider();

  console.log(rows);

  const columns: any = [
    {
      name: "Action",
      center: true,
      width: "13rem",
      selector: (row: any) => (
        <>
          <Tooltip
            className="mx-2"
            title="View"
            onClick={() => {
              setTaskListView({ view: true });
              settaskdata(row);
            }}
          >
            <VisibilityIcon className="fs-4 text-info" />
          </Tooltip>

          <Tooltip
            className="mx-2"
            title={row.percentage === 100 ? "Completed" : "Edit Task"}
            onClick={() => {
              if (!(row.percentage === 100)) {
                setTaskListView({ edit: true });
                settaskdata(row);
              }
            }}
          >
            {row.percentage === 100 ? (
              <TaskAltIcon className="fs-4 text-success" />
            ) : (
              <EditIcon className="fs-4 text-warning" />
            )}
          </Tooltip>
          {role != CUSTOMER && (
            <>
              <Tooltip title="Delete" className="mx-2">
                <DeleteIcon
                  className={`fs-4  ${
                    row.status.toLowerCase() === UNASSIGNED
                      ? "text-danger"
                      : "text-secondary"
                  }`}
                  onClick={() => {
                    if (row.status.toLowerCase() === UNASSIGNED) {
                      row.isActive = false;
                      row.employeeTaskId = 0;
                      row.CreatedBy = "user";
                      row.UpdatedBy = "user";
                      deleteTask(row);
                    }
                  }}
                />
              </Tooltip>

              <Tooltip
                className="mx-2"
                onClick={() => {
                  if (row.status?.toLowerCase() === UNASSIGNED) {
                    setSelectRow(row);
                    setOpen(true);
                  }
                }}
                title={`${
                  row.status?.toLowerCase() === UNASSIGNED
                    ? "Assign Task"
                    : row.status
                }`}
              >
                <img
                  src={
                    row.status?.toLowerCase() !== UNASSIGNED
                      ? Completed
                      : Assign
                  }
                  width={25}
                  height={25}
                />
              </Tooltip>
            </>
          )}
        </>
      ),
    },
    {
      name: "Task Name",
      width: "15rem",
      selector: (row: any) => (
        <Tooltip title={row.name} style={{ textDecoration: "none" }}>
          <p
            className={`tableStyle ${
              ConvertToISO(row?.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? (role == "Customer" ? "" : "text-danger")
                : ""
            }`}
          >
            {row?.name}
          </p>
        </Tooltip>
      ),
    },
    {
      name: "Project Name",
      width: "15rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row?.projectName}
        </p>
      ),
    },
    {
      name: "Priority",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.priority || "-"}
        </p>
      ),
    },
    {
      name: "Team Name",
      width: "10rem",
      selector: (row: any) =>
        !location?.state?.projectReportRoute ? (
          (role != "Customer") ? ( <Link
            to={`/${role}/TeamTaskQuadrant`}
            style={{ textDecoration: "none" }}
            state={{ data: row, route: "taskList" }}
            className={`tableStyle`}
          >
            {row.teamName}
          </Link>):(<h6 style={{color:"blue"}}>{row?.teamName}</h6>)
        ) : (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? (role == "Customer" ? "" : "text-danger")
                : ""
            }`}
          >
            {row.teamName || "-"}
          </p>
        ),
    },
    {
      name: "Employee Name",
      width: "15rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.employeeName?.replace(Regex.CHARACTER, " ") || "-"}
        </p>
      ),
    },
    {
      name: "Status",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.status || "-"}
        </p>
      ),
    },
    {
      name: "Week Ending Date",
      type: "Date",
      width: "10rem",
      selector: (row: any) => {
        const result = ConvertDate(row.weekEndingDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? (role == "Customer" ? "" : "text-danger")
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "Actual Start Date",
      width: "10rem",
      selector: (row: any) => {
        const result = ConvertDate(row.actualStartDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? (role == "Customer" ? "" : "text-danger")
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "Actual End Date",
      width: "10rem",
      selector: (row: any) => {
        const result = ConvertDate(row.actualEndDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? (role == "Customer" ? "" : "text-danger")
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "Estimated Start Date",
      type: "Date",
      width: "12rem",
      selector: (row: any) => {
        const result = ConvertDate(row.estimateStartDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? (role == "Customer" ? "" : "text-danger")
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "Estimated End Date",
      width: "12rem",
      selector: (row: any) => {
        const result = ConvertDate(row.estimateEndDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? (role == "Customer" ? "" : "text-danger")
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "US Name",
      width: "15rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.usName || "-"}
        </p>
      ),
    },
    {
      name: "UI Name",
      width: 200,
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.uiName || "-"}
        </p>
      ),
    },
    {
      name: "Description",
      width: "25rem",
      selector: (row: any) => (
        <Tooltip title={row.description} style={{ textDecoration: "none" }}>
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? (role == "Customer" ? "" : "text-danger")
                : ""
            }`}
          >
            {row?.description.slice(0, 45)}
            {row?.description.length > 45 ? "..." : ""}
          </p>
        </Tooltip>
      ),
    },
    {
      name: "Category",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.category || "-"}
        </p>
      ),
    },
    {
      name: "Sub Category",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.subCategory || "-"}
        </p>
      ),
    },
    {
      name: "Percentage (%)",
      right: true,
      width: "9rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.percentage || "-"}
        </p>
      ),
    },
    {
      name: "Estimated Time (hrs)",
      right: true,
      width: "12rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.estimateTime || "-"}
        </p>
      ),
    },
    {
      name: "Actual Time (hrs)",
      width: "12rem",
      right: true,
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? (role == "Customer" ? "" : "text-danger")
              : ""
          }`}
        >
          {row.actualTime || "-"}
        </p>
      ),
    },
  ];

  async function GetData() {
    let employeeList: any = await Get("app/Employee/GetEmployeeList");
    setEmployees(employeeList.data || []);
  }

  const handleResetState = () => {
    navigate(".", { state: {} });
  };
  useEffect(() => {
    if (location.state?.projectReportRoute) {
      filter.projectName = location?.state?.projectName;
    }
    let taskList;
    if (role === Roles.CUSTOMER) {
      taskList = Get("app/EmployeeTask/GetEmpTask");
    }else{
      taskList = Post(`app/Task/GetTaskList?month=10&year=2023`, filter);
    }
    
    let categoriesList = Get("app/Project/GetCategoriesList");
    let projectList = Get("app/Project/GetProjectList");
    // let EmployeeTask = Get("app/EmployeeTask/GetEmployeeTasks");
    let teamList = Get("app/Team/GetTeamList");
    var projectsGroups = Get("app/Project/GetProjectgrouplist");
    GetData();
  
    let teamNames = new Set<string>();
    teamList?.then((response: any) => {
      response?.data?.forEach((team: Team) => {
        teamNames?.add(team?.name);
      });
      setTeamNames([...teamNames]);
    });
  
    projectsGroups.then((res: any) => {
      setProjectGroup(res.data);
    });
  
    taskList?.then((response: any) => {
      const allRows = response?.data || [];
      let filteredRows = allRows;
  

        if (location?.state?.projectReportRoute) {
          if (location?.state?.status === COMPLETED) {
            filteredRows = allRows.filter((x: any) => x.percentage === 100);
          } else if (location?.state?.status === PENDING) {
            filteredRows = allRows.filter((x: any) => parseInt(`${x.percentage}`) < 100);
          }
        }
  
        if (state?.taskId?.length > 0) {
          filteredRows = filteredRows.filter((row: any) => state.taskId.includes(row?.id));
        } else if (state?.taskId?.length === 0) {
          filteredRows = [];
        }
  
        updateRows(filteredRows);
      
  
      setresetrows(rows);
      setLoading(false);
  
      if (rows) {
        let totalActTime = 0;
        let totalEstTime = 0;
  
        for (const task of rows) {
          totalActTime += task?.actualTime;
          totalEstTime += task?.estimateTime;
        }
  
        setTotalActTime(totalActTime);
        setTotalEstTime(totalEstTime);
      }
    });
  
    categoriesList.then((response: any) => {
      setcategories(response?.data || []);
    });
  
    var temp: string[] = [];
    projectList.then((response: any) => {
      if (Array.isArray(response.data)) {
        response.data.forEach((e: any) => {
          temp.push(e.name);
        });
        setProjects(temp);
      }
    });
    setProjects(temp);
  
    if (Array.isArray(rows)) {
      rows.forEach((row: any) => {
        ProjectSet?.add(row?.projectName);
      });
    }
    Autofilter();
  }, [loading, location?.state]);
  
  function updateRows(filteredRows: any[]) {
    setRows(filteredRows);
  }
  

  categories?.forEach((element: any) => {
    categorySet?.add(element.categories);
  });

  category = [...categorySet];
  category?.sort((a, b) => {
    return a.toLowerCase() < b.toLowerCase() ? -1 : 1;
  });

  async function Autofilter() {
    let temps = resetrows;
    if (state?.WeekEndingDateCurr) {
      temps = temps.filter(
        (x: any) =>
          x?.weekEndingDate?.toString().slice(0, 10) ===
          state?.WeekEndingDateCurr
      );
      setRows(temps);
    } else {
      setRows(resetrows);
    }

    if (state?.status) {
      temps = temps?.filter((x: any) => x?.status === state?.status);
      setRows(temps);
    }

    if (state?.teamName) {
      temps = temps?.filter((x: any) => x.team === state?.teamName);
      setRows(temps);
    }

    if (state?.projectName) {
      temps = temps?.filter((x: any) => x.projectName === state?.projectName);
      setRows(temps);
    }
  }

  const handleCategoryChange = (event: any) => {
    if (subCategoryRef.current) subCategoryRef.current.clearValue();
    let temp: any = [];
    setfilter((prevState) => {
      return {
        ...prevState,
        category: event?.label == "" ? null : event?.label,
      };
    });
    categories?.forEach((element: any) => {
      if (element.categories === event?.label) {
        temp.push(element.subCategory);
      }
    });
    temp.sort((a: any, b: any) => {
      return a.toLowerCase() < b.toLowerCase() ? -1 : 1;
    });
    setSubCategories(temp);
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  async function deleteTask(task: Task) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const { error }: any = await Post("app/Task/UpdateTask", task);
        var option: AlertOption;
        if (error) {
          option = {
            title: "Error",
            text: "Error Occured While Deleting!",
            icon: "error",
          };
        } else {
          option = {
            title: "Success",
            text: "User Story has been deleted!",
            icon: "success",
          };
        }
        Swal.fire({
          ...option,
          confirmButtonColor: "#3085d6",
        });
        setLoading((prev) => !prev);
      }
    });
  }

  function ApplyFilter() {
    if (filter.actStartDate != null && filter.estStartDate != null) {
      Swal.fire({
        text: "Please Select Either Actual Dates or Estimated Dates!",
        icon: "warning",
        confirmButtonColor: "#3085d6",
      });
      return;
    }

    if (filter.actStartDate != null) {
      if (filter.actEndDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
    }

    if (filter.estStartDate != null) {
      if (filter.estEndDate == null) {
        estEndDateRef.current?.focus();
        return;
      }
    }

    setLoading((prev) => !prev);
  }

  const handleChangePage = (page: number) => {
    setfilter((prevState) => {
      return {
        ...prevState,
        direction: Page > page ? "PREVIOUS" : "NEXT",
      };
    });
    setfilter((prevState) => {
      return {
        ...prevState,
        pageNumber: page,
      };
    });
    setPage(page);
    setLoading((prev) => !prev);
  };

  const handleChangeRowsPerPage = (currentRowsPerPage: number, _: number) => {
    setfilter((prevState) => {
      return {
        ...prevState,
        pageSize: currentRowsPerPage,
      };
    });
    setfilter((prevState) => {
      return {
        ...prevState,
        pageNumber: 0,
      };
    });
    setLoading((prev) => !prev);
  };

  function reset() {
    setfilter(FILTER);
    if (nameRef.current) nameRef.current.value = "";
    if (projectNameRef.current) projectNameRef.current.clearValue();
    if (statusRef.current) statusRef.current.clearValue();
    if (categoryRef.current) categoryRef.current.clearValue();
    if (subCategoryRef.current) subCategoryRef.current.clearValue();
    if (percentageRef.current) percentageRef.current.value = "";
    if (actTimeRef.current) actTimeRef.current.value = "";
    if (estTimeRef.current) estTimeRef.current.value = "";
    if (actStartDateRef.current) actStartDateRef.current.value = null!;
    if (actEndDateRef.current) actEndDateRef.current.value = null!;
    if (weekEndDateRef.current) weekEndDateRef.current.value = null!;
    if (estStartDateRef.current) estStartDateRef.current.value = "";
    if (estEndDateRef.current) estEndDateRef.current.value = "";
    if (teamNameRef.current) teamNameRef.current.clearValue();
    if (empNameRef.current) empNameRef.current.clearValue();
    if (priorityRef.current) priorityRef.current.clearValue();
    setLoading((prev) => !prev);
    setRows(resetrows);
    handleResetState();
  }

  return (
    <>
      {!location.state?.projectReportRoute && (
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>Task</Typography>
        </Breadcrumbs>
      )}

      {location.state?.projectReportRoute && (
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link color="inherit" to={`/${role}/Project`}>
            <Typography sx={{ fontWeight: "bold" }}>Projects</Typography>
          </Link>
          <Link
            color="inherit"
            to={`/${role}/ProjectQuadrant`}
            state={{ ...location.state }}
          >
            <Typography sx={{ fontWeight: "bold" }}>
              Project Quadrant
            </Typography>
          </Link>
          <Link
            color="inherit"
            to={`/${role}/ProjectQuadrant/ProjectReport`}
            state={{ ...location.state }}
          >
            <Typography sx={{ fontWeight: "bold" }}>Project Report</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>Task</Typography>
        </Breadcrumbs>
      )}

      <div className="well mx-auto mt-4">
        <div className="row">
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Team Name</label>
              <Select
                aria-label="Floating label select example"
                name="status"
                ref={teamNameRef}
                defaultValue={
                  state?.teamName
                    ? { value: state?.teamName, label: state?.teamName }
                    : null
                }
                className="mt-1"
                onChange={(code: any) => {
                  if (code) {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        teamName: code?.label == "" ? null : code?.label,
                      };
                    });
                  } else {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        teamName: code,
                      };
                    });
                  }
                }}
                options={teamNames
                  .slice()
                  .sort((a, b) => a.localeCompare(b))
                  .map((name: string) => {
                    return {
                      value: name,
                      label: name,
                    };
                  })}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
                isSearchable={true}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Employee Name</label>
              <Select
                aria-label="Floating label select example"
                ref={empNameRef}
                className="mt-1"
                onChange={(selectedOption: any) => {
                  setfilter((prevState) => {
                    return {
                      ...prevState,
                      employeeName:
                        selectedOption?.value == ""
                          ? null
                          : selectedOption?.value,
                    };
                  });
                }}
                options={employees
                  .sort((a, b) =>
                    a.name && b.name ? a.name.localeCompare(b.name) : 0
                  )
                  .map((employee: Employee) => {
                    return {
                      value: employee.name,
                      label: employee.name?.replace(Regex.CHARACTER, " "),
                    };
                  })}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
                isSearchable={true}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Project Name</label>
              <Select
                id="project-name"
                ref={projectNameRef}
                isMulti
                defaultValue={
                  state?.projectName
                    ? { value: state?.projectName, label: state?.projectName }
                    : null
                }
                className="col mt-1 custom-select"
                onChange={(selectedOption: any) => {
                  var projectName: string[] = [];
                  selectedOption?.forEach((e: any) => {
                    projectName.push(e.value);
                  });
                  setfilter((prevState: TaskFilter) => {
                    return {
                      ...prevState,
                      projectName: projectName.length > 0 ? projectName : null,
                    };
                  });
                }}
                options={Projects.slice()
                  .sort((a, b) => a.localeCompare(b))
                  .map((e: any) => {
                    return {
                      value: e,
                      label: e,
                    };
                  })}
                  styles={{
                    menu: (provided) => ({
                      ...provided,
                      zIndex: 1000,
                    }),
                    valueContainer: (provided) => ({
                      ...provided,
                      maxHeight: "40px",
                      overflowY: "auto",
                    }),
                    control: (provided) => ({
                      ...provided,
                      maxHeight: "150px",
                      overflowY: "auto",
                    }),
                  }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Project Group</label>
              <Select
                onChange={(code: any) => {
                  if (code) {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        projectGroupId: code?.value == "" ? null : code?.value,
                      };
                    });
                  } else {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        projectGroupId: code,
                      };
                    });
                  }
                }}
                options={projectGroup?.map((item: any) => ({
                  label: item.name,
                  value: item.id,
                }))}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                aria-label="Floating label select example"
                defaultValue={
                  state?.status
                    ? { value: state?.status, label: state?.status }
                    : null
                }
                name="status"
                ref={statusRef}
                isMulti
                className="mt-1"
                onChange={(selectedOption: any) => {
                  var status: string[] = [];
                  selectedOption?.forEach((e: any) => {
                    status.push(e.value);
                  });
                  setfilter((prevState) => ({
                    ...prevState,
                    status: status.length > 0 ? status : null,
                  }));
                }}
                options={[
                  {
                    label: "Assigned",
                    value: "Assigned",
                  },
                  {
                    label: "Completed",
                    value: "Completed",
                  },
                  {
                    label: "In Progress",
                    value: "In-Progress",
                  },
                  {
                    label: "On Hold",
                    value: "On Hold",
                  },
                  {
                    label: "Unassigned",
                    value: "Unassigned",
                  },
                ]}
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Priority</label>
              <Select
                id="priority"
                ref={priorityRef}
                isMulti
                className="col mt-1 custom-select"
                onChange={(selectedOption: any) => {
                  var priority: string[] = [];
                  selectedOption?.forEach((e: any) => {
                    priority.push(e.value);
                  });
                  setfilter((prevState: TaskFilter) => {
                    return {
                      ...prevState,
                      priority: priority.length > 0 ? priority : null,
                    };
                  });
                }}
                options={commonMaster
                  .filter((x) => x.codeType === PRIORITYTYPE)
                  .map((priority: CommonMaster) => ({
                    label: priority.codeValue,
                    value: priority.codeValue,
                  }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Percentage</label>
              <input
                id="percentage"
                className="m-1 form-control col"
                placeholder="Percentage"
                ref={percentageRef}
                onChange={(e) => {
                  let value = e.target.value.replace(/[^0-9]/g, "");
                  let numericValue = parseInt(value);
          
                  if (numericValue > 100) {
                    numericValue = 100;
                  }
                  e.target.value = numericValue ? numericValue.toString() : "";
                  setfilter((prevState) => ({
                    ...prevState,
                    percentage: numericValue,
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Actual Time</label>
              <input
                id="actual-time"
                className="m-1 form-control col"
                ref={actTimeRef}
                placeholder="Actual Time"
                type="text"
                onChange={(e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  setfilter((prevState) => ({
                    ...prevState,
                    actualTime: parseInt(e.target.value),
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Estimated Time</label>
              <input
                id="estimated-time"
                className="m-1 col form-control"
                ref={estTimeRef}
                placeholder="Estimated Time"
                type="number"
                onChange={(e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  setfilter((prevState) => ({
                    ...prevState,
                    estimatedTime: parseInt(e.target.value),
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Category</label>
              <Select
                id="category-simple-select"
                ref={categoryRef}
                className="col mt-1"
                onChange={handleCategoryChange}
                options={category.map((opt: any) => ({
                  label: opt,
                  value: opt,
                }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 99999,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Sub Category</label>
              <Select
                id="sub-category-simple-select"
                className="mt-1"
                ref={subCategoryRef}
                options={subCategories.map((opt: any) => ({
                  label: opt,
                  value: opt,
                }))}
                isSearchable={true}
                onChange={(selectedOption: any) => {
                  setfilter((prevState) => ({
                    ...prevState,
                    subCategory: selectedOption ? selectedOption.value : null,
                  }));
                }}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label className="mx-1">Estimated Start Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState) => {
                    return {
                      ...prevState,
                      estStartDate:
                        e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                ref={estStartDateRef}
                type="date"
                id="estimated-start-date"
                placeholder="Estimated Start Date"
                className="m-1  form-control"
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label className="mx-1">Estimated End Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState) => {
                    return {
                      ...prevState,
                      estEndDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                ref={estEndDateRef}
                type="date"
                id="estimated-end-date"
                placeholder="Estimated End Date"
                className="m-1 col form-control"
              />
            </div>
          </div>
          {/* <div className="container">
            <div className="row"> */}
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label className="mx-1">Actual Start Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState) => {
                    return {
                      ...prevState,
                      actStartDate:
                        e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="actual-start-date"
                placeholder="Actual Start Date"
                ref={actStartDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label className="mx-1">Actual End Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState) => {
                    return {
                      ...prevState,
                      actEndDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="actual-end-date"
                placeholder="Actual End Date"
                ref={actEndDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label className="mx-1">Week Ending Date</label>
              <input
                defaultValue={
                  state?.WeekEndingDateCurr
                    ? formatDate(state.WeekEndingDateCurr)
                    : ""
                }
                onChange={(e: any) => {
                  setfilter((prevState) => {
                    return {
                      ...prevState,
                      weekEndingDate:
                        e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="actual-end-date"
                placeholder="Actual End Date"
                ref={weekEndDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>

          <div className="col-md-12">
            <div className="row justify-content-end">
              <div className="col-auto">
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-3 mt-4"
                  onClick={() => ApplyFilter()}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-3 mt-4"
                  onClick={() => reset()}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
          {/* </div>
          </div> */}
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center mb-5">
        <div
          className="col-11 d-flex-inline"
          style={{ display: "inline-flex", justifyContent: "space-between" }}
        >
          <div>
            <div className="d-flex">
              <SquareCard
                text={`Total Act Hours : ${parseFloat(
                  `${totalActTime}`
                ).toFixed(2)}`}
                classNames="squareCard"
              />
              <SquareCard
                text={`Total Est Hours : ${parseFloat(
                  `${totalEstTime}`
                ).toFixed(2)}`}
                classNames="squareCard"
              />
            </div>
            {/* {role === CUSTOMER && (
              <Button
                variant="contained"
                className="mb-5"
                onClick={() => setAddTask(true)}
              >
                Add Task
                <AddIcon className="mx-1" />
              </Button>
            )} */}
          </div>
          <Grid>
            <Button
              variant="contained"
              className="mb-2 object-contain"
              onClick={() => {
                if (rows.length == 0) {
                  Swal.fire({
                    text: "No data to download!",
                    icon: "warning",
                    confirmButtonColor: "#3085d6",
                  });
                  return;
                }
                DownloadTaskList(rows);
              }}
            >
              Download
              <DownloadIcon className="mx-1 w-[9rem]" />
            </Button>
          </Grid>
        </div>
        <div className="d-flex flex-column justify-content-center align-items-center">
        <div
          className="responsive-div"
          style={{ marginTop: role === CUSTOMER ? "0%" : "4%", width: "94vw" }}
        >
          {role === CUSTOMER && (
              <Button
                variant="contained"
                className="mb-5"
                onClick={() => setAddTask(true)}
              >
                Add Task
                <AddIcon className="mx-1" />
              </Button>
            )}
        <Grid item xs={12} sm={11} sx={{ mt: "2%" }}>
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                fixedHeader={true}
                responsive
                persistTableHead
                progressPending={loading}
                data={rows || []}
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },
                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
                paginationComponentOptions={{
                  rowsPerPageText: "Rows per page:",
                  rangeSeparatorText: "of",
                  noRowsPerPage: false,
                  selectAllRowsItem: false,
                  selectAllRowsItemText: "All",
                }}
                onChangePage={handleChangePage}
                onChangeRowsPerPage={handleChangeRowsPerPage}
                paginationTotalRows={rows?.length && rows[0]?.totalCount}
                paginationServer
              />
              {/* <DataTable
                columns={columns}
                fixedHeader={true}
                responsive
                persistTableHead
                progressPending={loading}
                data={rows || []}
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },
                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
              /> */}
            </Box>
        </Grid>
        </div>
      </div>
        <BackDrop open={loading} />
      </div>
      <AssignTask
        openDialog={open}
        setOpenDialog={setOpen}
        selectedRow={selecteRow}
        setLoading={setLoading}
      />
      <EditTask
        openDialog={taskListView}
        setOpenDialog={setTaskListView}
        Data={taskdata}
        setLoading={setLoading}
      />
      <ViewTask
        openDialog={taskListView}
        setOpenDialog={setTaskListView}
        Data={taskdata}
      />
      <CreateTask open={addTask} setOpen={setAddTask} setReload={setLoading} />
    </>
  );
};
