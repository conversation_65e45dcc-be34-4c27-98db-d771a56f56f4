import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  TextField,
  InputLabel,
  TextareaAutosize,
  DialogActions,
  Button,
  Grid,
} from "@mui/material";
import * as React from "react";
import "../../../StyleSheets/EditTask.css";
import { ConvertToISO } from "../../../Utilities/Utils";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { TransitionProps } from "@mui/material/transitions";
import Slide from "@mui/material/Slide";


const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export const ViewTask = ({ openDialog, setOpenDialog, Data }: any) => {  
  console.log("Data.actualEndDate:", Data?.actualEndDate); 
  const defaultValue = Data?.actualEndDate ? ConvertToISO(Data.actualEndDate) : '-';
  console.log("Converted Date:", defaultValue);
  const handleClose = () => {
    setOpenDialog({ view: false });
  };

  return (
    <div>
      <Dialog open={openDialog?.view}  TransitionComponent={Transition}>
        {/* <div
          style={{
            maxHeight: "80vh",
            overflowY: "auto",
            overflowX: "hidden",
            position: "relative",
          }}
        > */}
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>Task</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
            {/* <span className="info-label">Project Name:</span>{" "}
              <span className="info-value">{Data?.projectName}</span>
              <br />
              <span className="info-label">Task Name:</span>{" "}
              <span className="info-value">{Data?.name}</span>
              <br />
              <span className="info-label">User Story:</span>{" "}
              <span className="info-value">{Data?.usName || "-"}</span>
              <br />
              <span className="info-label">User Interface:</span>{" "}
              <span className="info-value">{Data?.uiName || "-"}</span> */}
          </div>
          {/* <Grid container sx={{ display: "inline-flex" }}>
              <Grid item xs={8}>
                <DialogTitle
                  className="fs-3"
                  style={{
                    textAlign: "center",
                    marginLeft: "45%",
                    color: "orange",
                    fontWeight: "bold",
                  }}
                >
                  Task
                </DialogTitle>
              </Grid>
            </Grid> */}
          <DialogContent className="row popup">
            <div className="row">
              <TextField
                required
                defaultValue={Data?.name}
                className="col m-2 read-only-input"
                label="TasK Name"
                type="text"
                variant="outlined"
                inputProps={{ maxLength: 250, readOnly: true }}
              />
              <TextField
                defaultValue={Data?.status}
                className="col m-2 read-only-input"
                label="Status"
                type="text"
                variant="outlined"
                fullWidth
                InputProps={{
                  readOnly: true,
                }}
              />
            </div>
            <div className="row">
              <InputLabel id="description" className="mx-2">
                Description
              </InputLabel>
              <TextareaAutosize
                required
                disabled
                className="col m-2 form-control"
                defaultValue={Data?.description}
                style={{
                  height: 80,
                }}
              />
            </div>
            <div className="row">
              <TextField
                required
                defaultValue={Data?.usName}
                className="col m-2 read-only-input"
                label="User Stroy Name"
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              <TextField
                className="col m-2 read-only-input"
                defaultValue={Data?.uiName || "-"}
                label="User Interface Name"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
            </div>
            <div className="row">
              <TextField
                required
                defaultValue={Data?.teamName}
                className="col m-2 read-only-input"
                label="Team Name"
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              <TextField
                className="col m-2 read-only-input"
                defaultValue={Data?.employeeName || "-"}
                label="Employee Name"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
            </div>
            <div className="row">
              <TextField
                className="col m-2 read-only-input"
                defaultValue={Data?.category || "-"}
                label="Category"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              <TextField
                className="col m-2 read-only-input"
                defaultValue={Data?.subCategory || "-"}
                label="Sub Cateory"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
            </div>
            <div className="row">
              <TextField
                className="col m-2 read-only-input"
                defaultValue={Data?.taskType || "-"}
                label="Task Type"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              <TextField
                className="col m-2 read-only-input"
                defaultValue={Data?.classification || "-"}
                label="Classification"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
            </div>
            <div className="row">
              <div className="col">
                <InputLabel id="start-date">Est Start date</InputLabel>
                <TextField
                  required
                  id="start-date"
                  defaultValue={ConvertToISO(Data?.estimateStartDate)}
                  margin="dense"
                  className="read-only-input"
                  type="date"
                  fullWidth
                  InputProps={{
                    readOnly: true,
                  }}
                  variant="outlined"
                />
              </div>
              <div className="col">
                <InputLabel id="end-date">Est End date</InputLabel>
                <TextField
                  required
                  id="end-date"
                  defaultValue={ConvertToISO(Data?.estimateEndDate)}
                  margin="dense"
                  label=""
                  type="date"
                  className="read-only-input"
                  InputProps={{
                    readOnly: true,
                  }}
                  fullWidth
                  variant="outlined"
                />
              </div>
            </div>
            <div className="row">
              <div className="col">
                <InputLabel id="start-date">Actual Start date</InputLabel>
                <TextField
                  required
                  id="start-date"               
                  defaultValue={Data.actualStartDate ? ConvertToISO(Data.actualStartDate) : '-'}
                  className="read-only-input"
                  margin="dense"
                  label=""
                  type="text"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </div>
              <div className="col">
                <InputLabel id="end-date">Actual End date</InputLabel>
                <TextField
                  id="end-date"
                  defaultValue={Data.actualEndDate ? ConvertToISO(Data.actualEndDate) : '-'}
                  className="read-only-input"
                  margin="dense"
                  label=""
                  type="text"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </div>
            </div>

            <Grid container>
              <Grid item xs={5.5} sx={{ ml: 0 }}>
                <TextField
                  id="end-date"
                  defaultValue={ConvertToISO(Data?.weekEndingDate)}
                  label="Week Ending date"
                  fullWidth
                  className="col mt-3 read-only-input"
                  variant="outlined"
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions className="mx-3 px-4 mb-2">
            <Button onClick={handleClose} variant="contained" color="error">
              Cancel
            </Button>
          </DialogActions>
        </form>
        {/* </div> */}
      </Dialog>
    </div>
  );
};
