import {
  <PERSON><PERSON>crum<PERSON>,
  Grid,
  <PERSON>po<PERSON>,
  Box,
  Button,
  Tooltip,
} from "@mui/material";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  IWeeklyPlan,
  NWeeklyPlan,
} from "../../../Models/WeeklyPlan/WeeklyPlan";
import { useEffect, useRef, useState } from "react";
import { Get, Post } from "../../../Services/Axios";
import DataTable from "react-data-table-component";
import AddIcon from "@mui/icons-material/Add";
import { ConvertDate, ConvertToISO } from "../../../Utilities/Utils";
import { AddWeeklyPlan } from "./AddWeeklyPlan";
import Select from "react-select";
import { CommonMaster } from "../../../Models/Common/CommonMaster";
import {
  PRIORITYTYPE,
  WEEKLYPLAN,
} from "../../../Constants/Common/CommonMaster";
import VisibilityIcon from "@mui/icons-material/Visibility";
import SearchIcon from "@mui/icons-material/Search";
import EditIcon from "@mui/icons-material/Edit";
import RefreshIcon from "@mui/icons-material/Refresh";
import { ModalAction } from "../../../Models/Common/AlertOptions";
import { EditWeeklyPlan } from "./EditWeeklyPlan";
import { ViewWeeklyPlan } from "./ViewWeeklyPlan";
import BackDrop from "../../../CommonComponents/BackDrop";
// import { Regex } from "../../../Constants/Regex/Regex";
// import { Employee } from "../../../Models/Employee/Employee";
import { Project } from "../../../Models/Project/Project";
import { ToolTip } from "../../../CommonComponents/ToolTip";
import { useContextProvider } from "../../../CommonComponents/Context";
import { Roles } from "../../../Constants/Common/Roles";
import { WeeklyPlanFilter} from "../../../Constants/WeeklyPlan/Weeklyplanlist";

export const WeeklyPlan = () => {
  const TeamnameRef = useRef<any>(null);
  const [weeklyPlans, setWeeklyPlans] = useState<IWeeklyPlan[]>([]);
  const [filterData, setFilterData] = useState<any>([]);
  const [weeklyPlan, setWeeklyPlan] = useState<IWeeklyPlan | null>(null);
  const [open, setOpen] = useState<ModalAction | null>(null);
  const [reload, setReload] = useState<boolean>(false);
  const [filter, setFilter] = useState<NWeeklyPlan>(WeeklyPlanFilter);
  const [commonMaster, setCommonMaster] = useState<CommonMaster[]>([]);
  const projectNameRef = useRef<any>();
  const categoryRef = useRef<any>();
  const priorityRef = useRef<any>();
  const [loading, setLoading] = useState<boolean>(true);
  const dueDateRef = useRef<HTMLInputElement>(null);
  const weekendingRef = useRef<HTMLInputElement>(null);
  const [teams, setTeams] = useState<any>([]);
  const statusRef = useRef<any>(null);
  const { state } = useLocation();
  const [routeState, setRouteState] = useState<any>(state);
  const [projects, setProjects] = useState<Array<Project>>([]);
  const [Page, setPage] = useState(0);
  const { role } = useContextProvider();
  const navigate = useNavigate();

  async function getWeeklyPlan() {
    debugger
    const response: any = await Post(`app/WeeklyPlan/GetWeeklyPlans?month=10&year=2023`,filter);
    const projects: any = await Get("/app/Project/GetProjectList");
    setProjects(projects.data);
    setLoading(false);
    console.log(response?.data);
    
    let temps = response?.data;
    var data = [];
    if (routeState?.teamId) {
      data = response?.data.filter(
        (x: any) =>
          x.teamId === state.teamId &&
          ConvertToISO(x.weekEndingDate) === state.weekEndingDate
      );
    } else if (routeState?.employeeId) {
      data = response?.data.filter(
        (x: any) =>
          x.empId === state.employeeId &&
          ConvertToISO(x.weekEndingDate) === state.weekEndingDate
      );
    } else {
      data = response?.data;
    }

    setWeeklyPlans(response?.data);
    setFilterData(data);
    const teamList: any = await Get("app/Team/GetTeamList");
    const responseMaster: any = await Get("app/CommonMaster/GetCodeTableList");
    setCommonMaster(responseMaster.data ?? []);
    setTeams(teamList?.data);

    if (state?.WeekEndingDateCurr) {
      temps = temps.filter(
        (x: any) =>
          x.weekEndingDate.toString().slice(0, 10) === state?.WeekEndingDateCurr
      );
      setFilterData(temps);
    }

    if (state?.status) {
      temps = temps.filter((x: any) => x.status === state?.status);
      setFilterData(temps);
    }

    if (state?.teamName) {
      temps = temps.filter((x: any) => x.team === state?.teamName);
      setFilterData(temps);
    }
  }

  useEffect(() => {
    getWeeklyPlan();
  }, [reload,loading, location]);

  debugger

  console.log(filterData);

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      type: "Date",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: IWeeklyPlan) => {
        return (
          <>
            <Tooltip
              title="View"
              className="mx-1"
              onClick={() => {
                setOpen({ view: true });
                setWeeklyPlan(row);
              }}
            >
              <VisibilityIcon className="fs-4 text-info" />
            </Tooltip>
            {role !== Roles.CUSTOMER && (
              <Tooltip
                title="Edit"
                className="mx-1"
                onClick={() => {
                  setOpen({ edit: true });
                  setWeeklyPlan(row);
                }}
              >
                <EditIcon className="fs-4 text-warning" />
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      field: "project",
      name: "Project",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <ToolTip title={`${row.project}`}>
          <p className="tableStyle">{row.project}</p>
        </ToolTip>
      ),
    },
    {
      field: "notes",
      name: "Weekly Deliverable",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <ToolTip title={`${row.notes}`}>
          <p className="tableStyle">{row.notes}</p>
        </ToolTip>
      ),
    },
    {
      field: "employee",
      name: "Employees",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: any) => (
        <ToolTip title={`${row.employees}`}>
        <p className="tableStyle">{row.employees}</p>
      </ToolTip>
      ),
    },
    {
      field: "estimatedHours",
      name: "Estimated Hours",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.estimatedHours}</p>
      ),
    },
    {
      field: "category",
      name: "Category",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <ToolTip title={`${row.category}`}>
          <p className="tableStyle">{row.category}</p>
        </ToolTip>
      ),
    },
    {
      field: "priority",
      name: "Priority",
      width: "10rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.priority}</p>
      ),
    },
    {
      field: "team",
      name: "Team",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <Link
          to={`/${role}/WeeklyPlanDashboard`}
          state={{ data: { id: row.teamId, name: row.team } }}
        >
          <ToolTip title={`${row.team}`}>
            <p className="tableStyle">{row.team}</p>
          </ToolTip>
        </Link>
      ),
    },
    {
      field: "description",
      name: "Description",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: any) => (
        <Tooltip title={row.description}>
          <p className="tableStyle">
            {row.description.slice(0, 23)}
            {row.description.length > 23 && "..."}
          </p>
        </Tooltip>
      ),
    },
    {
      field: "dueDate",
      name: "Due Date",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{ConvertDate(`${row.dueDate}`)}</p>
      ),
    },
    {
      field: "status",
      name: "Status",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{row.status}</p>
      ),
    },
    {
      field: "weekEndingDate",
      name: "Week Ending Date",
      width: "13rem",
      editable: false,
      headerAlign: "left",
      align: "left",
      selector: (row: IWeeklyPlan) => (
        <p className="tableStyle">{ConvertDate(`${row.weekEndingDate}`)}</p>
      ),
    },
  ];

  function applyFilter() {
    let temp: Array<IWeeklyPlan> = weeklyPlans;

    if (filter.weekEndingDate) {
      temp = temp.filter(
        (x:any) =>
          x.weekEndingDate.toString().slice(0, 10) === filter.weekEndingDate
      );
      setFilterData(temp);
    }

    if (filter.dueDate) {
      temp = temp.filter(
        (x:any) => x.dueDate.toString().slice(0, 10) === filter.dueDate
      );
      setFilterData(temp);
    }

    if (filter.project) {
      temp = temp.filter((x) => x.project === filter.project);
      setFilterData(temp);
    }

    if (filter.category) {
      temp = temp.filter((x) => x.category === filter.category);
      setFilterData(temp);
    }

    if (filter.priority) {
      temp = temp.filter((x) => x.priority === filter.priority);
      setFilterData(temp);
    }

    if (filter.status) {
      temp = temp.filter((x) => x.status === filter.status);
      setFilterData(temp);
    }

    if (filter.team) {
      temp = temp.filter((x) => x.team === filter.team);
      setFilterData(temp);
    }

    if (filter.empId) {
      temp = temp.filter((x) => x.empId === filter.empId);
      setFilterData(temp);
    }
  }
  const handleResetState = () => {
    navigate(".", { state: {} });
  };

  const handleChangePage = (page: number) => {
    debugger
    setFilter((prevState) => {
      return {
        ...prevState,
        direction: Page > page ? "PREVIOUS" : "NEXT",
      };
    });
    setFilter((prevState) => {
      return {
        ...prevState,
        pageNumber: page,
      };
    });
    setPage(page);
    setLoading((prev) => !prev);
  };

  const handleChangeRowsPerPage = (currentRowsPerPage: number, _: number) => {
    debugger
    setFilter((prevState) => {
      return {
        ...prevState,
        pageSize: currentRowsPerPage,
      };
    });
    setFilter((prevState) => {
      return {
        ...prevState,
        pageNumber: 0,
      };
    });
    setLoading((prev) => !prev);
  };

  function reset() {
    categoryRef.current.clearValue();
    priorityRef.current.clearValue();
    statusRef.current.clearValue();
    TeamnameRef.current.clearValue();
    projectNameRef.current.clearValue();
    if (dueDateRef.current) dueDateRef.current.value = "";
    if (weekendingRef.current) weekendingRef.current.value = "";
    setFilterData(weeklyPlans);
    setFilter(WeeklyPlanFilter);
    setRouteState(null);
    setReload((prev) => !prev);
    handleResetState();
  }

  return (
    <div>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Weekly Plan</Typography>
      </Breadcrumbs>
      <div className="well mx-auto mt-4">
        <div className="row">
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Project Name</label>
              <Select
                id="project-name"
                ref={projectNameRef}
                className="col mt-1 custom-select"
                onChange={(selectedOption) => {
                  setFilter((prevState: NWeeklyPlan) => {
                    return {
                      ...prevState,
                      project: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                options={projects? projects
                  ?.filter((x: any) => x?.isActive === true)
                  ?.map((e: Project) => ({
                    label: e.name,
                    value: e.name,
                  })) : []}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Category</label>
              <Select
                id="category"
                ref={categoryRef}
                className="col mt-1 custom-select"
                onChange={(selectedOption: any) => {
                  setFilter((prevState: NWeeklyPlan) => {
                    return {
                      ...prevState,
                      category: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                options={commonMaster
                  .filter(
                    (commonMaster) => commonMaster.codeType === WEEKLYPLAN
                  )
                  .sort((a, b) => a.codeValue.localeCompare(b.codeValue))
                  .map((commonMaster: CommonMaster) => ({
                    label: commonMaster.codeValue,
                    value: commonMaster.codeValue,
                  }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Priority</label>
              <Select
                id="priority"
                ref={priorityRef}
                className="col mt-1 custom-select"
                onChange={(selectedOption) => {
                  setFilter((prevState: NWeeklyPlan) => {
                    return {
                      ...prevState,
                      priority: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                options={commonMaster
                  .filter((x) => x.codeType === PRIORITYTYPE)
                  .map((commonMaster: CommonMaster) => ({
                    label: commonMaster.codeValue,
                    value: commonMaster.codeValue,
                  }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                id="priority"
                ref={statusRef}
                defaultValue={
                  state?.status
                    ? { value: state.status, label: state.status }
                    : null
                }
                className="col mt-1 custom-select"
                onChange={(selectedOption) => {
                  setFilter((prevState: NWeeklyPlan) => {
                    return {
                      ...prevState,
                      status: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                options={[
                  "Pending",
                  "Not Started",
                  "In Progress",
                  "Completed",
                  "Moved",
                ].map((status: any) => ({
                  label: status,
                  value: status,
                }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Due Date</label>
              <input
                onChange={(e) => {
                  setFilter((prevState: any) => {
                    return {
                      ...prevState,
                      dueDate:
                        e.target.value.length === 0 ? null : e.target.value,
                    };
                  });
                }}
                ref={dueDateRef}
                type="date"
                id="dueDate"
                placeholder="Due Date"
                className="m-1  form-control"
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Week Ending Date</label>
              <input
                defaultValue={state?.weekEndingDate}
                onChange={(e) => {
                  setFilter((prevState: any) => {
                    return {
                      ...prevState,
                      weekEndingDate:
                        e.target.value.length === 0 ? null : e.target.value,
                    };
                  });
                }}
                ref={weekendingRef}
                type="date"
                id="dueDate"
                placeholder="Due Date"
                className="m-1 form-control"
              />
            </div>
          </div>

          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Team Name</label>
              <Select
                id="name"
                aria-label="Floating label select example"
                ref={TeamnameRef}
                defaultValue={
                  state?.team && {
                    label: state.team,
                    value: state.team,
                  }
                }
                placeholder="Team Name"
                className="mt-1"
                onChange={(selectedOption) => {
                  setFilter((prevState: NWeeklyPlan) => {
                    return {
                      ...prevState,
                      team: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                options={teams
                  .filter((x: any) => x.isActive === true)
                  .map((name: any) => {
                    return {
                      value: name.name,
                      label: name.name,
                    };
                  })}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
                isSearchable={true}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-10">
            <div className="row justify-content-end">
              <div className="col-auto">
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-3 mt-4"
                  onClick={() => applyFilter()}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-3 mt-4"
                  onClick={() => reset()}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="row d-flex" style={{ width: "95%", marginLeft: "21px" }}>
        <div className="col-5 ">
          {role !== Roles.CUSTOMER && (
            <Grid>
              <Button
                variant="contained"
                className="mb-2 float-md-start"
                size="small"
                onClick={() => setOpen({ add: true })}
                sx={{ ml: "3%", fontSize: { xs: "11px", md: "15px" } }}
              >
                Add Weekly Plan
                <AddIcon className="mx-1" />
              </Button>
            </Grid>
          )}
        </div>
        <div className="col-7">
          {role === "Admin" && (
            <Grid className="mb-2 float-md-start fs-5 bg-grey-500">
              <Typography sx={{ fontSize: { xs: "15px", md: "18px" } }}>
                Total Estimated Hours :{" "}
                <b>
                  {filterData?.reduce(
                    (acc: number, item: any) => item.estimatedHours + acc,
                    0
                  )}
                  hrs
                </b>
              </Typography>
            </Grid>
          )}
          {/* <div className="col-11 col-s-4"> */}
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center">
        <div
          className="responsive-div"
          style={{ marginTop: "4%", width: "94vw" }}
        >
          <Grid item xs={12} sm={11}>
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                fixedHeader={true}
                responsive
                persistTableHead
                data={filterData || []}
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },

                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
                paginationComponentOptions={{
                  rowsPerPageText: "Rows per page:",
                  rangeSeparatorText: "of",
                  noRowsPerPage: false,
                  selectAllRowsItem: false,
                  selectAllRowsItemText: "All",
                }}
                onChangePage={handleChangePage}
                onChangeRowsPerPage={handleChangeRowsPerPage}
                paginationTotalRows={role !== Roles.CUSTOMER ? filterData[0]?.totalCount : filterData?.length }
                paginationServer
              />
            </Box>
          </Grid>
        </div>
      </div>
      {open?.edit && (
        <EditWeeklyPlan
          open={open}
          setOpen={setOpen}
          setReload={setReload}
          weeklyPlan={weeklyPlan}
        />
      )}
      {open?.view && (
        <ViewWeeklyPlan open={open} setOpen={setOpen} weeklyPlan={weeklyPlan} />
      )}
      {open?.add && (
        <AddWeeklyPlan open={open} setOpen={setOpen} setReload={setReload} />
      )}
      <BackDrop open={loading} />
    </div>
  );
};
