import {
  Dialog,
  DialogContent,
  DialogTitle,
  TextField,
  InputLabel,
  FormControl,
  Tooltip,
  DialogActions,
  Button,
  Grid,
} from "@mui/material";
import "../../../StyleSheets/EditTask.css";
import { ConvertDate, ConvertToISO } from "../../../Utilities/Utils";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import TextareaAutosize from "@mui/material/TextareaAutosize";

export const ViewTask = ({ openDialog, setOpenDialog, Data }: any) => {
  const handleClose = () => {
    setOpenDialog({ view: false });
  };

  return (
    <div>
      <Dialog open={openDialog?.view}>
        {/* <div
          style={{
            maxHeight: "80vh",
            overflowY: "auto",
            overflowX: "hidden",
            position: "relative",
          }}
        > */}
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>View Task</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
            {/* <span className="info-label">Project Name:</span>{" "}
              <span className="info-value">{Data?.projectName}</span>
              <br />
              <span className="info-label">Task Name:</span>{" "}
              <span className="info-value">{Data?.name}</span>
              <br />
              <span className="info-label">User Story:</span>{" "}
              <span className="info-value">{Data?.usName || "-"}</span>
              <br />
              <span className="info-label">User Interface:</span>{" "}
              <span className="info-value">{Data?.uiName || "-"}</span> */}
          </div>
          {/* <Grid container sx={{ display: "inline-flex" }}>
              <Grid item xs={8}>
                <DialogTitle
                  className="fs-3"
                  style={{
                    textAlign: "center",
                    marginLeft: "45%",
                    color: "orange",
                    fontWeight: "bold",
                  }}
                >
                  Task
                </DialogTitle>
              </Grid>
            </Grid> */}
          <DialogContent className="row popup d-flex justify-content-center">
          <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <Tooltip title={Data?.name} arrow>
                <TextField
                  required
                  defaultValue={Data?.name}
                  className="read-only-input"
                  label="Task Name"
                  type="text"
                  variant="outlined"
                  inputProps={{ maxLength: 250, readOnly: true }}
                />
              </Tooltip>
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                required
                defaultValue={Data?.status}
                className="read-only-input"
                label="Status"
                type="text"
                variant="outlined"
                fullWidth
                InputProps={{
                  readOnly: true,
                }}
              />
           </FormControl>
           </Grid>
              <Grid item xs={12} md={12}>
              <InputLabel id="description">
                Description
              </InputLabel>
              <FormControl fullWidth>
              <TextareaAutosize
                disabled={true}
                id="description"
                aria-disabled
                // className="mb-3 form-control"
                aria-label="empty textarea"
                style={{
                  height: 90,
                }}
                defaultValue={Data?.description}
              />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                required
                defaultValue={Data?.usName}
                className="read-only-input"
                label="User Stroy Name"
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
               </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                className="read-only-input"
                defaultValue={Data?.uiName || "-"}
                label="User Interface Name"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
           </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                required
                defaultValue={Data?.teamName}
                className="read-only-input"
                label="Team Name"
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
               </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                className="read-only-input"
                defaultValue={Data?.employeeName || "-"}
                label="Employee Name"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
            </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                className="read-only-input"
                defaultValue={Data?.category || "-"}
                label="Category"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
               </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                className="read-only-input"
                defaultValue={Data?.subCategory || "-"}
                label="Sub Cateory"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
            </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                className="read-only-input"
                defaultValue={Data?.taskType || "-"}
                label="Task Type"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
               </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                className="read-only-input"
                defaultValue={Data?.classification || "-"}
                label="Classification"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
            </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Est Start date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  required
                  id="start-date"
                  defaultValue={ConvertToISO(Data?.estimateStartDate)}
                  margin="dense"
                  className="read-only-input"
                  type="date"
                  fullWidth
                  InputProps={{
                    readOnly: true,
                  }}
                  variant="outlined"
                />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">Est End date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  required
                  id="end-date"
                  defaultValue={ConvertToISO(Data?.estimateEndDate)}
                  margin="dense"
                  label=""
                  type="date"
                  className="read-only-input"
                  InputProps={{
                    readOnly: true,
                  }}
                  fullWidth
                  variant="outlined"
                />
               </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Actual Start date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  required
                  id="start-date"             
                  defaultValue={Data?.actualStartDate ? ConvertToISO(Data?.actualStartDate) : '-'}
                  className="read-only-input"
                  margin="dense"
                  label=""
                  type="text"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">Actual End date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  required
                  id="end-date"               
                  defaultValue={Data?.actualEndDate ? ConvertToISO(Data?.actualEndDate) : '-'}
                  className="read-only-input"
                  margin="dense"
                  label=""
                  type="text"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                required
                id="end-date"
                defaultValue={ConvertDate(Data?.weekEndingDate)}
                label="Week Ending date"
                fullWidth
                className="read-only-input"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
               </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField                  
                  value={Data?.comment || ''} // Use value instead of defaultValue
                  // margin="dense"
                  label="Comment"
                  type="text"
                  fullWidth
                  variant="outlined"
                  className="read-only-input"
                  disabled={Data?.comment? false : true}
                />
             </FormControl>
              </Grid>
              </Grid>
          </DialogContent>
          <DialogActions className="px-4">
            <Button onClick={handleClose} variant="contained" color="error">
              Cancel
            </Button>
          </DialogActions>
        </form>
        {/* </div> */}
      </Dialog>
    </div>
  );
};
