import { Divider } from "@mui/material";
import Multiselect from "multiselect-react-dropdown";
import { SetStateAction, Dispatch, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { Project } from "../../../Models/Project/Project";
import { AssignmentMapper } from "../../../Models/Common/AssignmentMapper";
import { ProjectGroupMapper } from "../../../Models/Project/ProjectGroup";
import Swal from "sweetalert2";
import { Post } from "../../../Services/Axios";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { Roles } from "../../../Constants/Common/Roles";

type AssignProjectProps = {
  show: boolean;
  setShow: Dispatch<SetStateAction<boolean>>;
  left: Project[];
  right: Project[];
  projectGroupId: number | undefined;
  setReload: Dispatch<SetStateAction<boolean>>;
};

export const AssignProject = ({
  show,
  setShow,
  left,
  right,
  projectGroupId,
  setReload,
}: AssignProjectProps) => {
  const [assignmentMapper, setAssignmentMapper] = useState<AssignmentMapper>({
    assignedList: [],
    unassignedList: [],
  });
  var assignedList: ProjectGroupMapper[] = [];
  var unassignedList: ProjectGroupMapper[] = [];

  function toGroupMapper(project: Project): ProjectGroupMapper {
    return {
      projectId: project.id,
      projectGroupId: projectGroupId ?? 0,
      CreatedBy: Roles.ADMIN,
      UpdatedBy: Roles.ADMIN,
    };
  }

  function handleSelect(projects: Project[], project: Project) {
    unassignedList = assignmentMapper.unassignedList.filter(
      (x) => x.projectId !== project.id
    );
    projects.map((project: Project) => {
      assignedList.push(toGroupMapper(project));
    });
    setAssignmentMapper({ assignedList, unassignedList });
  }

  function handleRemove(projects: Project[], project: Project) {
    unassignedList = [...assignmentMapper.unassignedList];
    var deleteItem = unassignedList.find((x) => x.projectId === project.id);
    if (!deleteItem) unassignedList.push(toGroupMapper(project));

    projects.map((project: Project) => {
      assignedList.push(toGroupMapper(project));
    });

    setAssignmentMapper({ assignedList, unassignedList });
  }

  async function save() {
    const { error }: any = await Post(
      "app/Project/AssignProjectToProjectGroup",
      assignmentMapper
    );

    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Assigning!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Project Assigned Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      showConfirmButton: true,
    });
    handleCloseModal();
  }

  const handleCloseModal = () => {
    setAssignmentMapper({ assignedList: [], unassignedList: [] });
    setShow(false);
    setReload((prev) => !prev);
  };

  return (
    <Modal show={show} onHide={() => setShow(false)} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit</Modal.Title>
      </Modal.Header>
      <Divider />
      <Modal.Body>
        <Multiselect
          options={left}
          selectedValues={right}
          onSelect={(selectedList, selectedItem) =>
            handleSelect(selectedList, selectedItem)
          }
          onRemove={(selectedList, selectedItem) => {
            handleRemove(selectedList, selectedItem);
          }}
          displayValue="name"
          className="p-2"
          style={{
            chips: {
              background: "#6777ef",
            },
            searchBox: {
              border: "1px solid #e4e7ea",
              "padding-left": "10px",
            },
          }}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={() => setShow(false)}>
          Close
        </Button>
        <Button variant="primary" onClick={() => save()}>
          Save
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
