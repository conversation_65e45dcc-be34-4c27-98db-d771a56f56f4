import {
  Alert,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextareaAutosize,
  Autocomplete,
  Grid,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import { useQuery } from "react-query";
import { Get, Post, PostFiles } from "../../../../Services/Axios";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AddProjectObjective } from "../ProjectObjective/AddProjectObjective";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import { FileUpload } from "../../../../CommonComponents/FileUpload";
import { Document } from "../../../../Models/Project/UserStory";
import AddIcon from "@mui/icons-material/Add";
import { Regex } from "../../../../Constants/Regex/Regex";

const formField = [
  "Name",
  "Description",
  "StartDate",
  "EndDate",
  "Status",
  "Percentage",
  "Complexity",
  "CreatedBy",
  "UpdatedBy",
  "ProjectId",
  "UICategory",
];

export const AddUserInterface = ({
  openDialog,
  setOpenDialog,
  projectId,
  SetReload,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [CategoryUI, setCategoryUI] = useState([]);
  const [save, setSave] = useState<boolean>(false);
  const [open, setOpen] = useState<any>({ add: false });
  const [uploadedFiles, setUploadedFiles] = useState<any>([]);
  const [IDs, setIds] = useState<number[]>([]);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  const [projectobjective, setprojectobjective] = useState<any>([]);

  async function fetchCommonMaster() {
    const commonMaster: any = await Get("app/CommonMaster/GetCodeTableList");
    return commonMaster.data;
  }

  const { data } = useQuery("master_UI", fetchCommonMaster);

  const onSubmitHandler = async (data: any) => {
    debugger
    setSave(true);
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }
    const defaultStatus = "Pending";
    data.Status = defaultStatus;
    data.ProjectObjectiveId = IDs.join("|");
    const response: any = await Post("app/Project/AddUserInterface", data);
    if (!response.error && uploadedFiles.length > 0) {
      uploadedFiles.forEach(async (file: any) => {
        var document: Document = {
          TableName: "UserInterface",
          AttributeId: response.data.id,
          ProjectId: data.ProjectId,
          DocType: data.DocType,
          FileName: file.name,
          FileType: file.type,
          File: file,
          IsActive: true,
          CreatedBy: "user",
          UpdatedBy: "user",
        };
        await PostFiles("app/Project/UploadFiles", document);
      });
    }

    var option: AlertOption;
    if (response?.error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "User Interface Added Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      SetReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  useEffect(() => {
    SetReload((prev: boolean) => !prev);
  }, [open]);

  useEffect(() => {
    async function getobjectives() {
      let objective: any = await Get(
        `app/Project/GetProjectObjective?ProjectId=${projectId}`
      );
      setprojectobjective(objective?.data?.projectObjectives || []);
    }
    getobjectives();
  }, [projectId, open]);

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setOpenDialog({ add: false });
    setCategoryUI([]);
    setSave(false);
    setUploadedFiles([]);
  };

  return (
    <div>
      <Dialog open={openDialog?.add}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue" }}>
              Add User Interface
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                {...register("Name")}
                label="User Interface Name"
                type="text"
                variant="outlined"
              />
               </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <TextField
                  id="outlined-read-only-input"
                  label="Status"
                  disabled
                  defaultValue={"Pending"}
                  InputProps={{
                    readOnly: true,
                  }}
                  {...register("Status")}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
            <FormControl fullWidth>
              <TextareaAutosize
                required
                placeholder="Description"
                {...register("Description")}
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                }}
                style={{ height: 100 }}
              />
            </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <InputLabel id="start-date">Start date</InputLabel>
            <FormControl fullWidth>
                <TextField
                  required
                  id="start-date"
                  margin="dense"
                  {...register("StartDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <InputLabel id="end-date">End date</InputLabel>
            <FormControl fullWidth>
                <TextField
                  required
                  id="end-date"
                  margin="dense"
                  {...register("EndDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <InputLabel id="UI-Category">UI Category</InputLabel>
                <Select
                  labelId="UI-Category"
                  id="uiCategory"
                  required
                  value={CategoryUI}
                  label="UI Category"
                  {...register("UICategory", {
                    onChange: (e: any) => {
                      setCategoryUI(e.target?.value);
                    },
                  })}
                >
                  {data?.map((e: any) => {
                    if (e.codeType == "UICategory")
                      return (
                        <MenuItem value={e.codeName} key={e.codeValue}>
                          {e.codeName}
                        </MenuItem>
                      );
                  })}
                </Select>
                </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <InputLabel id="Complexity">Complexity</InputLabel>
                <Select
                  labelId="Complexity"
                  required
                  id="Complexity"
                  label="Complexity"
                  {...register("Complexity")}
                >
                  <MenuItem value="Low">Low</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="High">High</MenuItem>
                </Select>
                </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <FileUpload
                  uploadedFiles={uploadedFiles}
                  setUploadedFiles={setUploadedFiles}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <InputLabel id="Status">Document Type</InputLabel>
                <Select
                  labelId="Document Type"
                  id="DocumentType"
                  label="Document Type"
                  required={uploadedFiles.length > 0}
                  {...register("DocType")}
                >
                  <MenuItem value="Input">Input</MenuItem>
                  <MenuItem value="Process">Process</MenuItem>
                  <MenuItem value="Output">Output</MenuItem>
                  <MenuItem value="Sample Code">Sample Code</MenuItem>
                </Select>
                </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <Autocomplete
                  multiple
                  options={projectobjective.map((objective: any) => ({
                    label: objective.description,
                    id: objective.id,
                  }))}
                  {...register("ProjectObjectiveIds")}
                  onChange={(e: any, value: any) => {
                    if (value || e){
                      const ids = value.map((obj: any) => obj.id);
                      setIds(ids);
                    }
                  }}
                  renderInput={(params: any) => (
                    <TextField
                      {...params}
                      label="Project Objective"
                      variant="outlined"
                    />
                  )}
                />
               </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <Button
                variant="contained"
                onClick={() => setOpen({ add: true })}
              >
                Add Project Objective
                <AddIcon className="mx-1" />
              </Button>
              </FormControl>
            </Grid>
            </Grid>
            <div className="row">
              <input {...register("CreatedBy")} value="user" hidden />
              <input {...register("UpdatedBy")} value="user" hidden />
              <input {...register("Percentage")} value="0" hidden />
              <input {...register("ProjectId")} value={projectId} hidden />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              disabled={save}
              color="success"
              type="submit"
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <AddProjectObjective
        openDialog={open}
        setOpenDialog={setOpen}
        ProjectId={projectId}
        SetReload={SetReload}
      />
    </div>
  );
};
