import { IBase, Nullable } from "../Common/BaseModel";
import { Employee } from "../Employee/Employee";


export interface IWeeklyPlan extends IBase {
    id?: number;
    employeeName?: string;
    projectName?: string;
    weekEndingDate?: Date | null;
    dueDate?: Date | null;
    status?: string | null;
    empId?: string | null;
    priority?: string | null;
    estimatedHours?: number | null;
    teamId?: number | null;
    projectId?: number | null;
    employeeId?: string | null;
    category?: string | null;
    description?: string | null;
    notes?: string | null;
    label? :  string | null;
    project?: string | null;
    team?: string | null;
    direction?: string | null;
    pageNumber?: number | null;
    pageSize?: number | null;
    employees?: Employee[] | null;
    employee?: Employee[] | null;
    createdDate?: Date | null;
    startDate?: Date | null;
    endDate?: Date | null;
}

export type NWeeklyPlan = Nullable<IWeeklyPlan>