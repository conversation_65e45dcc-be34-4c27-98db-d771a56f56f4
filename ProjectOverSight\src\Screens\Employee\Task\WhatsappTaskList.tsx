import { useEffect, useState } from "react";
import { Get } from "../../../Services/Axios";
import '../Task/whatsapp.css';
import {
  ConvertDate,
  ConvertToISO,
  WeekEndingDate,
} from "../../../Utilities/Utils";
import {
  B<PERSON>crum<PERSON>,
  Button,
  List,
  ListItem,
  ListSubheader,
  Typography,
} from "@mui/material";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import ErrorIcon from "@mui/icons-material/Error";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { Link } from "react-router-dom";
import { SelectCategory } from "./CompleteTaskPopup";

type Task = {
  Name: string;
  Description: string;
  EstTime: number;
  Percentage: number;
  ActTime: number;
  UserStory : string;
  UserInterface : string;
};

export const WhatsappTaskList = () => {
  const [dailyTask, setDailyTask] = useState<any>();
  const [toggleState, setToggleState] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [refetch, setRefetch] = useState<boolean>(false);
  const [date, setDate] = useState<string>(
    ConvertDate(new Date().toDateString())
  );
  const [taskListView, setTaskListView] = useState<any>({
    view: false,
    edit: false,
    add: false,
    assign: false,
    daily: false,
  });


  const [header, setHeader] = useState<string>("Today's Daily Task");
  const json: any = sessionStorage.getItem("user") || null;
  const sessionUser = JSON.parse(json);
  const weekEndDate: Date = WeekEndingDate();
  const map = new Map<string, Task[]>();

  const fetchdata = async (tomorrow: boolean = false) => {
    setLoading(true);
    const dateToFetch = tomorrow
      ? new Date(new Date().getTime() + 24 * 60 * 60 * 1000)
      : new Date();
    try {
      const response: any = await Get(
        `app/EmployeeTask/GetWhatsapptaskListByTaskId?employeeId=${sessionUser.employeeId
        }&WeekEndingDate=${weekEndDate.toDateString()}`
      );
      const temp = response?.data?.filter(
        (x: any) => x.workedOn.slice(0, 10) === ConvertToISO(dateToFetch)
      );

      console.log(temp);
      setDailyTask(temp || []);
      setHeader(tomorrow ? "Tomorrow's Daily Task" : "Today's Daily Task"); // Update header
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false); // Set loading to false regardless of success or failure
      // setRefetch(!refetch);
    }
  };


  const handleClickToday = () => {
    setToggleState(false);
    const today = new Date();
    const formattedDate = formatDate(today);
    setDate(formattedDate);
    fetchdata();
  };

  const handleClickTomorrow = () => {
    setToggleState(true);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const formattedDate = formatDate(tomorrow);
    setDate(formattedDate);
    fetchdata(true);
  };



  useEffect(() => {
    if (toggleState) {
      handleClickTomorrow();
    } else {
      handleClickToday();
    }
  }, [toggleState]);



  const formatDate = (date: any) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${day}-${month}-${year}`;
  };

  const isTomorrowTask = (task: any) => {
    const taskDate = new Date(task.workedOn.slice(0, 10));
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return (
      taskDate.getFullYear() === tomorrow.getFullYear() &&
      taskDate.getMonth() === tomorrow.getMonth() &&
      taskDate.getDate() === tomorrow.getDate()
    );
  };

  useEffect(() => {
    fetchdata();
  }, [refetch]);

  useEffect(() => {
    if (loading) {
      return;
    }
  }, [loading]);

  dailyTask?.forEach((task: any) => {
    debugger
    var temp: Task = {
      Name: task.name,
      Description: task.taskDescription,
      Percentage: task.percentage,
      EstTime: task.estTime,
      ActTime: task.actTime,
      UserStory: task.userStory,
      UserInterface: task.userInterface
    };
    if (map.has(task.projectName)) {
      var taskList: Task[] = map.get(task.projectName) as Task[];
      map.set(task.projectName, [...taskList, temp]);
    } else {
      map.set(task.projectName, [temp]);
    }
  });

  let encoded = "*Task List*\n";

  [...map.keys()].forEach((projectName: string) => {

      encoded += `*Project Name: ${projectName}* \n*${header} (${date})*\n`; 
      const taskList: Task[] = map.get(projectName) as Task[];
      let counter = 1;
      
      taskList?.forEach((task: Task) => {
        encoded += `*UserStory: ( ${task?.UserStory || "N/A"} )*\n`;
        encoded += `*UserInterface: ( ${task?.UserInterface || "N/A"} )*\n`; // Add user interface name (if available)
        encoded += `${counter}. ${task.Name} - ${task.Description} - ${task.EstTime}hr\nPercentage - ${task.Percentage}%\nActual Time - ${task.ActTime}hr\n`;
        counter++;
    });
  });
  
  
  var msg = encodeURIComponent(encoded);

  useEffect(() => {
    fetchdata();
  }, [refetch]);

  const handleClickOpen = (taskData: any) => {
    setTaskListView({
      add: true,
      selectedTaskData: taskData,
      percentage: taskData.percentage,
      taskName: taskData.name,
      ProjectName: taskData.projectName,
    });
  };

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link to="/Employee">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>WhatsApp Task</Typography>
      </Breadcrumbs>
      <div
        className="mt-4 border border-1 emp-over-cont w-75 mx-auto"
        style={{ borderRadius: '20px' }}
      >
        <List
          sx={{
            width: "100%",
            maxWidth: 1500,
            // backgroundColor: "#ebf5f3",
            position: "relative",
            overflow: "auto",
            maxHeight: "100%",
            borderRadius: '20px',
            ontFamily: "Product Sans",
            "& ul": { padding: 0 },
          }}
          subheader={
            <ListSubheader
              component="div"
              id="nested-list-subheader"
              className="fw-bold d-flex align-items-center justify-content-between"
              sx={{ backgroundColor: "#85edc0", padding: 1 }}
            >
              <div className="fs-6 d-flex align-items-center w-100 justify-content-between">
                <div className=" mx-3" style={{ width: "20%" }}>
                  <TaskAltIcon /> WhatsApp Task List
                </div>

                <div>
                  <a
                    href={`https://api.whatsapp.com/send?text=${msg}`}
                    target="#"
                  >
                    <button className="btn " style={{ marginRight: '20px', backgroundColor: '#fd7e14', color: 'white' }} >
                      <WhatsAppIcon className=" mx-2 mb-1" />
                      <span style={{ marginRight: '20px', fontSize: 'Medium', }} >SHARE</span>
                    </button>
                  </a>
                </div>
              </div>
            </ListSubheader>
          }
        >
          <div className="fs-6 d-flex align-items-center w-100 justify-content-evenly m-auto"
            style={{
              width: "70%",
              maxWidth: 1500,
              margin: '10px',
              position: "relative",
              overflow: "auto",
              maxHeight: 570,
              fontFamily: "Product Sans",
            }}>

            <div className="d-flex justify-content-start align-items-center mx-2" style={{ width: '100px' }}>
              {!toggleState && (
                <span style={{ color: "#355f4d" }} className="fw-bold me-2">{date}</span>
              )}
            </div>
            <div className="d-flex" style={{ alignItems: 'center', width: '40%' }}>
              <div
                className="btn m-2"
                style={{ color: "#355f4d", fontWeight: "bold", width: '300px' }}
                onClick={handleClickToday}
              >
                <div className="label-text">Today's Task</div>
              </div>
              <div>
                <label className="label">
                  <div className="toggle">
                    <input className="toggle-state"
                      type="checkbox"
                      name="check"
                      value="check"
                      checked={toggleState}
                      onChange={() => setToggleState(!toggleState)}
                    />
                    <div className="indicator"></div>
                  </div>
                </label>
              </div>
              <div className="btn m-2" style={{ color: "#355f4d", fontWeight: "bold", width: '300px' }} onClick={handleClickTomorrow}>
                <div className="label-text">Tomorrow's Task</div>
              </div>
            </div>
            <div className="d-flex justify-content-end align-items-center mx-2" style={{ width: '100px' }}>
              {toggleState && (
                <span style={{ color: "#355f4d" }} className="fw-bold ms-2">{date}</span>
              )}
            </div>
          </div>
          {[1].map((sectionId) => (
            <li key={`section-${sectionId}`}>
              <ul>
                {dailyTask?.length == 0 ? (
                  <>
                    <h4 className="text-center m-2">No Tasks</h4>
                  </>
                ) : (
                  dailyTask?.map((item: any) => (
                    <div key={`item-${sectionId}-${item.id}`}>
                      <div className="card m-2">
                        <ListItem className="d-flex flex-column flex-md-row">
                          <div className="card-body w-75">
                            <h5 className="card-title d-flex justify-content-between">
                              <div>
                                <span className="fw-bolder">Task Name: </span>
                                {item.name} ({" "}
                                {parseFloat(item.actTime).toFixed(1)} /{" "}
                                {parseFloat(item.estTime).toFixed(1)})
                              </div>
                            </h5>
                            <p className="card-text fs-6">
                              <span className="fw-bolder">UserStory:</span>
                              <span className="mx-1">
                                {item.userStory}
                              </span>
                            </p>
                            <p className="card-text fs-6">
                            <span className="fw-bolder">UserInterface:</span>
                              <span className="mx-1">
                                {item.userInterface}
                              </span>
                              </p>
                            <p className="card-text fs-6">
                              <span className="fw-bolder">Description:</span>
                              <span className="mx-1">
                                {item.taskDescription}
                              </span>
                            </p>
                            <p className="card-text d-flex justify-content-between">
                              <small className="text-muted fs-6">
                                Status:{" "}
                                <span
                                  className={
                                    item.percentage < 100
                                      ? `text-warning`
                                      : "text-success"
                                  }
                                >
                                  {item.status} ({item.percentage}%)
                                </span>
                              </small>
                            </p>
                          </div>
                          <div className="m-2">
                            <Button
                              color="info"
                              variant="contained"
                              component="div"
                              disabled={
                                isTomorrowTask(item) ||
                                (item.percentage === 100 &&
                                  !isTomorrowTask(item))
                              }
                              onClick={() => handleClickOpen(item)}
                              sx={{ float: "right" }}
                            >
                              {isTomorrowTask(item)
                                ? "Pending"
                                : "Complete Task"}{" "}
                              {isTomorrowTask(item) ? (
                                <ErrorIcon
                                  style={{ marginLeft: 10, color: "darkorange" }}
                                />
                              ) : (
                                <CheckCircleIcon style={{ marginLeft: 10 }} />
                              )}{" "}
                            </Button>
                          </div>
                        </ListItem>
                        <SelectCategory
                          openDialog={taskListView}
                          setOpenDialog={setTaskListView}
                          selectedTaskData={taskListView}
                          setRefetch={setRefetch}
                        />
                      </div>
                    </div>
                  ))
                )}
              </ul>
            </li>
          ))}
        </List>
      </div>
    </>
  );
};
