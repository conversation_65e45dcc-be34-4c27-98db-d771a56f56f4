import { Divider, Grid } from "@mui/material";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import TodayIcon from "@mui/icons-material/Today";
import BarChartIcon from "@mui/icons-material/BarChart";
import ViewKanbanIcon from "@mui/icons-material/ViewKanban";
import { Get } from "../../../Services/Axios";
import { useEffect, useState } from "react";
import React from "react";
import {
  ConvertDate,
  ConvertTime,
  ConvertToISO,
  WeekEndingDate,
  convertTo12HourFormat,
  formatTimeAgo,
} from "../../../Utilities/Utils";
import { Project } from "../../../Models/Project/Project";
import Skeleton from "react-loading-skeleton";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import { SessionUser } from "../../../Models/Employee/Employee";
import { EmployeeAttendanceVM } from "../../../Models/Employee/Attendance";
import { Link } from "react-router-dom";
const skleton = Array.from(Array(4), () => 0);

const EmployeeTask = () => {
  const [loginDetails, setLoginDetails] = useState<any>([]);
  const [attendanceDto, setAttendanceDto] = useState<EmployeeAttendanceVM>();
  const [employeeProjects, setProjects] = useState<any>([]);
  const [duetasks, setDueTasks] = useState<any>([]);
  const [todayTask, setTodayTask] = useState<any>([]);
  const weekEndDate: Date = WeekEndingDate();
  const [loading, setLoading] = useState<boolean>(false);
  const json: any = sessionStorage.getItem("user") || null;
  const sessionUser: SessionUser = JSON.parse(json);
  const dateFilter = {
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  };

  async function fetchData() {
    setLoading(true);
    let employeeProject: any = await Get("app/Project/GetEmployeeProjectlist");
    setProjects(employeeProject?.data || []);

    const todayTasks: any = await Get(
      `app/EmployeeTask/GetWhatsapptaskListByTaskId?employeeId=${
        sessionUser?.employeeId
      }&WeekEndingDate=${weekEndDate.toDateString()}`
    );
    setTodayTask(() => {
      const tasks = todayTasks?.data?.filter(
        (x: any) => x.workedOn.slice(0, 10) === ConvertToISO(new Date())
      );
      return tasks;
    });
    const { data }: any = await Get(
      `app/Employee/GetAttendanceByUserId?userId=${sessionUser.userId}&month=${dateFilter.month}&year=${dateFilter.year}`
    );
    setAttendanceDto(data);
    const employeeTime: any = await Get(
      "app/EmployeeTime/GetEmployeeTimeDetails"
    );
    setLoginDetails(employeeTime?.data || []);

    const overDueTask: any = await Get(`app/Task/GetOverDueTask`);
    setDueTasks(overDueTask?.data || []);
    setLoading(false);
  }

  useEffect(() => {
    fetchData();
  }, []);

  function getTextColorClass(percentage: number) {
    if (percentage === 100) return "text-success";
    if (percentage < 100) return "text-warning";
  }

  function SkletonLoader() {
    return (
      <div className="mx-2" style={{ height: "340px" }}>
        {skleton.map((e, index) => (
          <React.Fragment key={index}>
            <div className="fs-6 m-3">
              <h5 className="fw-bold">
                <Skeleton key={e} style={{ height: "1.6rem" }} />
              </h5>
              <h5>
                <Skeleton
                  key={e + 1}
                  style={{ height: "1.6rem", width: "8rem" }}
                />
              </h5>
            </div>
          </React.Fragment>
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="w-100 mx-5 d-flex justify-content-evenly bg-light row p-4 mb-3 rounded-3">
        <Grid item className="col-md-3 mt-3">
          <div className="card mx-1 bg-white">
            <div className="d-flex justify-content-between mx-3 mt-3">
              <ViewKanbanIcon className="fs-4" />
              <p className="fs-5">Projects</p>
            </div>
            <Divider className="border border-dark" />

            {loading ? (
              <SkletonLoader />
            ) : (
              <div className="scroll" style={{ height: "340px" }}>
                {employeeProjects.length > 0 ? (
                  employeeProjects.map((project: Project) => {
                    return (
                      <div key={project.name}>
                        <div className="fs-6 m-3">
                          <h6 className="text-dark">
                            {project.name} - ({project.type})
                          </h6>
                          <small>
                            <span className="text-secondary">Status: </span>
                            <span
                              className={`${getTextColorClass(
                                project.percentage!
                              )}`}
                            >
                              {project.status}
                            </span>
                          </small>
                        </div>
                        <Divider />
                      </div>
                    );
                  })
                ) : (
                  <h6 className="mt-3 d-flex align-items-center justify-content-center">
                    <ErrorOutlineIcon className="mx-1" />
                    Project Not Assigned
                  </h6>
                )}
              </div>
            )}
          </div>
        </Grid>
        <Grid item className="col-md-3 mt-3">
          <div className="card mx-1 bg-white">
            <div className="d-flex justify-content-between mx-3 mt-3">
              <TodayIcon className="fs-4" />
              <p className="fs-5">Today's Task</p>
            </div>
            <Divider className="border border-dark" />
            {loading ? (
              <SkletonLoader />
            ) : (
              <div className="scroll" style={{ height: "340px" }}>
                {todayTask?.length > 0 ? (
                  todayTask?.map((task: any, index: number) => {
                    return (
                      <div key={task.name}>
                        <div className="fs-6 m-4">
                          <h6 style={{ color: "#023070" }}>
                            {index + 1}. {task.name} ({task.estTime}hrs)
                          </h6>
                          <small className="text-muted">
                            Due Date:
                            <small className="text-dark">
                              {ConvertDate(task.workedOn)}
                            </small>
                          </small>
                          <br />
                          <small className="text-muted">
                            Status:{" "}
                            <small
                              className={
                                task.percentage < 100
                                  ? `text-warning`
                                  : "text-success"
                              }
                            >
                              {task.status} ({task.percentage}%)
                            </small>
                          </small>
                        </div>
                        <Divider />
                      </div>
                    );
                  })
                ) : (
                  <div className="mt-3 d-flex flex-column align-items-center justify-content-center">
                    <h6>
                      <ErrorOutlineIcon className="mx-1" />
                      Task Not Added
                    </h6>
                    <Link to="/Employee/Task">
                      <p className="mt-5 fs-6 btn btn-info btn-sm">Add Task</p>
                    </Link>
                  </div>
                )}
              </div>
            )}
          </div>
        </Grid>
        <Grid item className="col-md-3 mt-3">
          <div className="card mx-1 bg-white">
            <div className="d-flex justify-content-between mx-3 mt-3">
              <AccessTimeIcon className="fs-4" />
              <p className="fs-5">Overdue Task's</p>
            </div>
            <Divider className="border border-dark" />
            {loading ? (
              <SkletonLoader />
            ) : (
              <div className="scroll" style={{ height: "340px" }}>
                {duetasks?.length > 0 ? (
                  duetasks?.map((e: any, index: number) => {
                    return (
                      <div key={index}>
                        <div className="fs-6 m-4">
                          <h6>
                            <small style={{ color: "#4343e8" }}>
                              Task Name:
                            </small>{" "}
                            {e.name}
                            <br />
                            <small style={{ color: "#4343e8" }}>
                              Description:
                            </small>{" "}
                            {e.description}
                            <br />
                          </h6>
                          <small className="text-danger">
                            <strong className="text-muted">
                              Created Date:{" "}
                            </strong>
                            {ConvertDate(e.createdDate)}
                            <br />
                            <strong className="text-muted">
                              Last updated:{" "}
                            </strong>
                            {formatTimeAgo(new Date(e.actualStartDate))}
                          </small>
                        </div>
                        <Divider />
                      </div>
                    );
                  })
                ) : (
                  <h6 className="mt-3 d-flex align-items-center justify-content-center">
                    <CheckCircleOutlineIcon className="mx-1" />
                    No Overdue Tasks
                  </h6>
                )}
              </div>
            )}
          </div>
        </Grid>
        <Grid item className="col-md-3 mt-3">
          <div className="card mx-1 bg-white">
            <div className="d-flex justify-content-between mx-3 mt-3">
              <BarChartIcon className="fs-4" />
              <p className="fs-5">Attendance</p>
            </div>
            <Divider className="border border-dark" />
            {loading ? (
              <SkletonLoader />
            ) : (
              <div className="scroll" style={{ height: "340px" }}>
                <p className="fs-6 m-3">
                  Date: {ConvertDate(new Date().toDateString())}
                </p>
                {loginDetails.length > 0 ? (
                  loginDetails?.map((e: any) => {
                    return (
                      <React.Fragment key={e.inTime}>
                        <div className="fs-6 m-3">
                          <h6>
                            <strong className="text-success">In Time: </strong>
                            {ConvertTime(e.inTime, "")}
                          </h6>
                          <h6>
                            <strong className="text-success">Out Time: </strong>
                            {e.outTime && ConvertTime(e.outTime, "")}
                          </h6>
                        </div>
                        <Divider />
                        <div className="fs-6 m-3">
                          <h6>
                            <strong className="text-success">Present: </strong>
                            {attendanceDto?.totalAttendance}
                          </h6>
                          <h6>
                            <strong className="text-success">Absent: </strong>
                            {attendanceDto?.totalAbsent}
                          </h6>
                        </div>
                        <Divider />
                        <div className="fs-6 m-3">
                          <h6>
                            <strong className="text-success">
                              Average In Time:{" "}
                            </strong>
                            {convertTo12HourFormat(
                              `${attendanceDto?.averageInTime}`
                            )}
                          </h6>
                          <h6>
                            <strong className="text-success">
                              Average Out Time:{" "}
                            </strong>
                            {convertTo12HourFormat(
                              `${attendanceDto?.averageOutTime}`
                            )}
                          </h6>
                        </div>
                      </React.Fragment>
                    );
                  })
                ) : (
                  <div className="mt-3 d-flex flex-column align-items-center justify-content-center">
                    <h6 className="mt-3 d-flex align-items-center justify-content-center">
                      <ErrorOutlineIcon className="mx-1" />
                      In Time Not Added
                    </h6>
                    <Link to="/Employee/EmployeeTime">
                      <p className="mt-5 fs-6 btn btn-info btn-sm">
                        Add In Time
                      </p>
                    </Link>
                  </div>
                )}
              </div>
            )}
          </div>
        </Grid>
      </div>
    </>
  );
};
export default EmployeeTask;
