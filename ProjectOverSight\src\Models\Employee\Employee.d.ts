import { IBase } from "../Common/BaseModel";

export interface Employee extends IBase {
  userId?: number | null;
  name?: string | null;
  email?: string | null;
  phoneNumber?: string | null;
  category?: string | null;
  department?: string | null;
  isActive?: boolean | null;
  officialIntime?: any | null;
  officialOuttime?: any | null;
  direction?: string | null;
  pageNumber?: number | null;
  pageSize?: number | null;
}

export interface SessionUser {
  employeeId: number;
  expiration: Date;
  token: string;
  userName: string;
  userId: number;
  userRoles: string;
}
