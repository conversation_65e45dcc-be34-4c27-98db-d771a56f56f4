import { FormControl, TextField } from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import { useContextProvider } from "../../../../CommonComponents/Context";
import { Category } from "../../../../Models/Common/CommonMaster";
import { Dispatch, SetStateAction, useRef, useState } from "react";
import { TaskDTO } from "../../../../Models/Task/Task";

type SelectCategoryProps = {
  setUIApplicable: Dispatch<SetStateAction<boolean>>;
  setTask: Dispatch<SetStateAction<TaskDTO>>;
  setSubCategory: Dispatch<SetStateAction<string>>;
};

export const SelectCategory = ({
  setUIApplicable,
  setTask,
  setSubCategory,
}: SelectCategoryProps) => {
  const { category, setLoading } = useContextProvider();
  const [selectedCategory, setCategory] = useState<string>("");
  const subCategoryRef = useRef<any>(null);

  return (
    <div className="d-flex flex-column justify-content-center align-items-center mt-3">
      <FormControl className="w-50 m-3">
        <Autocomplete
          disablePortal
          onFocus={() => setLoading((prev) => !prev)}
          options={[...new Set(category.map((x) => x.categories))]
            .sort((a, b) => a.localeCompare(b))
            .map((category: string) => ({
              label: category,
              id: category,
            }))}
          onChange={(e: any, value: any) => {
            if (value) {
              setCategory(value.label);
            } else {
              setCategory("");
              return e;
            }
            const ele = subCategoryRef.current.getElementsByClassName(
              "MuiAutocomplete-clearIndicator"
            )[0];
            if (ele) ele.click();
          }}
          style={{ width: "100%" }}
          renderInput={(params) => (
            <TextField {...params} label="Category" required />
          )}
        />
      </FormControl>
      <FormControl className="w-50 m-3">
        <Autocomplete
          ref={subCategoryRef}
          disablePortal
          options={category
            .filter((x) => x.categories === selectedCategory)
            .sort((a, b) => a.subCategory.localeCompare(b.subCategory))
            .map((category: Category) => ({
              label: category.subCategory,
              id: category.subCategory,
            }))}
          onChange={(e: any, value: any) => {
            if (value) {
              var selCategory: Category | undefined = category.find(
                (x) =>
                  x.subCategory.trim() === value.id.trim() &&
                  x.categories === selectedCategory
              );
              setTask((prev: TaskDTO) => {
                return { ...prev, categoryId: selCategory?.id ?? 0 };
              });
              setSubCategory(value.label);
              if (value.label === "UILevelTask") {
                setUIApplicable(true);
              } else {
                setUIApplicable(false);
                setTask((prev: TaskDTO) => {
                  return { ...prev, uiId: 0 };
                });
              }
              return e;
            } else {
              setTask((prev: TaskDTO) => {
                return { ...prev, categoryId: 0 };
              });
            }
          }}
          style={{ width: "100%" }}
          renderInput={(params) => (
            <TextField {...params} label="Sub Category" required />
          )}
        />
      </FormControl>
    </div>
  );
};
