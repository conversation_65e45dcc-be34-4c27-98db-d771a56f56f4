import {
  DANGER,
  LATE,
  LEAVE,
  ONTIME,
  SECONDARY,
  SUCCESS,
} from "../Constants/TimeSpanText";
import { format } from 'date-fns';
const TIME12 = "12:00 AM"

export function ConvertDate(date: string | null): string {
  if (date == null || date === "01-01-1") return "-";
  const convertedDate = new Date(date)
    .toLocaleDateString("en-bz")
    .replaceAll("/", "-");
  return convertedDate;
}

export function ConvertTime(date: string | null, meridian: string): string {
  if (date == null) return "-";
  const formattedTime = new Date(date).toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  });
  const convertTime = formattedTime + ' ' + meridian;
  return convertTime;
}



export function TimeSpanText(inTime: string, DeptTime: string, EmpTime: string) {
  debugger
  if (inTime === null) return LEAVE;

  const date = new Date(inTime);
  const formattedTime = format(date, 'HH:mm');

  function convertTo24HourFormat(timeString: string): string {
    if (typeof timeString !== 'string' || timeString.trim() === '') {
        return '';
    }

    const [hoursStr, minutesStr, period] = timeString.split(/:|\s/);

    if (hoursStr === undefined || minutesStr === undefined || period === undefined) {
        return '';
    }

    let hours = parseInt(hoursStr, 10);

    if (period === 'PM' && hours < 12) {
        hours += 12;
    } else if (period === 'AM' && hours === 12) {
        hours = 0;
    }

    const formattedHours = hours.toString().padStart(2, '0');

    return `${formattedHours}:${minutesStr}`;
}


  const formattedDeptTime = DeptTime ? convertTo24HourFormat(DeptTime) : null;
  const formattedEmpTime = EmpTime ? convertTo24HourFormat(EmpTime) : null;

  if (formattedEmpTime !== null) {
    if (formattedTime > formattedEmpTime) return LATE;
    if (formattedTime <= formattedEmpTime) return ONTIME;
  } else if (formattedDeptTime !== null) {
    if (formattedTime > formattedDeptTime) return LATE;
    if (formattedTime <= formattedDeptTime) return ONTIME;
  } else {
    return LEAVE;
  }
}
export function TimeSpanBg(inTime: string, DeptTime: any, EmpTime: any) {

  if (inTime === null) return SECONDARY;

  const date = new Date(inTime);
  const formattedTime = format(date, 'HH:mm');

  function convertTo24HourFormat(timeString: string): string {
    if (typeof timeString !== 'string' || timeString.trim() === '') {
        return '';
    }

    const [hoursStr, minutesStr, period] = timeString.split(/:|\s/);

    if (hoursStr === undefined || minutesStr === undefined || period === undefined) {
        return '';
    }

    let hours = parseInt(hoursStr, 10);

    if (period === 'PM' && hours < 12) {
        hours += 12;
    } else if (period === 'AM' && hours === 12) {
        hours = 0;
    }

    const formattedHours = hours.toString().padStart(2, '0');

    return `${formattedHours}:${minutesStr}`;
}

  const formattedDeptTime = DeptTime ? convertTo24HourFormat(DeptTime) : null;
  const formattedEmpTime = EmpTime ? convertTo24HourFormat(EmpTime) : null;

  if (formattedEmpTime !== null) {
    if (formattedTime > formattedEmpTime) return DANGER;
    if (formattedTime <= formattedEmpTime) return SUCCESS;
  } else if (formattedDeptTime !== null) {
    if (formattedTime > formattedDeptTime) return DANGER;
    if (formattedTime <= formattedDeptTime) return SUCCESS;
  } else {
    return SECONDARY;
  }
}


export function WeekEndingDate(): Date {
  let currentDate: Date = new Date();
  if (currentDate.getDay() == 5) return currentDate;
  let weekEndingDate: Date;
  let daysUntilFriday: number = (5 - currentDate.getDay() + 7) % 7;
  if (daysUntilFriday <= 0) {
    daysUntilFriday += 7;
  }
  weekEndingDate = new Date(
    currentDate.getTime() + daysUntilFriday * 24 * 60 * 60 * 1000
  );
  weekEndingDate.setHours(23, 59, 59, 999);
  return weekEndingDate;
}

export function GetFridaysOfMonth(): Date[] {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const fridays: Date[] = [];
  const dateIterator = new Date(currentDate.getFullYear(), currentMonth, 1);

  while (dateIterator.getMonth() === currentMonth) {
    if (dateIterator.getDay() === 5) {
      fridays.push(new Date(dateIterator));
    }
    dateIterator.setDate(dateIterator.getDate() + 1);
  }
  return fridays;
}

export function getSaturdaysAndSundays(year: number, month: number): number {
  const result: Date[] = [];
  const currentDate = new Date(year, month - 1, 1);
  while (currentDate.getMonth() === month - 1) {
    const dayOfWeek = currentDate.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      result.push(new Date(currentDate));
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }
  return result.length;
}

export function ConvertToISO(params: Date | string): string {
  const date = new Date(params);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const formattedDate = `${year}-${month}-${day}`;
  return formattedDate;
}

export function ReduceFiveDays(params: any): string {
  let newdate = new Date(params);
  newdate.setDate(newdate.getUTCDate() - 4);
  return ConvertToISO(newdate);
}

export function SetMinDate(): string {
  const today = new Date();
  return ConvertToISO(today);
}

export function WeekEndingDateUTC(): Date {
  let currentDate: Date = new Date();
  if (currentDate.getUTCDay() == 5) return currentDate;
  let weekEndingDate: Date;
  let daysUntilFriday: number = (5 - currentDate.getUTCDay() + 7) % 7;
  if (daysUntilFriday <= 0) {
    daysUntilFriday += 7;
  }
  weekEndingDate = new Date(
    currentDate.getTime() + daysUntilFriday * 24 * 60 * 60 * 1000
  );
  weekEndingDate.setHours(23, 59, 59, 999);
  return weekEndingDate;
}

export function TimeSpan(employeeTime: any) {
  var onTime: number = 0;
  var late: number = 0;
  employeeTime.forEach((e: any) => {
    if (e.inTime !== null) {
      const officeTime: Date = new Date(`${e.inTime.slice(0, 10)}T10:00:01`);
      const InTime = new Date(e.inTime);
      if (InTime.getTime() > officeTime.getTime()) late++;
      if (InTime.getTime() <= officeTime.getTime()) onTime++;
    }
  });
  return { onTime, late };
}

export function getDatesBetween(fromDate: string, toDate: string) {
  const dates = [];
  const currentDate = new Date(fromDate);
  var ToDate = new Date(toDate);

  while (currentDate <= ToDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

export function convertTo12HourFormat(time24: string): string {
  if (!time24) return "-"
  const [hours, minutes] = time24.split(':');
  const parsedHours = parseInt(hours, 10);
  const period = parsedHours >= 12 ? 'PM' : 'AM';
  const hours12 = parsedHours % 12 || 12;
  const time12 = `${hours12}:${minutes} ${period}`;
  if (time12 === TIME12) return "00:00"
  return time12;
}

export function calculateTotalHours(startDateStr: string, endDateStr: string) {
  if(!startDateStr || !endDateStr) return "-"
  const startDate = new Date(startDateStr);
  const endDate = new Date(endDateStr);
  const timeDiff = endDate.getTime() - startDate.getTime();
  const hoursDiff = timeDiff / (1000 * 60 * 60);
  return hoursDiff >= 7 ? "Full Day":"Half Day"
}




const formatter = new Intl.RelativeTimeFormat(undefined, {
  numeric: "always",
})

const DIVISIONS: { amount: number; name: Intl.RelativeTimeFormatUnit }[] = [
  { amount: 60, name: "seconds" },
  { amount: 60, name: "minutes" },
  { amount: 24, name: "hours" },
  { amount: 7, name: "days" },
  { amount: 4.34524, name: "weeks" },
  { amount: 12, name: "months" },
  { amount: Number.POSITIVE_INFINITY, name: "years" },
]

export function formatTimeAgo(date: Date) {
  let duration = (date.getTime() - new Date().getTime()) / 1000

  for (let i = 0; i < DIVISIONS.length; i++) {
    const division = DIVISIONS[i]
    if (Math.abs(duration) < division.amount) {
      return formatter.format(Math.round(duration), division.name)
    }
    duration /= division.amount
  }
}

const YEARS: number[] = []
var START_YEAR = 2023
const CURRENT_YEAR = new Date().getFullYear()

while (START_YEAR <= CURRENT_YEAR) {
  YEARS.unshift(START_YEAR)
  START_YEAR++;
}

export default YEARS;