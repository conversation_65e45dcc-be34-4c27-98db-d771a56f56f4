import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Grid, <PERSON><PERSON><PERSON>, Typo<PERSON>, Box } from '@mui/material'
import Select from "react-select";
import { Link,useLocation} from 'react-router-dom';
import '../../../StyleSheets/RequirementMatrix.css'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleDoubleDown } from '@fortawesome/free-solid-svg-icons';
import DataTable from 'react-data-table-component';
import { useEffect, useRef, useState } from 'react';
import { Get} from "../../../Services/Axios";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import AddIcon from '@mui/icons-material/Add';
import "../../../App.css";
import { ViewRequirement } from './ViewRequirement';
import EditRequirement from './EditRequirement';
import { Project } from '../../../Models/Project/Project';
import BackDrop from "../../../CommonComponents/BackDrop";
import AddRequirement from './AddRequirement';
import { useContextProvider } from '../../../CommonComponents/Context';
import BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';

export const RequirementMatrixDashboard = ({

}: any) => {
  const location = useLocation();
  const { role } = useContextProvider();
  const [showSecondRow, setShowSecondRow] = useState(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [reload, setReload] = useState<boolean>(true);
  const [requirements, setRequirements] = useState<any>([]);
  const [ RestRequirements , setRestRequirements] = useState<any>([]);
  const projectNameRef = useRef<any>();
  const ReqirementSourceRef = useRef<any>();
  const ReqirementDefinitionRef = useRef<any>();
  const ReqirementTypeRef = useRef<any>();
  const ReleaseDateRef = useRef<any>();
  const [Projectlist, setProjectlist] = useState<any>();
  const [filter, setFilter] = useState<any>({});
  const [requirementdata, setRequirementdata] = useState<any>();
  const [requirementView, setRequirementView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });

  const fetchData = async () => {
    debugger
    try {
      const response: any = await Get(
        `app/Requirement/GetRequirements`,
      );
      const getProject: any = await Get(`app/Project/GetProjectList`)
      setRequirements(response?.data);
      console.log(response?.data);
      setRestRequirements(response?.data);
      setProjectlist(getProject?.data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };
  const handleClickOpen = () => {
    setRequirementView({ add: true });
  };

  useEffect(() => {
    fetchData();
  }, [reload]);


  const columns: any = [
    {
      name: "Action",
      center: true,
      width: "13rem",
      selector: (row: any) => (
        <>
          <Tooltip
            className="mx-2"
            title="View Requirement"
            onClick={() => {
              setRequirementView({ view: true });
              setRequirementdata(row);
            }}
          >
            <VisibilityIcon className="fs-4 text-info" />
          </Tooltip>
          <>
            <Tooltip
              className="mx-2"
              title={row.percentage === 100 ? "Completed" : "Edit Requirement"}
              onClick={() => {
                if (!(row.percentage === 100)) {
                  setRequirementView({ edit: true });
                  setRequirementdata(row);
                }
              }}
            >
              {row.percentage === 100 ? (
                <TaskAltIcon className="fs-4 text-success" />
              ) : (
                <EditIcon className="fs-4 text-warning" />
              )}
            </Tooltip>
            { role == "Customer" && 
            <Link
                className="mx-2"
                to="/Customer/AssignUserStory"
                state={{
                  ...location?.state,
                  RequirementSource: row.requirementSource,
                  RequirementID: row.id,
                  projectId: row.projectId,
                  projectName: row.projectName,
                }}
                style={{ textDecoration: "none" }}
              >
                <Tooltip title="Assign User Story">
                  <BookmarkAddedIcon className="fs-4 text-info" />
                </Tooltip>
              </Link>
    }
        
          </>

        </>
      ),
    },
    {
      field: "Project Name",
      name: "Project Name",
      type: "name",
      width: "10rem",
      editable: false,
      center: true,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",

      selector: (row: any) => <p className="tableStyle">{row.projectName}</p>,
    },
    {
      field: "requirementSource",
      name: "Requirement Source",
      width: "16rem",
      center: true,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => (
        <Tooltip title={row.requirementSource} style={{ textDecoration: "none" }}>
          <p className="tableStyle">{row.requirementSource}</p>
        </Tooltip>
      ),
    },
    {
      field: "requirementType",
      name: "RequirementType",
      width: "13rem",
      center: true,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => (
        <Tooltip title={row.requirementType} style={{ textDecoration: "none" }}>
          <p className="tableStyle">{row.requirementType}</p>
        </Tooltip>
      ),
    },
    {
      field: "requirementDefinition",
      name: "Requirement Definition",
      width: "20rem",
      editable: false,
      center: true,
      headerClassName: "bg-primary text-light",
      selector: (row: any) => (
        <Tooltip title={row.requirementDefinition} style={{ textDecoration: "none" }}>
          <p className="tableStyle">{row.requirementDefinition}</p>
        </Tooltip>
      ),
    },
    {
      field: "sourceDocument",
      name: "Source Document",
      width: "20rem",
      editable: false,
      center: true,
      headerClassName: "bg-primary text-light",
      selector: (row: any) => (
        <Tooltip title={row.sourceDocument} style={{ textDecoration: "none" }}>
          <p className="tableStyle">{row.sourceDocument}</p>
        </Tooltip>
      ),
    },
    {
      field: "Stage",
      name: "Stages",
      width: "20rem",
      editable: false,
      center: true,
      headerClassName: "bg-primary text-light",
      selector: (row: any) => (
        <Tooltip title={row.stages} style={{ textDecoration: "none" }}>
          <p className="tableStyle">{row.stages}</p>
        </Tooltip>
      ),
    },
    {
      field: "Design",
      name: "Design",
      type: "string",
      width: "10rem",
      center: true,
      headerClassName: "bg-primary text-light",
      selector: (row: any) => <p className="tableStyle">{row.design}</p>,
    },
    {
      field: "Development/Implementation",
      name: "Development/Implementation",
      type: "string",
      width: "15rem",
      center: true,
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.development}</p>,
    },
    {
      field: "Testing",
      name: "Testing",
      type: "string",
      width: "10rem",
      center: true,
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.testing}</p>,
    },
    {
      field: "Category",
      name: "Requirement Category",
      type: "string",
      width: "14rem",
      center: true,
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      editable: false,
      selector: (row: any) =>
        <p className="tableStyle">{row.category}</p>,
    },
    {
      field: "Testing",
      name: "Requirement SubCategory",
      type: "string",
      width: "14rem",
      center: true,
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.subCategory}</p>,
    },
    {
      field: "actualReleaseDate",
      name: "Release Date",
      type: "Date",
      width: "10rem",
      selector: (row: any) => {
        const date = row.estimatedReleaseDate ? new Date(row.estimatedReleaseDate) : null;
        const result = date
          ? date.toLocaleDateString("en-bz").replaceAll("/", "-")
          : " ";
        return <p className="tableStyle">{result}</p>;
      },
    }
    
  ];

  const handleDropButtonClick = () => {
    setShowSecondRow(!showSecondRow);
  };

  function applyFilter() {
    let temp: Array<any> = requirements;

    if (filter.project) {
      temp = temp.filter((x) => x.projectName === filter.project);
      setRequirements(temp);
    }

    if (filter?.ReqirementDefinition) {
      temp = temp?.filter((x: any) => x?.requirementDefinition === filter?.ReqirementDefinition);
      setRequirements(temp);
    }

    if (filter.ReqirementSource != null) {
      temp = temp?.filter((e: any) => {
        return e?.requirementSource?.toLowerCase()?.search(filter.ReqirementSource?.toLowerCase()) >= 0;
      });
      setRequirements(temp);
    }
    
    if (filter.ReqirementType != null) {
      temp = temp?.filter((e: any) => {
        return e?.requirementType?.toLowerCase()?.search(filter.ReqirementType?.toLowerCase()) >= 0;
      });
      setRequirements(temp);
    }

    if (filter.ReleaseDate != null) {
      temp = temp?.filter((e: any) => {
        return e?.estimatedReleaseDate?.slice(0, 10)?.includes(filter.ReleaseDate?.slice(0, 10));
      });
      setRequirements(temp);
    }
  }


  function reset() {
    projectNameRef.current.clearValue();
    ReqirementSourceRef.current.clearValue();
    ReqirementDefinitionRef.current.clearValue();
    ReleaseDateRef.current.value = '';
    setRequirements(RestRequirements);
    setFilter({});
  }


  return (
    <div className='main-container '>
      <div>
        <Breadcrumbs className="mt-3 mx-3 " separator=">">
          <Link to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link to={`/${role}/RequirementMatrixDashboard`}>
            <Typography sx={{ fontWeight: "bold", color: 'grey' }}>Requirement Tracebility Matrix</Typography>
          </Link>
        </Breadcrumbs>
      </div>
      <Button
        variant="contained"
        className="mb-3 float-md-start float-right custom-button-gradient"
        style={{ marginRight: '10%', borderRadius: '10px' }}
        onClick={handleClickOpen}

        sx={{ ml: '3%' }}
      >
        Add Requirement
        <AddIcon className="mx-1" />
      </Button>

      <div className="top-div mx-auto shadow-container"
        style={{
          border: '2px solid #3498db',
          marginTop: '4%',
          borderRadius: '20px',
          padding: '30px',
          width: '80%',
          backgroundColor: 'white',
          transition: 'box-shadow 0.3s ease',

        }}>

        <div className='First row' style={{ display: 'flex', alignItems: 'center' }}>
          <div className="col-sm-5 " style={{ display: 'flex' }}>
            <div className="mx-2 w-100">
              <label>Project Name</label>
              <Select
                id="project-name"
                isClearable={true}
                ref={projectNameRef}
                className="col mt-1 custom-select"
                onChange={(selectedOption: any) => {
                  setFilter((prevState: Project) => {
                    return {
                      ...prevState,
                      project: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                options={(Projectlist && Array.isArray(Projectlist)) ? Projectlist.map((e: Project) => ({
                  label: e.name,
                  value: e.name,
              })) : []}
                styles={{
                  menu: (provided: any) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          <div className="mx-2 w-100">
              <label>Reqirement Source</label>
              <Select
                id="reqirement-Source"
                isClearable={true}
                ref={ReqirementSourceRef}
                className="col mt-1 custom-select"
                // onChange={(e) => {
                //   const inputValue = e.target.value;
                //   const alphabeticValue = inputValue.replace(/[^A-Za-z]/g, " ");
                //   setFilter((prevState: any) => ({
                //     ...prevState,
                //     ReqirementSource: alphabeticValue === "" ? null : alphabeticValue,
                //   }));
                // }}
                onChange={(selectedOption: any) => {
                  setFilter((prevState: any) => {
                    return {
                      ...prevState,
                      ReqirementSource: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                // value={filter.ReqirementSource || ""}
                 options={RestRequirements?.map((e: any) => ({
                  label: e.requirementSource,
                  value: e.requirementSource,
                }))}
                styles={{
                  menu: (provided: any) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-3">
          <div className="mx-2 w-100">
              <label>Reqirement Definition</label>
              <Select
                id="reqirement-Source"
                isClearable={true}
                ref={ReqirementDefinitionRef}
                className="col mt-1 custom-select"
                // onChange={(e) => {
                //   const inputValue = e.target.value;
                //   const alphabeticValue = inputValue.replace(/[^A-Za-z]/g, " ");
                //   setFilter((prevState: any) => ({
                //     ...prevState,
                //     ReqirementSource: alphabeticValue === "" ? null : alphabeticValue,
                //   }));
                // }}
                onChange={(selectedOption: any) => {
                  setFilter((prevState: any) => {
                    return {
                      ...prevState,
                      ReqirementDefinition: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                // value={filter.ReqirementSource || ""}
                 options={RestRequirements?.map((e: any) => ({
                  label: e.requirementDefinition,
                  value: e.requirementDefinition,
                }))}
                styles={{
                  menu: (provided: any) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-md-1">
            <Button
              variant="contained"
              style={{
                borderRadius: '30px', fontSize: 'x-large', border: '1px solid #2c73ff',
              }}
              className="mt-4 drop-button custom-btn-gradient"
              onClick={handleDropButtonClick}
            >
              <FontAwesomeIcon icon={faAngleDoubleDown} />
            </Button>
          </div>
          <div className="col-md-3">
            <div className="row ">
              <div className='col-6'>
                <Button
                  style={{ borderRadius: '15px', border: '1px solid #2c73ff'}}
                  variant="contained"
                  className="mt-4 custom-button-gradient"
                  onClick={applyFilter}
                >
                  Search
                </Button>
              </div>
              
              <div className='col-6'>
                <Button
                  style={{ borderRadius: '15px', border: '1px solid #2c73ff', }}
                  variant="contained"
                  className="mt-4 custom-button-gradient"
                  onClick={reset}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>

        </div>

        <div className='Second-row mt-3'
          style={{
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'center',
            transition: 'height 0.3s ease-in-out',
            height: showSecondRow ? 'auto' : 0,
            overflow: 'hidden',

          }}>

          <div className="col-sm-3">
            <div className="form-group">
              <label>Reqirement Type</label>
              <input
                id="reqirement-type"
                ref={ReqirementTypeRef}
                placeholder="Reqirement Type"
                className="m-1 form-control"
                style={{
                  height: '40px',
                  width: '100%',
                  boxShadow: '0 0 6px 0 rgba(0, 0, 0, 0.1)',
                  borderRadius: '4px',
                  padding: '8px'
                }}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const alphabeticValue = inputValue.replace(/[^A-Za-z]/g, " ");
                  setFilter((prevState: any) => ({
                    ...prevState,
                    ReqirementType: alphabeticValue === "" ? null : alphabeticValue,
                  }));
                }}
                value={filter.ReqirementType || ""}
              />
            </div>
          </div>

          <div className="col-sm-3">
            <div className="form-group mx-2">
              <label className="">Release Date</label>
              <input
                type="date"
                ref={ReleaseDateRef}
                id="actual-start-date"
                className="m-1 form-control"
                style={{
                  height: '40px',
                  width: '100%',
                  boxShadow: '0 0 6px 0 rgba(0, 0, 0, 0.1)',
                  borderRadius: '4px',
                  padding: '8px'
                }}
                onChange={(e: any) => {
                  setFilter((prevState: any) => {
                    return {
                      ...prevState,
                      ReleaseDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
              />
            </div>
          </div>
          <div className="container">
            <div className="row">
            </div>
          </div>
        </div>
      </div>
      <Grid item xs={12} sm={11} sx={{ mt: '5%' }}>
        <Box
          style={{
            width: "80vw",
            position: 'relative',
            margin: 'auto',
            boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.1)',
          }}>
          <DataTable
            columns={columns}
            fixedHeader={true}
            responsive
            persistTableHead

            data={requirements || []}
            customStyles={{

              table: {
                style: {
                  height: "80vh",
                  border: "none",
                  overflowY: "scroll",
                  alignItems: 'center',

                },
              },

              headRow: {
                style: {
                  background: 'linear-gradient(to right, #2c80f6e0, #1a67ff)',
                  fontSize: "15px",
                  fontWeight: '',
                  color: "white",
                  fontFamily: "inherit",
                  alignItems: 'center',

                },
              },
              pagination: {
                style: {

                  position: "absolute",
                  width: "100%",
                  background: "white",
                  color: "#333",
                  textAlign: "right",
                  top: -55,
                  borderRadius: "20px 20px 0px 0px",
                },
              },
            }}
            pagination
            paginationPerPage={50}
            paginationRowsPerPageOptions={[50, 100, 200]}
            pointerOnHover={true}
          />
         { requirementView?.add &&<AddRequirement
            openDialog={requirementView}
            setOpenDialog={setRequirementView}
            setReload={setReload}
          />}

         {requirementView?.edit &&
          <EditRequirement
            openDialog={requirementView}
            setOpenDialog={setRequirementView}
            Data={requirementdata}
            setReload={setReload}
          />}

          {requirementView?.view && <ViewRequirement
            openDialog={requirementView}
            setOpenDialog={setRequirementView}
            Data={requirementdata}
          />}
        </Box>
      </Grid>
      <BackDrop open={loading} />
    </div>
  )
}

