import {
  Dialog,
  DialogActions,
  <PERSON>alogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Checkbox,
  ListItemText,
  OutlinedInput,
  Grid,
} from "@mui/material";
import * as React from "react";
import Select from "@mui/material/Select";
import { Get } from "../../../Services/Axios";
import { ConvertDate } from "../../../Utilities/Utils";
import { useEffect, useState } from "react";
import TextareaAutosize from "@mui/material/TextareaAutosize";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { TransitionProps } from "@mui/material/transitions";
import Slide from "@mui/material/Slide";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};


const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export const ViewProject = ({ openDialog, setOpenDialog, Data }: any) => {
  const [techStack, settechStack] = useState<any>([]);
  const [commonMaster, setCommonMaster] = useState<any>([]);
  const [TeamName,setTeamName] = useState<any>([]);
  const handleClose = () => {
    setOpenDialog({ add: false });
  };

  async function fetchCommonMaster2() {
    const commonMaster: any = await Get("app/CommonMaster/GetCodeTableList");
    const teamName: any = await Get("app/Team/GetTeamList");
    setCommonMaster(commonMaster.data);
    setTeamName(teamName?.data)
  }

  useEffect(() => {
    fetchCommonMaster2();
    let temp: any = [];
    Data?.projectTechStacks?.map((e: any) => {
      temp.push(e.techStack);
    });
    settechStack(temp);
  }, [openDialog?.view]);

  return (
    <div>
      <Dialog open={openDialog?.view || false}  TransitionComponent={Transition}>
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              View Project
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
          <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                className="read-only-input"
                label="Project Name"
                disabled={Data?.name? false : true}
                value={Data?.name}
                type="text"
                variant="outlined"
              />
              </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                label="Project Type"
                disabled={Data?.type? false : true}
                value={Data?.type}
                type="text"
                variant="outlined"
              />
            </FormControl>
            </Grid>
              <Grid item xs={12} >
              <InputLabel id="description" >
                Description
              </InputLabel>
              <FormControl fullWidth>
              <TextareaAutosize
                disabled={Data?.description? false : true}
                style={{ height: "5rem" }}
                id="description"
                className="read-only-input form-control"
                defaultValue={Data?.description}
              />
           </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Start date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  className="read-only-input"
                  disabled={Data?.startDate? false : true}
                  id="start-date"
                  label=""
                  type="text"
                  value={ConvertDate(Data?.startDate)}
                  fullWidth
                  variant="outlined"
                />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">End date</InputLabel>
                <FormControl fullWidth>
                <TextField
                   className="read-only-input"
                   disabled={Data?.endDate? false : true}
                  id="end-date"
                  label=""
                  value={ConvertDate(Data?.endDate)}
                  type="text"
                  fullWidth
                  variant="outlined"
                />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <InputLabel id="project-type">Tech Stack</InputLabel>
                <Select
                  labelId="Tech-Stack"
                  id="Tech-Stack"
                  multiple
                  className="read-only-input"
                  disabled={techStack? false : true}
                  defaultValue={techStack}
                  label="Tech Stack"
                  style={{ pointerEvents: "none", touchAction: "none" }}
                  value={techStack}
                  input={<OutlinedInput label="Tech Stack" />}
                  renderValue={(selected: any) => (
                    <div>
                      {selected.map((value: number) => (
                        <span key={value} style={{ marginRight: 8 }}>
                          {
                            commonMaster.find(
                              (option: any) => option.id === value
                            )?.codeName
                          }
                          ,
                        </span>
                      ))}
                    </div>
                  )}
                  MenuProps={MenuProps}
                >
                  {commonMaster?.map((e: any) => {
                    if (e.codeType == "ProjectTechStackCatagory")
                      return (
                        <MenuItem value={e.id} key={e.codeValue}>
                          <Checkbox
                            checked={techStack.indexOf(e.id as never) > -1}
                          />
                          <ListItemText primary={e.codeName} />
                        </MenuItem>
                      );
                  })}
                </Select>
                </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <TextField
                  margin="dense"
                  label="Status"
                  className="read-only-input"
                  value={Data?.status}
                  type="text"
                  fullWidth
                  variant="outlined"
                  disabled={Data?.status? false : true}
                />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                {/* <TextField
                className="col m-2 read-only-input"
                label="Team Name"
                disabled={Data?.teamId? false : true}
                value={Data?.teamId}
                type="text"
                variant="outlined"
              /> */}
                  <InputLabel id="team">Team List</InputLabel>
                  <Select
                    labelId="team"
                    id="team"
                    className="read-only-input"
                    required
                    disabled={Data?.teamId? false : true}
                    value={Data?.teamId}
                    label="Team List"
                  >
                    {TeamName?.filter((x: any) => x.isActive === true && x.id === Data?.teamId)
                      .map((e: any) => (
                        <MenuItem value={e.id} key={e.id}>
                          {e.name}
                        </MenuItem>
                      ))}
                  </Select>
                  </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                <TextField
                className="read-only-input"
                label="Is Active"
                disabled={Data?.isActive? false : true}
                value={Data?.isActive}
                type="text"
                variant="outlined"
              />
            </FormControl>
            </Grid>
           </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="contained">
              Ok
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
