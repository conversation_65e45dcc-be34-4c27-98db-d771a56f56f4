import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  TextareaAutosize,
  InputLabel,
  Grid,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { useState, useEffect } from "react";
import { Get } from "../../../Services/Axios";

export const ViewRequirement = ({ openDialog, setOpenDialog, Data }: any) => {
  const handleClose = () => {
    setOpenDialog({ view: false });
  };
  const [getObjectives, setgetObjectives] = useState<any>([]);

  const getData = async () => {
    try {

      const response: any = await Get(
        `app/Requirement/GetRequirementTracebalityMapper?RequirementId=${Data?.id}`
      );
      const allGetObjectivesData = [];

      for (const projectId of response.data) {
        const getObjectives: any = await Get(
          `app/Project/GetProjectObjectiveById?Id=${projectId?.projectobjectiveID}`
        );
        allGetObjectivesData.push(getObjectives?.data);
      }

      const getObjectivesWithDescription = allGetObjectivesData?.filter(
        (objective: any) => objective?.description
      );

      setgetObjectives(getObjectivesWithDescription);
      console.log(getObjectives);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };
  useEffect(() => {
    getData();
  }, [openDialog?.edit]);

  return (
    <div>
      <Dialog open={openDialog?.view}>
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Requirement View
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
          <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                required
                value={Data?.projectName}
                label="Project Name"
                type="text"
                variant="outlined"
                className="read-only-input"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                value={Data?.requirementSource}
                className="read-only-input"
                label="Requirement Source"
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                value={Data?.sourceDocument}
                className="read-only-input"
                label="Source Document "
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                value={Data?.requirementNumberAsPerSourceDocument}
                className="read-only-input"
                label="RequirementNumberAsPerSourceDocument"
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                value={Data?.requirementType}
                className="read-only-input "
                label="Requirement Type"
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                {" "}
                {/* Adjust the width by changing col-sm-6 */}
                <TextField
                  required

                  className="read-only-input col m-1"
                  value={Data?.isActive == true? "Yes":"No"}
                  margin="dense"
                  label="Is Active"
                  type="text"
                  variant="outlined"
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </FormControl>
            </Grid>
              <Grid item xs={12}>
              <InputLabel>Requirement Definition</InputLabel>
              <FormControl fullWidth>
              <TextareaAutosize
                    required
  
                    className="read-only-input"
                    placeholder="Requirement Definition"
                    defaultValue={Data?.requirementDefinition}
                    style={{ width: "100%", height: 100 }}
                    readOnly
                  />
              </FormControl>
              </Grid>
              {/* <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                value={Data?.sourceDocumentSpecification}
                className="read-only-input col m-1"
                label="Source Document Specification"
                type="text"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
              </Grid> */}
              <Grid item xs={12} md={6}>
           <FormControl fullWidth>
                <TextField
                  required

                  className="read-only-input"
                  value={Data?.category}
                  margin="dense"
                  label="Requirement Category"
                  type="text"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                className="read-only-input"
                value={Data?.subCategory}
                margin="dense"
                label="Requirement Sub Category"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
              </Grid>
            <Grid item xs={12} md={6}>
              {!Data?.estimatedReleaseDate?.slice(0, 10) && 
              <InputLabel>Release Date</InputLabel>}
                <FormControl fullWidth>
                <TextField
                  required
                  label={Data?.estimatedReleaseDate?.slice(0, 10) ? 'Release Date' : ''}
                  id="start-date"
                  value={Data?.estimatedReleaseDate?.slice(0, 10)}
                  margin="dense"
                  type="date"
                  className="read-only-input col m-1"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    readOnly: true,
                  }}
                />
                </FormControl>
             </Grid>
              {/* <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                className="read-only-input col m-1"
                value={
                  Array.isArray(getObjectives) && getObjectives.length > 0
                    ? getObjectives
                        .map((objective: any) => objective.description)
                        .join(", ")
                    : ""
                }
                margin="dense"
                label="Project Objective"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
            </Grid> */}
              <Grid item xs={12} md={6}>
              {!Data?.estimatedReleaseDate?.slice(0, 10) && 
              <InputLabel className="mt-3"></InputLabel>}
              <FormControl fullWidth>
              <TextField
                required
                className="read-only-input "
                value={Data?.design}
                margin="dense"
                label="Design"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
           </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                className="read-only-input "
                value={Data?.development}
                margin="dense"
                label="Development"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
           </Grid>
           <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                className="read-only-input "
                value={Data?.testing}
                margin="dense"
                label="Testing"
                type="text"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              </FormControl>
           </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="primary"
            >
              OK
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
