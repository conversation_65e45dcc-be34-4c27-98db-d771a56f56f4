@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  --body-color: #E4E9F7;
  --sidebar-color: rgb(3, 108, 219);
  --primary-color: rgb(3, 108, 219);
  --primary-color-light: #F6F5FF;
  --toggle-color: #DDD;
  --text-color: #f5f5f5;

  --tran-03: all 0.2s ease;
  --tran-04: all 0.3s ease;
  --tran-05: all 0.3s ease;
}


::selection {
  background-color: var(--primary-color);
  color: #fff;
}


.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 250px;
  padding: 10px 14px;
  background: var(--sidebar-color);
  transition: var(--tran-05);
  z-index: 100;
}

.sidebar.close {
  width: 88px;
}

.sidebar li {
  height: 50px;
  list-style: none;
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.sidebar header .image,
.sidebar .icon {
  min-width: 60px;
  border-radius: 6px;
}

.sidebar .icon {
  min-width: 60px;
  border-radius: 6px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.sidebar .text,
.sidebar .icon {
  color: var(--text-color);
  transition: var(--tran-03);
  text-align: left;
}

.sidebar .text {
  font-size: 18px;
  text-align: center;
  font-weight: 500;
  white-space: nowrap;
  opacity: 1;
}

.sidebar.close .text {
  opacity: 0;
}

.sidebar header {
  position: relative;
}


.image-text {
  margin-top: 10px;
}

header .image-text .name {
  margin-top: 2px;
  font-size: 18px;
  font-weight: 600;
}

header .image-text .profession {
  font-size: 16px;
  margin-top: -2px;
  display: block;
}

.sidebar header .image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar header .image img {
  width: 40px;
  border-radius: 6px;
}

.sidebar header .toggle {
  position: absolute;
  top: 50%;
  right: -25px;
  transform: translateY(-50%) rotate(180deg);
  height: 25px;
  width: 25px;
  background-color: var(--primary-color);
  color: var(--sidebar-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  cursor: pointer;
  transition: var(--tran-05);
}

body.dark .sidebar header .toggle {
  color: var(--text-color);
}

.sidebar.close .toggle {
  transform: translateY(-50%) rotate(0deg);
}


.sidebar .menu {
  margin-top: 40px;
}

.sidebar li.search-box {
  border-radius: 6px;
  background-color: var(--primary-color-light);
  cursor: pointer;
  transition: var(--tran-05);
}

.sidebar li.search-box input {
  height: 100%;
  width: 90%;
  outline: none;
  text-align: center;
  border: 1px solid lightblue;
  background-color: var(--primary-color-light);
  color: rgb(153, 151, 151);
  border-radius: 6px;
  font-size: 17px;
  font-weight: 500;
  transition: var(--tran-05);
}

.sidebar li a {
  list-style: none;
  border: 1px solid #fff;
  text-align: center;
  padding: 10px;
  height: 100%;
  background-color: transparent;
  height: 100%;
  width: 100%;
  border-radius: 6px;
  transition: var(--tran-03);
}

.sidebar li a:hover {
  background-color: var(--primary-color);
}


.sidebar .menu-bar {
  height: calc(100% - 55px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow-y: scroll;
}

.menu-bar::-webkit-scrollbar {
  display: none;
}

.users-div {
  height: 150px;
  width: 300px;
  border-radius: 10px;
  padding: 5px;
  border: 1px solid lightgray;
}