import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    CardHeader,
    Checkbox,
    Divider,
    Grid,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    TextField,
    Typography,
  } from "@mui/material";
  import { Link, useLocation } from "react-router-dom";
  import { Get, Post } from "../../../../Services/Axios";
  import { UserInterface } from "../../../../Models/Project/UserInterface";
  import React from "react";
  import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
  import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
  import { ADMIN } from "../../../../Constants/Common/Roles";
  import Swal from "sweetalert2";
  import BackDrop from "../../../../CommonComponents/BackDrop";
  import { AlertOption } from "../../../../Models/Common/AlertOptions";
  import { AssignmentMapper } from "../../../../Models/Common/AssignmentMapper";
  
  function not(a: readonly UserInterface[], b: readonly UserInterface[]) {
    return a.filter((value) => b.indexOf(value) === -1);
  }
  
  function intersection(
    a: readonly UserInterface[],
    b: readonly UserInterface[]
  ) {
    return a.filter((value) => b.indexOf(value) !== -1);
  }
  
  function union(a: readonly UserInterface[], b: readonly UserInterface[]) {
    return [...a, ...not(b, a)];
  }
  
  export const AssignUserStory = () => {
    const location = useLocation();
    const [loading, setLoading] = React.useState<boolean>(false);
    const [checked, setChecked] = React.useState<UserInterface[]>([]);
    const [left, setLeft] = React.useState<UserInterface[]>([]);
    const [right, setRight] = React.useState<UserInterface[]>([]);
    const [filter, setFilter] = React.useState<UserInterface[]>([]);
    const leftChecked = intersection(checked, left);
    const rightChecked = intersection(checked, right);
    const [assignmentMapper, setAssignmentMapper] =
      React.useState<AssignmentMapper>({ assignedList: [], unassignedList: [] });
    var assignedList: UserInterface[] = [];
    var unassignedList: UserInterface[] = [];
  
    async function fetchUserInterface() {

      setLoading(true);
      const userInterface: any = await Get<Promise<any>>(
        `app/Project/GetUserStoryList?projectId=${location.state.projectId}`
      );
      const userStoryUi: any = await Get<Promise<any>>(
        `app/Requirement/GetAssignRequirementUserStory?requirementId=${location.state.RequirementID}`
      );
  
      let left: any[] = [];
      let right: any[] = [];
  
      userInterface?.data
  ?.filter((n: any) => n.projectId == location.state.projectId)
  .map((e: any) => {
    debugger;
    // Fix the reference to e.id instead of n.id
    let condition = userStoryUi?.data.find((x: any) => x.userStoryId === e.id);

    if (condition) {
      right.push(e);
    } else {
      left.push(e);
    }
  });

      setLeft(left || []);
      setFilter(left || []);
      setRight(right || []);
      setLoading(false);
    }
  
    React.useEffect(() => {
      fetchUserInterface();
    }, []);
  
    function toUserInterface(userInterface: UserInterface): UserInterface {
      return {
        uiId: userInterface.id,
        userStoryId: location.state.UserStoryId,
        userInterfaceName: userInterface.name,
        CreatedBy: ADMIN,
        UpdatedBy: ADMIN,
      };
    }
  
    async function SaveUserStoryUI() {
      debugger
        const joinedIds:any = [];
        right.forEach(r => joinedIds.push(r.id));
    
        const newObj = {
          requirementsId: location?.state?.RequirementID,
          projectId: location?.state?.projectId,
          userStoryIds: joinedIds,
          CreatedBy:"User",
          UpdatedBy:"User"  
        };
      
      
      const { error }: any = await Post(
        "app/Requirement/AssignRequirementUserStory",
        newObj
      );

      var option: AlertOption;
      if (error) {
        option = {
          title: "Error",
          text: "Error Occured While Assigning!",
          icon: "error",
        };
      } else {
        option = {
          title: "Success",
          text: "User Story Assigned Successfully!",
          icon: "success",
        };
      }
      Swal.fire({
        ...option,
        showConfirmButton: true,
      });
      setAssignmentMapper({ assignedList: [], unassignedList: [] });
    }
  
    const handleToggle = (value: UserInterface) => () => {
      debugger
      const currentIndex = checked.indexOf(value);
      const newChecked = [...checked];
  
      if (currentIndex === -1) {
        newChecked.push(value);
      } else {
        newChecked.splice(currentIndex, 1);
      }
      setChecked(newChecked);
    };
  
    const stringOfChecked = (items: readonly UserInterface[]) =>
      intersection(checked, items).length;
  
    const handleToggleAll = (items: readonly UserInterface[]) => () => {
      if (stringOfChecked(items) === items.length) {
        setChecked(not(checked, items));
      } else {
        setChecked(union(checked, items));
      }
    };
  
    const handleCheckedRight = () => {
      assignedList = [...assignmentMapper.assignedList];
      unassignedList = [...assignmentMapper.unassignedList];
  
      leftChecked.map((userInterface: UserInterface) => {
        var isAssigned = assignedList.find((x) => x.uiId === userInterface.id);
        if (!isAssigned) {
          unassignedList = unassignedList.filter(
            (x) => x.uiId !== userInterface.id
          );
          assignedList.push(toUserInterface(userInterface));
        }
      });
      setAssignmentMapper({ unassignedList, assignedList });
      setRight(right.concat(leftChecked));
      setLeft(not(left, leftChecked));
      var result: UserInterface[] = [];
      filter.forEach((e) => {
        if (leftChecked.indexOf(e) === -1) result.push(e);
      });
      setFilter(result);
      setChecked(not(checked, leftChecked));
    };
  
    const handleCheckedLeft = () => {
      unassignedList = [...assignmentMapper.unassignedList];
      assignedList = [...assignmentMapper.assignedList];
      rightChecked.map((userInterface: UserInterface) => {
        var isNotAssigned = unassignedList.find(
          (x) => x.uiId == userInterface.id
        );
        if (!isNotAssigned) {
          assignedList = assignedList.filter((x) => x.uiId !== userInterface.id);
          unassignedList.push(toUserInterface(userInterface));
        }
      });
      setAssignmentMapper({ assignedList, unassignedList });
      setLeft(left.concat(rightChecked));
      var result: UserInterface[] = [];
      filter.forEach((e:any) => {
        if (rightChecked.indexOf(e) === -1) result.push(e);
      });
      setFilter(result);
      setRight(not(right, rightChecked));
      setChecked(not(checked, rightChecked));
    };
  
    const handleSearch = (key: string) => {
      var temp:any = filter?.filter(
        (e: any) => e.name?.toLowerCase().search(key?.toLowerCase()) >= 0
      );
      setLeft(temp);
    };
  
    const customList = (title: React.ReactNode, items: readonly any[]) => (
      <Card sx={{mt:'25px'}}>
        <div className="d-flex justify-content-between">
          <CardHeader
            sx={{ px: 2, py: 2 }}
            avatar={
              <>
                <Checkbox
                  onClick={handleToggleAll(items)}
                  checked={
                    stringOfChecked(items) === items?.length && items?.length !== 0
                  }
                  indeterminate={
                    stringOfChecked(items) !== items?.length &&
                    stringOfChecked(items) !== 0
                  }
                  disabled={items.length === 0}
                  inputProps={{
                    "aria-label": "all items selected",
                  }}
                />
              </>
            }
            title={title}
            subheader={`${stringOfChecked(items)}/${items.length} selected`}
          />
          {title !== "User Story lists" ? (
            <div>
              <Button
                variant="contained"
                className="m-2"
                onClick={SaveUserStoryUI}
              >
                Save
              </Button>
            </div>
          ) : (
            <div>
              <TextField
                variant="outlined"
                label="Search"
                className="m-2"
                onChange={(event: any) => {
                  handleSearch(event.target.value);
                }}
              />
            </div>
          )}
        </div>
        <Divider />
        <List
          sx={{
            width: 700,
            height: 300,
            bgcolor: "background.paper",
            overflow: "auto",
          }}
          dense
          component="div"
          role="list"
        >
          {items.map((value: any, index: number) => {
            const labelId = `transfer-list-all-item-${value}-label`;
            return (
              <ListItem key={index} role="listitem" onClick={handleToggle(value)}>
                <ListItemIcon>
                  <Checkbox
                    checked={checked.indexOf(value) !== -1}
                    tabIndex={-1}
                    disableRipple
                    inputProps={{
                      "aria-labelledby": labelId,
                    }}
                  />
                </ListItemIcon>
                <ListItemText id={labelId} primary={`${value.name}`} />
              </ListItem>
            );
          })}
        </List>
      </Card>
    );
  
    return (
      <div>
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link to="/Customer">
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link to="/Customer/RequirementMatrix" state={location.state}>
            <Typography sx={{ fontWeight: "bold" }}>Requirement Matrix</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>Assign User Story</Typography>
        </Breadcrumbs>
        <div className="d-flex justify-content-center">
          <Typography color="text.primary" className="mx-3 fs-4 ">
            Project Name: <b className="fw-bold">{location.state?.projectName}</b>
          </Typography>
          <Typography color="text.primary" className="mx-3 fs-4">
          Requirement Source:{" "}
            <b className="fw-bold">{location.state?.RequirementSource}</b>
          </Typography>
        </div>
        <Grid
          container
          spacing={2}
          justifyContent="space-between"
          alignItems="center" 
          className="container m-3 mx-auto d-flex mt-5 mb-5"
        >
          <Grid xs={5}>
          <Grid item >{customList("User Story lists", left)}</Grid>
          </Grid>
          <Grid xs={2}>
          <Grid item>
            <Grid container direction="column" alignItems="center">
              <Button
                sx={{ my: 0.5 }}
                variant="outlined"
                size="small"
                className="mx-2"
                onClick={handleCheckedRight}
                disabled={leftChecked.length === 0}
                aria-label="move selected right"
              >
                <KeyboardDoubleArrowRightIcon />
              </Button>
              <Button
                sx={{ my: 0.5 }}
                variant="outlined"
                size="small"
                onClick={handleCheckedLeft}
                disabled={rightChecked.length === 0}
                aria-label="move selected left"
              >
                <KeyboardDoubleArrowLeftIcon />
              </Button>
            </Grid>
          </Grid>
          </Grid>
          <Grid xs={5}>
          <Grid item>{customList("Assigned User Story", right)}</Grid>
          </Grid>
        </Grid>
        <BackDrop open={loading} />
      </div>
    );
  };
  