import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ton, Grid } from "@mui/material";
import { Link, useLocation } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import Select from "react-select";
import { RectangleCard, SquareCard } from "../../../CommonComponents/Card";
import { COMPLETED, PENDING } from "../../../Constants/Common/Common";
import {
  FILTER,
  TEAMREPORT,
  TEAM_DETAILS,
  TODAYSTASK_LIST,
} from "../../../Constants/TeamReport/TeamReport";
import DataTable from "react-data-table-component";
import { useEffect, useRef, useState } from "react";
import BackDrop from "../../../CommonComponents/BackDrop";
import { Get } from "../../../Services/Axios";
import {
  TeamDetailsVM,
  Team<PERSON>ilter,
  TeamProjectVM,
  TeamReportVM,
} from "../../../Models/Team/Team";
import { ConvertDate } from "../../../Utilities/Utils";
import { useContextProvider } from "../../../CommonComponents/Context";

function ActualHour(total: any, num: any): number {
  return total + num.estTime;
}

function TeamReportList() {
  const {role} = useContextProvider();
  const location = useLocation();
  const { data }: any = location.state;
  const [loading, setLoading] = useState<boolean>(false);
  const [filter, setFilter] = useState<TeamFilter>(FILTER);
  const [TeamReport, SetTeamReport] = useState<TeamReportVM>(TEAMREPORT);
  const projectNameRef = useRef<any>();
  const employeeNameRef = useRef<any>();
  const [filterRows, setfilterRows] = useState<any>([]);

  async function GetData() {
    setLoading(true);
    const response: any = await Get(
      `/app/Team/GetTeamReportDetails?TeamId=${location.state?.data?.teamId}`
    );
    SetTeamReport(response?.data ?? TEAMREPORT);
    setfilterRows(response?.data?.teamdetails);
    setLoading(false);
  }

  useEffect(() => {
    GetData();
  }, []);

  function ApplyFilter() {
    let temp = TeamReport.teamdetails;

    if (filter.projectName.length > 0) {
      temp = temp.filter((x: TeamDetailsVM) => {
        if (x.projectName != null)
          return (
            x.projectName
              .toLowerCase()
              .search(filter.projectName.toLowerCase()) >= 0
          );
      });
      setfilterRows(temp);
    }

    if (filter.employeeName.length > 0) {
      temp = temp.filter((x: TeamDetailsVM) => {
        if (x.employeeName != null)
          return (
            x.employeeName
              .toLowerCase()
              .search(filter.employeeName.toLowerCase()) >= 0
          );
      });
    }
    setfilterRows(temp);
  }

  function reset() {
    if (projectNameRef.current) projectNameRef.current.clearValue();
    if (employeeNameRef.current) employeeNameRef.current.clearValue();
    setFilter(FILTER);
  }

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Link color="inherit" to={`/${role}/Team`} state={{ ...location.state }}>
          <Typography sx={{ fontWeight: "bold" }}>Team</Typography>
        </Link>

        <Typography sx={{ fontWeight: "bold" }}>Team Report</Typography>
      </Breadcrumbs>

      <div
        className="d-flex justify-content-between mx-5 mt-3 mx-auto flex-wrap"
        style={{ width: "90%" }}
      >
        <div className="m-2 p-3">
          <span>
            <h5>
              Team Name: <b>{location.state?.data?.teamName}</b>
            </h5>
          </span>
        </div>

        <div className="m2 p-3">
          <h5 className="p-2">
            Team Start Date: <b>{ConvertDate(data.startDate)}</b>
          </h5>
          <h5 className="p-2">
            Team End Date: <b>{ConvertDate(data.endDate)}</b>
          </h5>
        </div>
      </div>

      <div className="well mx-auto mt-4" style={{ width: "90%" }}>
        <div className="row">
          <div className="col-sm-5 p-2 m-auto">
            <div className="form-group col-12">
              <label>Project Name</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="ProjectName"
                ref={projectNameRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setFilter((prevState: TeamFilter) => {
                    return {
                      ...prevState,
                      projectName: code ? code.value : null,
                    };
                  });
                }}
                options={TeamReport.teamtotalprojects.map(
                  (x: TeamProjectVM) => ({
                    value: x?.project?.name,
                    label: x?.project?.name,
                  })
                )}
                placeholder="Project Name"
                isSearchable={true}
              />
            </div>
          </div>
          <div className="col-sm-5 col-sm-6 p-2 m-auto">
            <div className="form-group col-12">
              <label>Team Member Name</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="TeamMemberName"
                ref={employeeNameRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setFilter((prevState: TeamFilter) => {
                    return {
                      ...prevState,
                      employeeName: code ? code.value : null,
                    };
                  });
                }}
                options={TeamReport.teamdetails
                  .filter(
                    (obj: any, index: any, self: any) =>
                      index ===
                      self.findIndex(
                        (t: any) => t.employeeName === obj.employeeName
                      )
                  )
                  .map((x: TeamDetailsVM) => ({
                    value: x?.employeeName,
                    label: x?.employeeName?.replace(/[^A-Za-z ]/g, ""),
                  }))}
                placeholder="Team Member Name"
                isSearchable={true}
              />
            </div>
          </div>

          <div className="container">
            <div className="row justify-content-end">
              <div className="col-auto">
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => ApplyFilter()}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => reset()}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        className="d-flex p-2 m-auto "
        style={{
          backgroundColor: "#e1f4f5",
          width: "90%",
          flexDirection: "column",
        }}
      >
        <div className="p-3 m-2">
          <div className="p-3 m-2 d-flex flex-wrap"style={{justifyContent:"space-evenly"}} >
            <SquareCard
              classNames="teamReportCard mt-2"
              text={"Total Projects"}
              count={TeamReport?.teamtotalprojects.length}
            />
            <SquareCard
              classNames="teamReportCard mt-2"
              text={"Total Objectives"}
              count={TeamReport?.teamtotalobjectives.length}
            />
            <SquareCard
              classNames="teamReportCard mt-2"
              text={"Team Total Task In Hrs"}
              count={TeamReport?.teamtotaltasks.length}
            />
            <SquareCard
              classNames="teamReportCard mt-2"
              text={"Team Assigned Hrs"}
              count={TeamReport?.teamtotaltasks.reduce(ActualHour, 0)}
            />
            <SquareCard
              classNames="teamReportCard mt-2"
              text={"Team Members"}
              count={TeamReport?.teamMemebers.length}
            />
            {/* <SquareCard text={"Team Unassigned Hrs"} /> */}
          </div>


          <Grid container >
           <Grid md={6} xs={6} lg={3} className="m-auto">
                <div className="col-4 mt-2 mb-2 ">
                  <RectangleCard classNames="m-auto" text={COMPLETED} />
                </div>

                <div className="col-4 mb-2">
                  <RectangleCard
                  classNames="m-auto"
                    count={
                      TeamReport.teamtotalprojects.filter(
                        (x: TeamProjectVM) =>
                          parseFloat(`${x.project?.percentage ?? 0} `) === 100
                      ).length
                    }
                  />
                </div>

                <div className="col-4 mb-2 ">
                  <RectangleCard
                  classNames="m-auto"
                    count={
                      TeamReport.teamtotalobjectives.filter(
                        (x: any) => x.percentage === 100
                      ).length
                    }
                  />
                </div>
              </Grid>



              <Grid md={6} xs={6} lg={3} className="m-auto">

                              <div className=" col-4 mb-2 mt-2 ">
                                <RectangleCard classNames="" text={PENDING} />
                              </div>

                              <div className=" col-4 mb-2 ">
                                <RectangleCard
                                classNames=""
                                  count={
                                    TeamReport.teamtotalprojects.filter(
                                      (x: TeamProjectVM) =>
                                        parseFloat(`${x.project?.percentage ?? 0} `) < 100
                                    ).length
                                  }
                                />
                              </div>
                              
                              <div className="col-4 mb-2 ">
                                <RectangleCard
                                classNames=""
                                  count={
                                    TeamReport.teamtotalobjectives.filter(
                                      (x: any) => x.percentage < 100
                                    ).length
                                  }
                                />
                              </div>

              </Grid>
          </Grid>


        </div>
      </div>
      <div
        className="m-auto mt-5 p-3 text-light d-flex align-items-center"
        style={{ backgroundColor: "darkviolet", width: "90%" }}
      >
        <h5 className="m-1 fw-bold">List Of Team Details</h5>
      </div>
      <div className="m-auto mt-2" style={{ width: "90%" }}>
        <DataTable
          customStyles={{
            table: {
              style: {
                height: "80vh",
                border: "1px solid rgba(0,0,0,0.1)",
                overflowY: "auto",
              },
            },

            headRow: {
              style: {
                background: "#1e97e8",
                fontSize: "16px",
                color: "white",
                fontFamily: "inherit",
              },
            },
          }}
          columns={TEAM_DETAILS}
          data={filterRows}
        />
      </div>

      <div
        className="m-auto mt-5 p-3 text-light d-flex align-items-center"
        style={{ backgroundColor: "darkviolet", width: "90%" }}
      >
        <h5 className="m-1 fw-bold">List Of Today's Tasks</h5>
      </div>
      <div className="m-auto mt-2" style={{ width: "90%" }}>
        <DataTable
          customStyles={{
            table: {
              style: {
                height: "80vh",
                border: "1px solid rgba(0,0,0,0.1)",
                overflowY: "auto",
              },
            },

            headRow: {
              style: {
                background: "#1e97e8",
                fontSize: "16px",
                color: "white",
                fontFamily: "inherit",
              },
            },
          }}
          columns={TODAYSTASK_LIST}
          data={TeamReport?.todaysTasks}
        />
      </div>
      <BackDrop open={loading} />
    </>
  );
}

export default TeamReportList;
