import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  Tooltip,
  TextField,
  FormControl,
  Autocomplete,
} from "@mui/material";
import { Link, useLocation } from "react-router-dom";
import Select from "react-select";
import DataTable from "react-data-table-component";
import SearchIcon from "@mui/icons-material/Search";
import AddIcon from "@mui/icons-material/Add";
import ChecklistIcon from "@mui/icons-material/Checklist";
import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import DownloadIcon from "@mui/icons-material/Download";
import Box from "@mui/material/Box";
import { useEffect, useRef, useState } from "react";
import { AddUserInterface } from "./AddUserInterface";
import { EditUserInterface } from "./EditUserInterface";
import { ViewUserInterface } from "./VeiwUserInterface";
import RefreshIcon from "@mui/icons-material/Refresh";
import { UserInterface } from "../../../../Models/Project/UserInterface";
import { ConvertDate } from "../../../../Utilities/Utils";
import { Get, Post } from "../../../../Services/Axios";
import PendingActionsIcon from "@mui/icons-material/PendingActions";
import { DownloadUserInterfaceList } from "../../../../Services/ProjectService";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Modal from "@mui/material/Modal";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import RuleIcon from "@mui/icons-material/Rule";
import StepLabel from "@mui/material/StepLabel";
import StepConnector, {
  stepConnectorClasses,
} from "@mui/material/StepConnector";
import { StepIconProps } from "@mui/material/StepIcon";
import BackDrop from "../../../../CommonComponents/BackDrop";
import { useContextProvider } from "../../../../CommonComponents/Context";
import { ADMIN } from "../../../../Constants/Common/Roles";
import { USER_INTERFACE } from "../../../../Constants/UserInterface/UserInterface";
import { COMPLETED, PENDING } from "../../../../Constants/Common/Common";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import DeleteIcon from "@mui/icons-material/Delete";
import Swal from "sweetalert2";
import { Project } from "../../../../Models/Project/Project";
import { useQuery } from "react-query";

const style = {
  position: "absolute" as "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  height: 700,
  width: 600,
  bgcolor: "White",
  border: "2px solid #000",
  boxShadow: 24,
  p: 4,
};

const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 22,
    left: 125,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage:
        "linear-gradient( 95deg,rgb(242,113,33) 0%,rgb(233,64,87) 50%,rgb(138,35,135) 100%)",
      Width: 10,
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage:
        "linear-gradient( 95deg,rgb(242,113,33) 0%,rgb(233,64,87) 50%,rgb(138,35,135) 100%)",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 65,
    width: 10,
    border: 0,
    backgroundColor:
      theme.palette.mode === "dark" ? theme.palette.grey[800] : "#eaeaf0",
    borderRadius: 1,
  },
}));

const ColorlibStepIconRoot = styled("div")<{
  ownerState: { completed?: boolean; active?: boolean };
}>(({ theme, ownerState }) => ({
  backgroundColor:
    theme.palette.mode === "dark" ? theme.palette.grey[700] : "#ccc",
  zIndex: 1,
  color: "#fff",
  width: 50,
  height: 50,
  display: "flex",
  borderRadius: "50%",
  justifyContent: "center",
  alignItems: "center",
  ...(ownerState.active && {
    backgroundImage:
      "linear-gradient( 136deg, rgb(242,113,33) 0%, rgb(233,64,87) 50%, rgb(138,35,135) 100%)",
    boxShadow: "0 4px 10px 0 rgba(0,0,0,.25)",
  }),
  ...(ownerState.completed && {
    backgroundImage:
      "linear-gradient( 136deg, rgb(242,113,33) 0%, rgb(233,64,87) 50%, rgb(138,35,135) 100%)",
  }),
}));

function ColorlibStepIcon(props: StepIconProps) {
  const { active, completed, className } = props;

  return (
    <ColorlibStepIconRoot
      ownerState={{ completed, active }}
      className={className}
    ></ColorlibStepIconRoot>
  );
}

const steps = ["Business Analysis", "Development", "QA", "UAT", "Production"];

export const UserInterfacelist = () => {
  const [filterRows, setfilterRows] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [reload, SetReload] = useState<boolean>(true);
  const [open, setOpen] = useState(false);
  const handleClose = () => setOpen(false);
  const [rows, setRows] = useState<any>([]);
  const location = useLocation();
  const { projectReportRoute, status } = location.state;
  const uiNameRef = useRef<HTMLInputElement>(null);
  const statuiRef = useRef<any>();
  const percentageRef = useRef<HTMLInputElement>(null);
  const actStartDateRef = useRef<HTMLInputElement>(null);
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const descriptionRef = useRef<HTMLInputElement>(null);
  const complexityRef = useRef<any>();
  const userInterfaceRef = useRef<any>();
  const [filter, setfilter] = useState<UserInterface>(USER_INTERFACE);
  const [userInterfacedata, setUserInterfacedata] = useState<any>();
  const { role } = useContextProvider();
  const [UserInterfaceList, setUserInterfaceList] = useState<any>([]);
  const [UserInterfaceView, setUserInterfaceView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });
  const [projects, setProjects] = useState([]);
  const [Project, setProject] = useState<any>({
    ProjectId: location?.state?.projectId,
    ProjectName: location?.state?.projectName,
  });

  var activeStep = 0;

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      type: "Date",
      width: "11rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: UserInterface) => {
        return (
          <>
            <Tooltip
              title="View"
              className="mx-2"
              onClick={() => {
                setUserInterfaceView({ view: true });
                setUserInterfacedata(row);
              }}
            >
              <VisibilityIcon className="fs-4 text-info" />
            </Tooltip>
            {role === ADMIN && (
              <>
                <Tooltip
                  title="Edit"
                  className="mx-2"
                  onClick={() => {
                    setUserInterfaceView({ edit: true });
                    setUserInterfacedata(row);
                  }}
                >
                  <EditIcon className="fs-4 text-warning" />
                </Tooltip>
                <Tooltip title="Delete" className="mx-2">
                  <DeleteIcon
                    className="fs-4 text-danger"
                    onClick={() => {
                      row.isActive = false;
                      row.projectObjectiveId = 0;
                      row.documents = null;
                      deleteUserInterface(row);
                    }}
                  />
                </Tooltip>
              </>
            )}
            {/* <Tooltip title="Stages">
              <StackedBarChartIcon
                onClick={handleOpen}
                className="fs-4 text-primary"
              />
            </Tooltip> */}
          </>
        );
      },
    },
    {
      field: "name",
      name: "Name",
      width: 290,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: any) => (
        <Tooltip title={row.name}>
          <p className="tableStyle">{row.name}</p>
        </Tooltip>
      ),
    },
    {
      field: "description",
      name: "Description",
      width: "20rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: any) => (
        <Tooltip title={row.description}>
          <p className="tableStyle">{row.description}</p>
        </Tooltip>
      ),
    },
    {
      field: "status",
      name: "Status",
      width: "10rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.status}</p>,
    },
    {
      field: "percentage",
      name: "Percentage",
      type: "number",
      width: "10rem",
      align: "center",
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      right: true,
      selector: (row: any) => <p className="tableStyle">{row.percentage}</p>,
    },
    {
      field: "complexity",
      name: "Complexity",
      width: 150,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.complexity}</p>,
    },
    {
      field: "uiCategory",
      name: "UI Category",
      width: "15rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.uiCategory}</p>,
    },
    {
      field: "startDate",
      name: "Start Date",
      type: "Date",
      width: 150,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: any) => {
        const result = ConvertDate(row.startDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
    {
      field: "endDate",
      name: "End Date",
      type: "Date",
      width: 150,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: any) => {
        const result = ConvertDate(row.endDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
  ];

  async function fetchData() {
    debugger;
    console.log(Project);
    setLoading(true);
    let UserInterface: any = await Get(
      `app/Project/GetUserInterfaceList?projectId=${
        Project?.ProjectName?.length > 0
          ? Project.ProjectId
          : location.state.projectId
      }`
    );
    setRows(UserInterface?.data);

    if (projectReportRoute) {
      if (status === COMPLETED) {
        setfilterRows(
          UserInterface?.data?.filter(
            (x: UserInterface) => x.percentage === 100
          ) || []
        );
      } else if (status === PENDING) {
        setfilterRows(
          UserInterface?.data?.filter(
            (x: UserInterface) => parseInt(`${x.percentage}`) < 100
          ) || []
        );
      } else {
        setfilterRows(UserInterface?.data || []);
      }
    } else {
      setfilterRows(UserInterface?.data || []);
    }

    const response: any = await Get("app/Project/GetProjectList");
    setProjects(response.data || []);
    setLoading(false);
    setUserInterfaceList(UserInterface?.data || []);
  }

  const { data, refetch } = useQuery("AddUserStory", () => {
    var objective: any = Get(
      `app/Project/GetProjectObjective?ProjectId=${
        Project.ProjectId?.length > 0
          ? Project.ProjectId
          : location.state.projectId
      }`
    );
    return objective;
  });

  useEffect(() => {
    fetchData();
  }, [reload]);

  const handlePendingClick = (statusfilter: string) => {
    const filteredRow = rows.filter(
      (e: any) => e.status.toLowerCase() === statusfilter.toLowerCase()
    );
    setfilterRows(filteredRow);
  };

  const handleClickOpen = () => {
    setUserInterfaceView({ add: true });
  };

  const GetProjectNames = projects
    .filter((e: any) => e?.id === location?.state?.projectId)
    .map((project: any) => project.name);

  async function deleteUserInterface(userInterface: UserInterface) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {

        if (userInterface.projectObjectiveId !== null && userInterface.projectObjectiveId !== undefined) {
          userInterface.projectObjectiveId = userInterface.projectObjectiveId.toString();
        }
        const { error }: any = await Post(
          "app/Project/UpdateUserInterface",
          userInterface
        );
        var option: AlertOption;
        if (error) {
          option = {
            title: "Error",
            text: "Error Occured While Deleting!",
            icon: "error",
          };
        } else {
          option = {
            title: "Success",
            text: "User Interface has been deleted!",
            icon: "success",
          };
        }
        Swal.fire({
          ...option,
          confirmButtonColor: "#3085d6",
        });
        SetReload((prev: boolean) => !prev);
      }
    });
  }

  function ApplyFilter() {
    let temp: any = rows;

    if (filter.startDate != null) {
      if (filter.endDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].startDate.slice(0, 10) >= filter.startDate &&
          rows[i].endDate.slice(0, 10) <= filter.endDate
        ) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.name != null) {
      temp = temp.filter((e: any) => {
        return e.name.toLowerCase().search(filter.name?.toLowerCase()) >= 0;
      });
      setfilterRows(temp);
    }

    if (filter.status != null) {
      temp = temp.filter((e: any) => {
        return e.status.toLowerCase() === filter.status?.toLowerCase();
      });
      setfilterRows(temp);
    }

    if (filter.percentage != null) {
      temp = temp.filter((e: any) => {
        return e.percentage === Number(filter.percentage);
      });
      setfilterRows(temp);
    }

    if (filter.complexity != null) {
      temp = temp.filter((e: any) => {
        return e.complexity.toLowerCase() === filter.complexity?.toLowerCase();
      });
      setfilterRows(temp);
    }
  }

  function reset() {
    setfilter(USER_INTERFACE);
    if (uiNameRef.current) uiNameRef.current.value = "";
    if (statuiRef.current) statuiRef.current.clearValue();
    if (percentageRef.current) percentageRef.current.value = "";
    if (actStartDateRef.current) actStartDateRef.current.value = "";
    if (actEndDateRef.current) actEndDateRef.current.value = "";
    if (descriptionRef.current) descriptionRef.current.value = "";
    if (complexityRef.current) complexityRef.current.clearValue();
    if (userInterfaceRef.current) userInterfaceRef.current.clearValue();
    setfilterRows(rows);
  }

  return (
    <div>
      {!projectReportRoute && (
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link to={`/${role}/Project`}>
            <Typography sx={{ fontWeight: "bold" }}>Project</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>User Interface</Typography>
        </Breadcrumbs>
      )}

      {projectReportRoute && (
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link color="inherit" to={`/${role}/Project`}>
            <Typography sx={{ fontWeight: "bold" }}>Projects</Typography>
          </Link>
          <Link
            color="inherit"
            to={`/${role}/ProjectQuadrant`}
            state={{ ...location.state }}
          >
            <Typography sx={{ fontWeight: "bold" }}>
              Project Quadrant
            </Typography>
          </Link>
          <Link
            color="inherit"
            to={`/${role}/ProjectQuadrant/ProjectReport`}
            state={{ ...location.state }}
          >
            <Typography sx={{ fontWeight: "bold" }}>Project Report</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>User Interface</Typography>
        </Breadcrumbs>
      )}
      <div className="row">
        <div className="col-4">
          <FormControl
            className="col-md-6 "
            sx={{ ml: "55px", marginTop: "20px" }}
          >
            <Autocomplete
              options={projects
                .sort((a: Project, b: Project) =>
                  a?.name!.localeCompare(b?.name!)
                )
                .map((project: Project) => ({
                  label: project.name,
                  id: project.id,
                }))}
              onChange={(e: any, value: any) => {
                if (value)
                  setProject({
                    ProjectId: value?.id,
                    ProjectName: value?.label,
                  });
                SetReload((prev) => !prev);
                return e;
              }}
              renderInput={(params: any) => (
                <TextField
                  {...params}
                  label="Select Project"
                  variant="filled"
                  required
                />
              )}
            />
          </FormControl>
        </div>
        <div className="col-8">
          <Typography
            align="center"
            className="fw-bolder fs-3"
            sx={{ float: "left", marginTop: "20px", ml: "45px" }}
          >
            Project Name:{" "}
            {Project?.ProjectName?.length > 0
              ? Project.ProjectName
              : GetProjectNames}
          </Typography>
        </div>
      </div>
      <div className="row">
      <div
          className="attce d-flex mt-3 m-auto flex-wrap "
          style={{ width: "80%", justifyContent: 'space-evenly' }}
        >
           <div className="shadow d-flex align-items-center mt-2 px-4" onClick={() => reset()}>
            <div className="container">
              <div className="row justify-content-center">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#1f59c4",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-12"
                >
                  Total
                </div>
                <div className="col-md-4 col-sm-4 col-6 d-flex justify-content-center pt-2">
                  <FormatListBulletedIcon color="primary" sx={{ fontSize: "255%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-6 align-items-center px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                  }}
                >
                  {rows?.length}
                </div>
              </div>
            </div>
          </div>
          {/* <div className="shadow m-2 mt-3 pt-2" onClick={() => reset()}>
            <div className="container">
              <div className="row">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#1f59c4",
                    paddingLeft: "35px",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-md-12"
                >
                  Total
                </div>
              </div>
              <div className="row">
                <div className="col-md-4 col-sm-4 col-xs-6 col-5 pt-2">
                  <FormatListBulletedIcon
                    color="primary"
                    sx={{ fontSize: "255%" }}
                  />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-xs-6 col-7 px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  {rows?.length || 0}
                </div>
              </div>
            </div>
          </div> */}
           <div className="shadow d-flex align-items-center mt-2" onClick={() => handlePendingClick("Completed")}>
            <div className="container">
              <div className="row justify-content-center">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#48e022",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-12"
                >
                  Completed
                </div>
                <div className="col-md-4 col-sm-4 col-6 d-flex justify-content-center pt-2">
                  <ChecklistIcon color="success" sx={{ fontSize: "225%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-6 align-items-center px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                  }}
                >
                  {rows.filter((e: any) => e.status === "Completed").length ||
                    "0"}
                </div>
              </div>
            </div>
          </div>
        {/* <div className="col-md-2 col-sm-4 col-xs-12 ">
          <div
            className="shadow  m-2 mt-3 pt-2 "
            onClick={() => handlePendingClick("Completed")}
          >
            <div className="container">
              <div className="row">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#48e022",
                    paddingLeft: "35px",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-md-12"
                >
                  Completed
                </div>
              </div>
              <div className="row">
                <div className="col-md-4 col-sm-4 col-xs-6 col-5 pt-2">
                  <ChecklistIcon color="success" sx={{ fontSize: "225%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-xs-6 col-7 px-4"
                  style={{
                    color: "#70d40d",
                    fontSize: "255%",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  {rows?.filter((e: any) => e?.status === "Completed")
                    ?.length || "0"}
                </div>
              </div>
            </div>
          </div>
        </div> */}
          <div className="shadow d-flex align-items-center mt-2" onClick={() => handlePendingClick("In Progress")}>
            <div className="container">
              <div className="row justify-content-center">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#d16e1d",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-12"
                >
                  In Progress
                </div>
                <div className="col-md-4 col-sm-4 col-6 d-flex justify-content-center pt-2">
                  <RuleIcon color="warning" sx={{ fontSize: "225%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-6 align-items-center px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                  }}
                >
                  {rows.filter((e: any) => e.status === "In Progress")
                    .length || "0"}
                </div>
              </div>
            </div>
          </div>
        {/* <div className="col-md-2 col-sm-4 col-xs-12 ">
          <div
            className="shadow  m-2 mt-3 pt-2 "
            onClick={() => handlePendingClick("In progress")}
          >
            <div className="container">
              <div className="row">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#d16e1d",
                    paddingLeft: "35px",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-md-12"
                >
                  In-Progress
                </div>
                <div className="row">
                  <div className="col-md-4 col-sm-4 col-xs-6 col-5 pt-2">
                    <RuleIcon color="warning" sx={{ fontSize: "225%" }} />
                  </div>
                  <div
                    className="col-md-8 col-sm-8 col-xs-6 col-7 px-4"
                    style={{
                      color: "#824908",
                      fontSize: "250%",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    {rows?.filter((e: any) => e?.status === "In Progress")
                      ?.length || "0"}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div> */}
         <div className="shadow d-flex align-items-center mt-2 px-4" onClick={() => handlePendingClick("pending")}>
            <div className="container">
              <div className="row justify-content-center">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#1f59c4",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-12"
                >
                  Pending
                </div>
                <div className="col-md-4 col-sm-4 col-6 d-flex justify-content-center pt-2">
                  <PendingActionsIcon color="error" sx={{ fontSize: "225%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-6 align-items-center px-3"
                  style={{
                    color: "#072f70",
                    fontSize: "250%",
                  }}
                >
                  {rows.filter((e: any) => e.status === "Pending").length ||
                    "0"}
                </div>
              </div>
            </div>
          </div>
        {/* <div className="col-md-2 col-sm-4 col-xs-12 ">
          <div
            className="shadow  m-2 mt-3 pt-2 "
            onClick={() => handlePendingClick("pending")}
          >
            <div className="container">
              <div className="row">
                <div
                  style={{
                    fontSize: "18px",
                    lineHeight: "28px",
                    color: "#1f59c4",
                    paddingLeft: "35px",
                    fontWeight: "900",
                    marginTop: "3%",
                    textAlign: "center",
                  }}
                  className="col-md-12"
                >
                  Pending
                </div>
              </div>
              <div className="row">
                <div className="col-md-4 col-sm-4 col-xs-6 col-5 pt-2">
                  <PendingActionsIcon color="error" sx={{ fontSize: "225%" }} />
                </div>
                <div
                  className="col-md-8 col-sm-8 col-xs-6 col-7 px-3"
                  style={{
                    color: "#7d0d09",
                    fontSize: "250%",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  {rows?.filter((e: any) => e?.status === "Pending")?.length ||
                    "0"}
                </div>
              </div>
            </div>
          </div>
        </div> */}
        </div>
      </div>
      {/* <Grid container> */}
      {/* <Grid xs={9.5}> */}
      <div className="well mx-5 mt-4">
        <div className="row">
          <div className="col-sm-2">
            <div className="form-group">
              <label>User Interface</label>
              <Select
                aria-label="Floating label select example"
                name="UserInterface"
                ref={userInterfaceRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      name: code ? code.value : null,
                    };
                  });
                }}
                options={UserInterfaceList?.map((opt: any) => ({
                  label: opt?.name,
                  value: opt?.name,
                }))}
                placeholder="User Interface"
                isSearchable={true}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    zIndex: 999,
                  }),
                  menu: (provided) => ({
                    ...provided,
                    maxHeight: 300,
                    zIndex: 999, // Set your desired max height here
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                aria-label="Floating label select example"
                name="status"
                ref={statuiRef}
                className="select-dropdowns mt-1 col"
                onChange={(selectedOption: any) => {
                  setfilter((prevState) => ({
                    ...prevState,
                    status: selectedOption ? selectedOption.value : null,
                  }));
                }}
                options={[
                  {
                    label: "Completed",
                    value: "Completed",
                  },
                  {
                    label: "In Progress",
                    value: "In Progress",
                  },
                  {
                    label: "Pending",
                    value: "Pending",
                  },
                ]}
                placeholder="Status"
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Complexity</label>
              <Select
                aria-label="Floating label select example"
                name="complexity"
                ref={complexityRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      complexity: code ? code.value : null,
                    };
                  });
                }}
                options={[
                  {
                    label: "High",
                    value: "High",
                  },
                  {
                    label: "Low",
                    value: "Low",
                  },
                  {
                    label: "Medium",
                    value: "Medium",
                  },
                ]}
                placeholder="Complexity"
                isSearchable={true}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Percentage</label>
              <input
                id="percentage"
                placeholder="Percentage"
                className="m-1 form-control col"
                ref={percentageRef}
                onChange={(e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  setfilter((prevState) => ({
                    ...prevState,
                    percentage: parseInt(e.target.value),
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-md-2">
            <div className="form-group">
              <label className="mx-1">Start Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      startDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="start-date"
                placeholder="Start Date"
                ref={actStartDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="col-md-2">
            <div className="form-group">
              <label className="mx-1">End Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      endDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="end-date"
                placeholder="End Date"
                ref={actEndDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>

          <div className="container">
            <div className="row ">
              <div className="col">
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-1 mt-4"
                  sx={{ float: "right" }}
                  onClick={() => reset()}
                >
                  Reset
                </Button>
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-4 mt-4"
                  sx={{ float: "right" }}
                  onClick={() => ApplyFilter()}
                >
                  search
                </Button>
              </div>
            </div>
          </div>
        </div>
        {/* <div className="row" style={{ width: "90rem" }}>
           <div className="col-sm-2">
            
          </div>
          <div className="col-md-8" >
              <div  style={{ width: "20rem"}}>
               
            </div>
          </div>
        </div> */}
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center mt-4">
        <div className="col-11 col-s-4">
          <Grid>
            {role === ADMIN && (
              <Button
                variant="contained"
                className="mb-2 float-md-start"
                onClick={handleClickOpen}
                sx={{ ml: "3%" }}
              >
                Add User Interface
                <AddIcon className="mx-1" />
              </Button>
            )}
            <Button
              variant="contained"
              className="mb-2 float-md-end"
              onClick={() => {
                DownloadUserInterfaceList(filterRows);
              }}
              sx={{ mr: "3%" }}
            >
              Download
              <DownloadIcon className="mx-1" />
            </Button>
          </Grid>
        </div>
        <Grid item xs={12} sm={11} sx={{ mt: "4%" }}>
          <div className="col-3 col-s-3">
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                fixedHeader={true}
                responsive
                persistTableHead
                progressPending={loading}
                data={filterRows || []}
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },

                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
              />
              <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
              >
                <Box sx={style}>
                  <Typography
                    id="modal-modal-title"
                    variant="h6"
                    component="h2"
                    sx={{ textAlign: "center" }}
                  >
                    User Interface Stages
                  </Typography>
                  <Box sx={{ maxWidth: 900 }}>
                    <Stepper
                      activeStep={activeStep}
                      connector={<ColorlibConnector />}
                      orientation={steps.length < 2 ? "horizontal" : "vertical"}
                    >
                      {steps.map((label) => (
                        <Step key={label}>
                          <StepLabel StepIconComponent={ColorlibStepIcon}>
                            {label}
                          </StepLabel>
                        </Step>
                      ))}
                    </Stepper>
                    {activeStep === steps.length && (
                      <Paper square elevation={4} sx={{ p: 3 }}>
                        <Typography>
                          All steps completed - you&apos;re finished
                        </Typography>
                      </Paper>
                    )}
                  </Box>
                </Box>
              </Modal>
            </Box>
          </div>
        </Grid>
      </div>
      <AddUserInterface
        openDialog={UserInterfaceView}
        data1={data?.length > 0 ? data : []}
        setOpenDialog={setUserInterfaceView}
        SetReload={SetReload}
        projectId={Project.ProjectId}
        refetch={refetch}
      />
      <EditUserInterface
        openDialog={UserInterfaceView}
        setOpenDialog={setUserInterfaceView}
        Data={userInterfacedata}
        data1={data ?? []}
        projectId={location.state.projectId}
        SetReload={SetReload}
      />
      <ViewUserInterface
        openDialog={UserInterfaceView}
        setOpenDialog={setUserInterfaceView}
        Data={userInterfacedata}
        data1={data ?? []}
      />
      <BackDrop open={loading} />
    </div>
  );
};
