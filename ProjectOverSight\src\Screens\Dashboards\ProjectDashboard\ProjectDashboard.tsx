import "../../../StyleSheets/ProjectDashboard.css";
import { Breadcrumbs, Grid, Skeleton, Typography } from "@mui/material";
import { Link, useLocation } from "react-router-dom";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
// import HomeIcon from "@mui/icons-material/Home";
import React, { useEffect, useState } from "react";
import { Get, Post } from "../../../Services/Axios";
import BackDrop from "../../../CommonComponents/BackDrop";
import { Box } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
// import { ConvertDate } from "../../../Utilities/Utils";
import { PiFlagCheckeredFill } from "react-icons/pi";
// import {
//   CircularProgressbar,
//   CircularProgressbarWithChildren,
// } from "react-circular-progressbar";
// import { Card } from "rebass";
// import { TaskFilter } from "../../../Models/Task/TaskFilter";
import { FILTER } from "../../../Constants/Task/Task";
export const ProjectDashboard = () => {
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const skleton = Array.from(Array(12), () => 0);
  const [Data, setData] = useState<any>({});
  // const [percentage, setPercentage] = useState<number>(0);
  const [formattedDate, setFormattedDate] = useState<string>("");
  const [daysElapsed, setDaysElapsed] = useState<number>(0);
  const [overdueTasks, setoverdueTasks] = useState<any>([]);
  const filter = FILTER;
  const [completedTask, setcompletedTask] = useState<any>([]);

  async function fetchData() {
    setLoading(true);
    const userStoryList = Get(
      `app/Project/GetUserStoryList?projectId=${location.state.projectId}`
    );
    const projectDetails = Get(
      `app/Project/GetProjectById?Id=${location.state.projectId}`
    );
    const overduetasks: any = await Get(
      `app/Task/GetOverDueTask?projectId=${location.state.projectId}`
    );
    debugger;
    filter.percentage = 100;
    filter.projectName = [location.state.projectName];
    let taskList: any = await Post(
      "app/Task/GetTaskList?month=10&year=2023",
      filter
    );
    setcompletedTask(taskList?.data);

    setoverdueTasks(overduetasks?.data);
    projectDetails.then((response: any) => {
      const projectPercentage = response.data.percentage || 0;

      const startDate = response.data.startDate;
      const formattedStartDate = formatStartDate(startDate);
      setFormattedDate(formattedStartDate);

      const today = new Date();
      const start = new Date(startDate);
      const elapsedDays = Math.floor(
        (today.getTime() - start.getTime()) / (1000 * 3600 * 24)
      );
      setDaysElapsed(elapsedDays);
      return projectPercentage;
    });
    userStoryList.then((response: any) => {
      setData(response.data);
      setLoading(false);
    });
  }

  useEffect(() => {
    fetchData();
    return overdueTasks;
  }, []);

  function formatStartDate(startDate: string): string {
    const dateObject = new Date(startDate);
    const days = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const dayOfWeek = days[dateObject.getDay()];
    const month = months[dateObject.getMonth()];
    const day = dateObject.getDate();
    const year = dateObject.getFullYear();
    return `${dayOfWeek}, ${month} ${day} ${year}`;
  }

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">>">
        <Link color="inherit" to="/Admin">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Link color="inherit" to="/Admin/Project">
          <Typography sx={{ fontWeight: "bold" }}>Projects</Typography>
        </Link>
        <Typography color="slateblue">
          <TaskAltIcon sx={{ mr: 0.5 }} fontSize="inherit" /> Project Dashboard
        </Typography>
      </Breadcrumbs>
      <Grid container>
        <div
          className="container "
          style={{ background: "#f8f8fb", maxHeight: "100%" }}
        >
          <div className="row">
            <div className="col-lg-4 col-md-12">
              <div
                className="shadow bg-light overflow-scroll m-2 mt-3"
                style={{ maxHeight: 350 }}
              >
                <h5
                  className="text-center m-1 text-secondary"
                  style={{ fontWeight: 600 }}
                >
                  User Story
                </h5>
                {loading ? (
                  <div className="mx-1">
                    {skleton.map((e) => (
                      <Skeleton key={e} />
                    ))}
                  </div>
                ) : (
                  <div className=" m-3" style={{ height: "40vh" }}>
                    {Data?.length > 0 ? (
                      <table
                        className="table"
                        style={{
                          borderCollapse: "separate",
                          borderSpacing: "4px",
                        }}
                      >
                        <thead>
                          <tr>
                            <th
                              className="text-left"
                              style={{
                                backgroundColor: "#bdc8be",
                                border: "1px solid white",
                              }}
                            >
                              Name
                            </th>
                            <th
                              className="text-left"
                              style={{
                                backgroundColor: "#bdc8be",
                                border: "1px solid white",
                              }}
                            >
                              Stage Name
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {Data?.map((e: any) => {
                            return (
                              <tr key={e.id} style={{ marginBottom: "1px" }}>
                                <td
                                  className="text-left"
                                  style={{ padding: "10px" }}
                                >
                                  {e.name}
                                </td>
                                <td
                                  className="text-left"
                                  style={{ padding: "10px" }}
                                >
                                  {}
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    ) : (
                      <h6 className="mt-3 d-flex align-items-center justify-content-center">
                        <ErrorOutlineIcon className="mx-1" />
                        User Story Not Assigned
                      </h6>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div className="col-lg-4 col-md-12">
              <div className=" shadow mx-2 mt-3 bg-light d-flex flex-column align-items-center">
                <h5
                  className="m-1 text-secondary"
                  style={{ fontSize: "xx-large" }}
                >
                  Overall Progress
                </h5>
                <Box sx={{ width: "45%" }}>
                  <div className="w-100 progress-container">
                    {/* <CircularProgressbarWithChildren
                      value={percentage}
                      circleRatio={0.5}
                      styles={{
                        root: {
                          width: "70%",
                          marginLeft: "18px",
                          marginTop: "2%",
                          transform: "rotate(0.75turn)",
                        },
                        path: { stroke: "#26A65B" },
                        trail: { stroke: "#DCDCDC" },
                        background: { backgroundColor: "red" },
                      }}
                    >
                      <div
                        style={{
                          fontSize: "xx-large",
                          marginTop: "45%",
                          marginRight: "10%",
                        }}
                      >
                        <Typography sx={{ fontWeight: "bold" }}>
                          {percentage}%
                        </Typography>
                      </div>
                    </CircularProgressbarWithChildren> */}
                  </div>
                </Box>
              </div>
              <div className=" shadow mx-2 mt-3 bg-light d-flex flex-column align-items-center">
                <h5
                  className="m-1 text-secondary"
                  style={{ fontSize: "xx-large" }}
                >
                  Project Launch Date
                </h5>
                <Box sx={{ width: "45%", height: "15%" }}>
                  <div className="w-100 progress-container">
                    <div
                      className="col"
                      style={{
                        fontSize: "45px",
                        display: "inline-flex",
                        marginLeft: "-41%",
                      }}
                    >
                      <div style={{ transform: "rotate(-20deg)" }}>
                        <PiFlagCheckeredFill />
                      </div>

                      <div style={{ marginLeft: "1%" }}>
                        <h5 className="m-3">
                          <strong>{daysElapsed} Days</strong>
                          <br />
                          <strong>{formattedDate}</strong>
                        </h5>
                      </div>
                    </div>
                  </div>
                </Box>
              </div>
            </div>

            <div className="col-lg-4 col-md-12">
              <div
                className="shadow  overflow-scroll m-2 mt-3"
                style={{ maxHeight: 350, background: "#ffffff" }}
              >
                <h5
                  className="text-center m-2"
                  style={{
                    fontWeight: 600,
                    color: "#343a40",
                    float: "left",
                    paddingLeft: "5%",
                  }}
                >
                  Overdue Tasks
                </h5>

                <div className=" m-3" style={{ height: "45vh" }}>
                  <div
                    className="card"
                    style={{
                      width: "21rem",
                      height: "8rem",
                      marginBottom: "4%",
                    }}
                  >
                    <div className="card-body">
                      <div>
                        <div>
                          <h1
                            className="card-title"
                            style={{ fontSize: "20px" }}
                          >
                            Create a Project
                            <span
                              style={{
                                float: "right",
                                fontSize: "75%",
                                backgroundColor: "#daf4eb",
                                borderRadius: "5px",
                                color: "#34c39a",
                              }}
                            >
                              Completed
                            </span>
                          </h1>
                        </div>
                        <h1
                          className="card-subtitle text-body-secondary"
                          style={{ fontSize: "13px" }}
                        >
                          10 Dec, 2023
                        </h1>
                        <p style={{ fontSize: "18px", marginTop: "4%" }}>
                          Display the List Of Project
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    className="card"
                    style={{
                      width: "21rem",
                      height: "8rem",
                      marginBottom: "2%",
                    }}
                  >
                    <div className="card-body">
                      <div>
                        <div>
                          <h1
                            className="card-title"
                            style={{ fontSize: "20px" }}
                          >
                            Create a Project
                            <span
                              style={{
                                float: "right",
                                fontSize: "75%",
                                backgroundColor: "#daf4eb",
                                borderRadius: "5px",
                                color: "#34c39a",
                              }}
                            >
                              Completed
                            </span>
                          </h1>
                        </div>
                        <h1
                          className="card-subtitle text-body-secondary"
                          style={{ fontSize: "13px" }}
                        >
                          10 Dec, 2023
                        </h1>
                        <p style={{ fontSize: "18px", marginTop: "5%" }}>
                          Display the List Of Project
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    className="card"
                    style={{
                      width: "21rem",
                      height: "8rem",
                      marginBottom: "4%",
                    }}
                  >
                    <div className="card-body">
                      <div>
                        <div>
                          <h1
                            className="card-title"
                            style={{ fontSize: "20px" }}
                          >
                            Create a Project
                            <span
                              style={{
                                float: "right",
                                fontSize: "75%",
                                backgroundColor: "#daf4eb",
                                borderRadius: "5px",
                                color: "#34c39a",
                              }}
                            >
                              Completed
                            </span>
                          </h1>
                        </div>
                        <h1
                          className="card-subtitle text-body-secondary"
                          style={{ fontSize: "13px" }}
                        >
                          10 Dec, 2023
                        </h1>
                        <p style={{ fontSize: "18px", marginTop: "5%" }}>
                          Display the List Of Project
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="row">
            <div className="col-lg-4 col-md-12">
              <div
                className="shadow bg-light overflow-scroll m-2 mt-3"
                style={{ maxHeight: 350 }}
              >
                <h5
                  className="text-center m-2 text-secondary"
                  style={{ fontWeight: 600 }}
                >
                  Completed Tasks
                </h5>
                <div className="m-3" style={{ height: "40vh" }}>
                  {completedTask.length === 0 ? (
                    <h6 className="mt-3 d-flex align-items-center justify-content-center">
                      <ErrorOutlineIcon className="mx-1" />
                      No completed tasks.
                    </h6>
                  ) : (
                    <ul>
                      {completedTask.map((data: any, index: any) => (
                        <React.Fragment key={index}>
                          <div className="d-flex flex-column align-items-center justify-content-between">
                            <h6>
                              {data.name.split(" ").slice(0, 4).join(" ")}
                            </h6>
                            <span className="ml-2">{data.status}</span>
                          </div>
                          {index !== completedTask.length - 1 && <hr />}
                        </React.Fragment>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>
            <div className="col-lg-8 col-md-12">
              <div
                className="shadow bg-light overflow-scroll m-2 mt-3"
                style={{ maxHeight: 350 }}
              >
                <h5
                  className="text-center m-1 text-secondary"
                  style={{ fontWeight: 600 }}
                >
                  WorkLoad
                </h5>
                <div className=" m-3" style={{ height: "40vh" }}>
                  <h6 className="mt-3 d-flex align-items-center justify-content-center">
                    <ErrorOutlineIcon className="mx-1" />
                  </h6>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Grid>

      <BackDrop open={loading} />
    </>
  );
};
