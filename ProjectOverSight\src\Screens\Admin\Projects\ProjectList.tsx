import { useState, useEffect, useRef } from "react";
import Box from "@mui/material/Box";
import DataTable from "react-data-table-component";
import { ConvertDate } from "../../../Utilities/Utils";
import Button from "@mui/material/Button";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import Select from "react-select";
import DownloadIcon from "@mui/icons-material/Download";
import { DownloadProjectist } from "../../../Services/ProjectService";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import { AddProject } from "./AddProject";
import { Typography, Grid, Tooltip } from "@mui/material";
import { Get } from "../../../Services/Axios";
import { Link } from "react-router-dom";
import "../../../StyleSheets/ProjectList.css";
import { ViewProject } from "./ViewProject";
import { EditProject } from "./EditProject";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import AutoStoriesIcon from "@mui/icons-material/AutoStories";
import PersonIcon from "@mui/icons-material/Person";
import MonitorIcon from "@mui/icons-material/Monitor";
import { useContextProvider } from "../../../CommonComponents/Context";
import { ADMIN } from "../../../Constants/Common/Roles";
import BackDrop from "../../../CommonComponents/BackDrop";
import { useQuery } from "react-query";
import { Projectpriority } from "../WeeklyPlan/Projectpriority";
import { IOSSwitch } from "../../../CommonComponents/IOSSwitch";
import { NProject, Project } from "../../../Models/Project/Project";
import { Regex } from "../../../Constants/Regex/Regex";
import { ProjectFilter } from "../../../Constants/Project/Projectlist";

// interface ProjectFilter {
//   id?: number | null;
//   name?: string | null;
//   description?: string | null; 
//   type?: string | null;
//   status?: string | null;
//   percentage?: number | null;
//   startDate?: string | null;
//   endDate?: string | null;
//   isActive?: boolean | null;
//   actStartDate?: Date | null;
//   estStartDate?: Date | null;
//   actEndDate?: Date | null;
//   projectName ?: String | null;
//   projectType ?: String | null;
//   priorities ?: String | null;
//   pageNumber?: number | null;
//   pageSize?: number | null;
//   direction?: string | null;
// }



export const ProjectList = () => {
  const [rows, setRows] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [reload, setReload] = useState<boolean>(true);
  const [active, setActive] = useState(true);
  const [filterRows, setfilterRows] = useState<any>([]);
  const projectNameRef = useRef<any>(null);
  const statusRef = useRef<any>();
  const ProjectpriorityRef = useRef<any>();
  const percentageRef = useRef<HTMLInputElement>(null);
  const actStartDateRef = useRef<HTMLInputElement>(null);
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const projectTypeRef = useRef<any>();
  const [refreshFlag, setRefreshFlag] = useState<boolean>(false);
  const [Page, setPage] = useState(0);
  const [filter, setfilter] = useState<NProject>(ProjectFilter);
  const [projectdata, setProjectdata] = useState<any>();
  const [projectView, setProjectView] = useState<any>({
    view: false,
    edit: false,
    add: false,
    viewObj: false,
    Priority: false,
  });
  var ProjectSet = new Set<any>();
  const { role } = useContextProvider();
  const [Projects, setProjects] = useState<Array<Project>>([]);

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      type: "Date",
      width: "14rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: any) => {
        return (
          <>   
            <Tooltip
              className="mx-1"
              title="view"
              onClick={() => {
                setProjectView({ view: true });
                setProjectdata(row);
              }}
            >
              <VisibilityIcon className="fs-4 text-info" />
            </Tooltip>
            {role === ADMIN && (
              <Tooltip
                className="mx-1"
                title="Edit"
                onClick={() => {
                  setProjectView({ edit: true });
                  setProjectdata(row);
                }}
              >
                <EditIcon className="fs-4 text-warning" />
              </Tooltip>
            )}
            <Link
              to={row.isActive && `/${role}/UserStory`}
              state={{
                projectId: row.id,
                projectName: row.name,
                projectReportRoute: false,
                status: "",
              }}
              style={{ textDecoration: "none" }}
            >
              <Tooltip
                color="info"
                title={`${
                  row.isActive ? "User Story" : "Project is not active"
                }`}
                className="mx-1"
              >
                <AutoStoriesIcon className="fs-4 text-primary" />
              </Tooltip>
            </Link>
            {role === ADMIN && (
              <Link
                to={row.isActive && "/Admin/ManageAssignment"}
                state={{
                  projectId: row.id,
                  projectName: row.name,
                }}
                style={{ textDecoration: "none" }}
              >
                <Tooltip
                  title={`${
                    row.isActive ? "Assign Employee" : "Project is not active"
                  }`}
                  className="mx-1"
                >
                  <PersonIcon />
                </Tooltip>
              </Link>
            )}
            <Link
              to={row.isActive && `/${role}/UserInterface`}
              state={{
                projectId: row.id,
                projectName: row.name,
                projectReportRoute: false,
                status: "",
              }}
              style={{ textDecoration: "none" }}
            >
              <Tooltip
                title={`${
                  row.isActive ? "User Interface" : "Project is not active"
                }`}
                className="mx-1"
              >
                <MonitorIcon className="fs-4 text-secondary" />
              </Tooltip>
            </Link>
            {role === ADMIN && (
              <Link
                to={row.isActive && "/Admin/AssignCustomer"}
                state={{
                  projectId: row.id,
                  projectName: row.name,
                }}
              >
                <Tooltip
                  title={`${
                    row.isActive ? "Assign Customer" : "Project is not active"
                  }`}
                  className="mx-1"
                >
                  <PersonIcon className="fs-4 text-dark" />
                </Tooltip>
              </Link>
            )}
          </>
        );
      },
    },
    {
      field: "name",
      name: "Project name",
      width: "13rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      cellClassName: "bg-light",
      align: "left",
      selector: (row: any) => (
        <Tooltip title={row.name}>
          <Link
            className={`tableStyle ${!row.isActive && "text-dark"}`}
            to={row.isActive && `/${role}/ProjectQuadrant`}
            state={{
              projectId: row.id,
              projectName: row.name,
              startDate: row.startDate,
              endDate: row.endDate,
            }}
            style={{ textDecoration: "none" }}
          >
            {row.name}
          </Link>
        </Tooltip>
      ),
    },
    {
      field: "type",
      name: "Project Type",
      width: "13rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => <p className="tableStyle">{row.type}</p>,
    },
    {
      field: "priorities",
      name: "Priority",
      width: "10rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.priorities}</p>,
    },
    {
      field: "description",
      name: "Description",
      width: "18rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => (
        <Tooltip title={row.description}>
          <p className="tableStyle">{row.description}</p>
        </Tooltip>
      ),
    },
    {
      field: "status",
      name: "Status",
      width: "10rem",
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.status}</p>,
    },
    {
      field: "percentage",
      name: "Percentage",
      type: "number",
      width: "10rem",
      align: "right",
      headerClassName: "bg-primary text-light",
      headerAlign: "right",
      editable: false,
      right: true,
      selector: (row: any) => <p className="tableStyle">{row.percentage}</p>,
    },
    {
      field: "startDate",
      name: "Start Date",
      type: "Date",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => {
        const result = ConvertDate(row.startDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
    {
      field: "endDate",
      name: "End Date",
      type: "Date",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => {
        const result = ConvertDate(row.endDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
  ];

  const handleClickOpen = () => {
    setProjectView({ add: true });
  };

  async function fetchCommonMaster1() {
    const commonMaster: any = await Get("app/CommonMaster/GetCodeTableList");
    return { commonMaster };
  }

  const { data } = useQuery("AddProject", fetchCommonMaster1);

  useEffect(() => {
    const projects =  Get("app/Project/GetProjectList");
    projects.then((response:any) =>{
      setProjects(response?.data);
    });

    projects.then((response: any) => {

      const allProjects = response?.data || [];
      const activeProject = allProjects.filter(
        (Project: any) => Project.isActive
      );
      const inactiveProject = allProjects.filter(
        (Project: any) => !Project.isActive
      );
      activeProject.sort((a: any, b: any) => a.name.localeCompare(b.name));
      inactiveProject.sort((a: any, b: any) => (a.name > b.name ? 1 : -1));
      //const priorityOrder: Record<string, number> = { High: 1, Medium: 2, Low: 3 };
      // activeProject.sort((a: any, b: any) => {
      //   if (a.name.localeCompare(b.name) === 0) {
      //     return priorityOrder[a.priority] - priorityOrder[b.priority];
      //   } else {
      //     return 0; // Maintain alphabetical order if names are different
      //   }
      // });
      const mergedProjects = [...activeProject, ...inactiveProject];
      setRows(mergedProjects || []);
      setfilterRows(mergedProjects || []);
      const projectNames = mergedProjects.map((project: any) => ({
        name: project.name,
        isActive: project.isActive,
      }));
      setProjects(projectNames);
      setLoading(false);
    });
  }, [reload, refreshFlag, loading]);

  rows?.forEach((row: any) => {
    ProjectSet.add(row?.projectName);
  });
  rows?.forEach((row: any) => {
    ProjectSet.add(row?.projectName);
  });

  function ApplyFilter() {
    let temp: any = [];

    if (filter.actStartDate != null && filter.estStartDate != null) {
      return;
    }

    if (filter.actStartDate != null) {
      if (filter.actEndDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].startDate.slice(0, 10) >= filter.actStartDate &&
          rows[i].endDate.slice(0, 10) <= filter.actEndDate
        ) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.projectName != null) {
      temp = temp.filter((e: any) => {
        return (
          e.name
            .replace(Regex.specialCharactersRegex, "")
            .toLowerCase()
            .search(
              filter.projectName
                ?.replace(Regex.specialCharactersRegex, "")
                ?.toLowerCase()
            ) >= 0
        );
      });
      setfilterRows(temp);
    }

    if (filter.projectType != null) {
      temp = temp.filter((e: any) => {
        return e.type.toLowerCase() === filter.projectType?.toLowerCase();
      });
      setfilterRows(temp);
    }

    if (filter.status != null) {
      if (typeof filter.status === "string") {
        temp = temp.filter((e: any) => {
          return e.status.toLowerCase() === filter.status?.toLowerCase();
        });
      } else if (typeof filter.status === "boolean") {
        temp = temp.filter((e: any) => {
          return e.isActive === filter.status;
        });
      }
      setfilterRows(temp);
    }

    if (filter.priorities != null) {
      temp = temp.filter((e: any) => {
        return (
          e.priorities != null &&
          e.priorities.toLowerCase() === filter.priorities?.toLowerCase()
        );
      });
    }
    setfilterRows(temp);

    if (filter.percentage != null) {
      temp = temp.filter((e: any) => {
        return e.percentage === Number(filter.percentage);
      });
      setfilterRows(temp);
    }
  }

  const handleChangePage = (page: number) => {
    debugger
    setfilter((prevState) => {
      return {
        ...prevState,
        direction: Page > page ? "PREVIOUS" : "NEXT",
      };
    });
    setfilter((prevState) => {
      return {
        ...prevState,
        pageNumber: page,
      };
    });
    setPage(page);
    setLoading((prev) => !prev);
  };

  const handleChangeRowsPerPage = (currentRowsPerPage: number, _: number) => {
    debugger
    setfilter((prevState) => {
      return {
        ...prevState,
        pageSize: currentRowsPerPage,
      };
    });
    setfilter((prevState) => {
      return {
        ...prevState,
        pageNumber: 0,
      };
    });
    setLoading((prev) => !prev);
  };

  const handleRefresh = () => {
    setRefreshFlag((prev) => !prev);
  };
  function reset() {
    setfilter(ProjectFilter);
    if (projectNameRef.current) projectNameRef.current.clearValue();
    if (statusRef.current) statusRef.current.clearValue();
    if (percentageRef.current) percentageRef.current.value = "";
    if (actStartDateRef.current) actStartDateRef.current.value = "";
    if (actEndDateRef.current) actEndDateRef.current.value = "";
    if (projectTypeRef.current) projectTypeRef.current.clearValue();
    if (ProjectpriorityRef.current) ProjectpriorityRef.current.clearValue();
    setfilterRows(rows);
    handleRefresh();
  }

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Project</Typography>
      </Breadcrumbs>

      <div className="well mx-auto mt-4">
        <div className="row">
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Project Name</label>
              <Select
                id="project-name"
                ref={projectNameRef}
                className="col mt-1 custom-select"
                onChange={(selectedOption: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      projectName: selectedOption ? selectedOption.value : null,
                    };
                  });
                }}
                options={Projects?.filter(
                  (x: any) => x.isActive === active
                ).map((e: any) => ({
                  label: e.name,
                  value: e.name,
                }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Project Type</label>
              <Select
                aria-label="Floating label select example"
                name="projectType"
                ref={projectTypeRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  if (code) {
                    setfilter((prevState: any) => {
                      return { ...prevState, projectType: code.label };
                    });
                  }
                }}
                options={
                  data?.commonMaster?.data?.length > 0 &&
                  data?.commonMaster?.data
                    ?.sort((a: any, b: any) =>
                      a.codeValue.localeCompare(b.codeValue)
                    )
                    .filter((x: any) => x.codeType === "ProjectType")
                    .map((e: any) => ({
                      label: e.codeValue,
                      id: e.codeValue,
                    }))
                }
                isSearchable={true}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    zIndex: 999,
                  }),
                  menu: (provided) => ({
                    ...provided,
                    maxHeight: 300,
                    zIndex: 999,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                aria-label="Floating label select example"
                name="status"
                ref={statusRef}
                className="select-dropdowns mt-1 col"
                onChange={(selectedOption: any) => {
                  if (selectedOption) {
                    setfilter((prevState: any) => ({
                      ...prevState,
                      status:
                        selectedOption.value === ""
                          ? null
                          : selectedOption.value,
                    }));
                  }
                }}
                options={[
                  {
                    label: "Completed",
                    value: "Completed",
                  },
                  {
                    label: "In Progress",
                    value: "In Progress",
                  },
                ]}
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Percentage</label>
              <input
                id="percentage"
                placeholder="Percentage"
                className="m-1 form-control col"
                ref={percentageRef}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const numericValue = parseFloat(inputValue);
                  if (!isNaN(numericValue) || inputValue === "") {
                    setfilter((prevState: any) => ({
                      ...prevState,
                      percentage: isNaN(numericValue)
                        ? undefined
                        : numericValue,
                    }));
                  }
                }}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label className="mx-1">Start Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      actStartDate:
                        e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="start-date"
                placeholder="Start Date"
                ref={actStartDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label className="mx-1">End Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      actEndDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="end-date"
                placeholder="End Date"
                ref={actEndDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-2">
            <div className="form-group">
              <label>Project Priority</label>
              <Select
                aria-label="Floating label select example"
                name="Select"
                ref={ProjectpriorityRef}
                className="select-dropdowns mt-1 col f-left"
                onInputChange={(inputValue: string) => {
                  const alphabeticValue = inputValue.replace(
                    /[^A-Za-z\s]/g,
                    ""
                  );
                  return alphabeticValue;
                }}
                onChange={(selectedOption: any) => {
                  if (selectedOption) {
                    setfilter((prevState: any) => ({
                      ...prevState,
                      priorities:
                        selectedOption.label.trim() === ""
                          ? null
                          : selectedOption.label,
                    }));
                  }
                }}
                options={[
                  {
                    label: "High",
                    value: "High",
                  },
                  {
                    label: "Medium",
                    value: "Medium",
                  },
                  {
                    label: "Low",
                    value: "Low",
                  },
                ]}
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>
          <div className="col-sm-6 col-md-6 col-lg-10">
            <div className="row justify-content-end">
              <div className="col-auto d-flex align-items-center">
                <span className="mt-3">
                  <IOSSwitch
                    className="mx-2"
                    defaultChecked
                    onChange={(e: any) => {
                      setActive(e.target.checked);
                    }}
                  />
                  <span>{active ? "Active" : "In Active"}</span>
                </span>
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-3 mt-3"
                  onClick={() => ApplyFilter()}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => reset()}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="d-flex flex-column justify-content-center align-items-center mb-5">
        <div className="col-11">
          <Grid>
            {role === ADMIN && (
              <>
                <Button
                  variant="contained"
                  className="mb-2 float-md-start"
                  onClick={handleClickOpen}
                >
                  Add Project
                  <AddIcon className="mx-1" />
                </Button>
                <Button
                  variant="contained"
                  className="mb-2 ml-1"
                  sx={{ ml: "3%" }}
                  onClick={() => setProjectView({ Priority: true })}
                >
                  Project Priority
                  <AddIcon className="mx-1" />
                </Button>
              </>
            )}

            <Button
              variant="contained"
              className="mb-2 float-end"
              onClick={() => {
                DownloadProjectist(filterRows);
              }}
            >
              Download
              <DownloadIcon className="mx-1" />
            </Button>
          </Grid>
        </div>
        <Grid item xs={12} sm={11} sx={{ mt: "4%" }}>
          <div className="responsive-div col-3 col-s-3">
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                fixedHeader
                responsive
                persistTableHead
                progressPending={loading}
                data={
                  filterRows.filter((x: Project) => x.isActive === active) || []
                }
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },

                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
                paginationComponentOptions={{
                  rowsPerPageText: "Rows per page:",
                  rangeSeparatorText: "of",
                  noRowsPerPage: false,
                  selectAllRowsItem: false,
                  selectAllRowsItemText: "All",
                }}
                onChangePage={handleChangePage}
                onChangeRowsPerPage={handleChangeRowsPerPage}
                paginationTotalRows={filterRows?.length && filterRows[0]?.totalCount }
                paginationServer
              />
            </Box>
          </div>
        </Grid>
      </div>
      <AddProject
        openDialog={projectView}
        setOpenDialog={setProjectView}
        setReload={setReload}
      />
      <ViewProject
        openDialog={projectView}
        setOpenDialog={setProjectView}
        Data={projectdata}
      />
      <EditProject
        openDialog={projectView}
        setOpenDialog={setProjectView}
        setfilterRows={setfilterRows}
        setRows={setRows}
        Data={projectdata}
      />
      <Projectpriority
        open={projectView}
        setOpen={setProjectView}
        setReload={setReload}
      />
      <BackDrop open={loading} />
    </>
  );
};
