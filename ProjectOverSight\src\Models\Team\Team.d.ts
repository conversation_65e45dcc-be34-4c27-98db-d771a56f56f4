import { IBase } from "../Common/BaseModel";
import { Project } from "../Project/Project";
import { TaskListDto } from "../Task/Task";

export interface Team extends IBase {
  name: string;
  startDate: Date;
  endDate: Date;
}

export interface  TeamReportVM 
{
  teamtotalprojects: array<TeamProject>;
  teamtotalobjectives: array<TeamObjective>;
  teamMemebers: array<TeamEmployee>;
  teamtotaltasks: array<EmployeeTask>;
  teamdetails: array<TeamDetailsVM>;
  todaysTasks:array<TaskListDto>
}

export interface TeamDetailsVM 
{
  projectName: string | null ;
  employeeName: string | null;
  totalTaskCount: number | null;
  totalActualHrs: number | null;
  employeeId: number | null;
  id: number | null;
}

export interface TeamProjectVM
{
  startDate: Date;
  endDate: Date | null;
  teamId: number;
  projectId: number;
  team?: Team | null;
  project?: Project | null;
  projectName?: string | null;
}

export type TeamFilter = {
  projectName: string ;
  employeeName: string ;  
}




