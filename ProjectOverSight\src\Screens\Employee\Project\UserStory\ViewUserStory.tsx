import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Grid,
  Autocomplete,
  TextareaAutosize,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
export const ViewUserStory = ({ openDialog, setOpenDialog, Data, data }: any) => {
  const handleClose = () => {
    setOpenDialog({ view: false });
  };

  var objective: any[] = [];
  data?.data?.projectObjectiveMappings?.forEach((e: any) => {
    if (e.userStoryId === Data?.id)
      objective.push({ label: e.description, id: e.projectObjectiveId });
  });

  return (
    <div>
      <Dialog open={openDialog?.view}>
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              View User Story
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    required
                    value={Data?.name}
                    label="User Story Name"
                    type="text"
                    variant="outlined"
                    className="read-only-input"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    id="outlined-read-only-input"
                    label="Status"
                    defaultValue={Data?.status}
                    InputProps={{
                      readOnly: true,
                    }}
                    className="read-only-input"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <InputLabel id="description">Description</InputLabel>
                <FormControl fullWidth>
                  <TextareaAutosize
                    required
                    defaultValue={Data?.description}
                    placeholder="Description"
                    style={{ height: 100 }}
                    className="read-only-input"
                    disabled
                  />
                  {/* <TextField
                required
                value={Data?.description}
                margin="dense"
                label="Description"
                type="text"
                fullWidth
                variant="outlined"
              /> */}
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Start date</InputLabel>
                <FormControl fullWidth>
                  <TextField
                    required
                    id="start-date"
                    className="read-only-input"
                    value={Data?.startDate?.slice(0, 10)}
                    margin="dense"
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">End date</InputLabel>
                <FormControl fullWidth>
                  <TextField
                    required
                    id="end-date"
                    className="read-only-input"
                    value={Data?.endDate?.slice(0, 10)}
                    margin="dense"
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <InputLabel id="Project Objectives">Project Objectives</InputLabel>
                <FormControl fullWidth>
                  <Autocomplete
                    multiple
                    disabled={objective?.map((e: any) => e?.label) ? false : true}
                    readOnly
                    defaultValue={objective?.map((e: any) => e.label)}
                    options={[]}
                    sx={{ maxHeight: '150px', overflowY: 'auto' }}
                    renderInput={(params: any) => (
                      <TextField
                        {...params}
                        className="read-only-input"
                        label={objective?.length > 0 ? '' : 'Select Objectives'}
                        variant="outlined"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="primary"
            >
              OK
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
