import { IBase } from "../Common/BaseModel";

export interface UserStory extends IBase {
  projectId?: number | null;
  projectObjectiveIds?: Array<number> | null;
  name?: string | null;
  description?: string | null;
  status?: string | null;
  percentage?: number | null;
  startDate?: string | null;
  endDate?: string | null;
  id?: number | null;
  isActive?: boolean | null;
  documents?:Array<File> | null;
}


export interface Document extends IBase {
  TableName?: string  | null;
  AttributeId?: number  | null;
  ProjectId?: number  | null;
  DocType?: string  | null;
  FileName?: string  | null;
  FileType?: string  | null;
  File?: File  | null;
  IsActive?: boolean  | null;
}
