import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import CssBaseline from "@mui/material/CssBaseline";
import Box from "@mui/material/Box";
import LockOutlinedIcon from "@mui/icons-material/LockOutlined";
import Typography from "@mui/material/Typography";
import Container from "@mui/material/Container";
import Alert from "@mui/material/Alert";
import "../../StyleSheets/LoginPage.css";
import LinearProgress from "@mui/material/LinearProgress";
import { SignUp } from "./SignUp";
import { Register } from "./Register";
import { useState } from "react";
import { TypeAnimation } from "react-type-animation";

type UserAction = {
  signUp: boolean;
  register: boolean;
};

export const LoginScreen = () => {
  sessionStorage.setItem("isLoggedIn", "Login");
  const [userAction, setUserAction] = useState<UserAction>({
    signUp: true,
    register: false,
  });
  const [emailError, setemailError] = useState<boolean>(false);
  const [passwordError, setpasswordError] = useState<boolean>(false);
  const [errorMsg, seterrorMsg] = useState<string>("");
  const [loader, setLoader] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  // const [currentImage, setCurrentImage] = useState(1);
  // const totalImages = 10;
  // const imageFolder = "src/assets/Display_Pic/";

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     setCurrentImage((prevImage) => (prevImage % totalImages) + 1);
  //   }, 5000);

  //   return () => clearInterval(interval);
  // }, [totalImages]);

  return (
    <div className="Login-main w-100 d-flex flex-colum-reverse">
      <div
        className="w-100 d-flex-row"
        style={{
          display: "flex",
          marginTop: "18%",
          position: "relative",
        }}
      >
        <div
          className="title-card d-flex"
          style={{
            position: "absolute",
            left: "30%",
            zIndex: 1,
          }}
        >
          <h1
            className="fw-bold three-d-text"
            style={{ color: "rgb(25, 118, 210)" }}
          >
            <TypeAnimation
              sequence={["Project Oversight"]}
              wrapper="span"
              speed={50}
              cursor={false}
              repeat={1}
            />
          </h1>
        </div>
        {/* {[...Array(totalImages)].map((_, index) => (
          <img
            key={index + 1}
            src={`${imageFolder}Dis_${index + 1}.jpeg`}
            alt={`Project Image ${index + 1}`}
            style={{
              maxWidth: "100%",
              height: "90%",
              position: "absolute",
              padding: "5px",
              borderRadius: "50px",
              border: "2px solid #e6e3e3",
              top: "60%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              opacity: index + 1 === currentImage ? 1 : 0,
              transition: "opacity 1s ease-in-out",
              zIndex: index + 1 === currentImage ? 0 : -1,
            }}
          />
        ))} */}

        <div className="outer">
          <div className="inner"></div>
        </div>
      </div>

      <div
        className="login-container d-flex flex-column w-100 align-items-center "
        style={{ marginTop: "5%" }}
      >
        {(emailError || passwordError || error) && (
          <Alert severity="error">{errorMsg}</Alert>
        )}
        <Container component="main" maxWidth="xs" className="m-3 rounded">
          <CssBaseline />
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Avatar
              sx={{ m: 1, bgcolor: "secondary.main", marginBottom: "2%" }}
            >
              <LockOutlinedIcon />
            </Avatar>
            <Typography component="h1" variant="h5">
              {userAction.signUp ? "Sign in" : "Register"}
            </Typography>
            {loader && (
              <Box sx={{ width: "100%" }} className="mt-2">
                <LinearProgress />
              </Box>
            )}
            <div className="d-flex m-2">
              <Button
                fullWidth
                className="button mx-1"
                variant={userAction.signUp ? "contained" : `outlined`}
                onClick={() => {
                  setUserAction({ signUp: true, register: false });
                }}
              >
                Sign In
              </Button>
              <Button
                fullWidth
                className="button mx-1"
                type="submit"
                disabled={loader}
                variant={userAction.register ? "contained" : `outlined`}
                onClick={() => {
                  setUserAction({ signUp: false, register: true });
                  setemailError(false);
                  setpasswordError(false);
                }}
              >
                Register
              </Button>
            </div>
            <Box component="form" noValidate sx={{ mt: 1 }}>
              {userAction.signUp && (
                <SignUp
                  setLoader={setLoader}
                  setemailError={setemailError}
                  setpasswordError={setpasswordError}
                  seterrorMsg={seterrorMsg}
                  emailError={emailError}
                  passwordError={passwordError}
                  setError={setError}
                  loader={loader}
                />
              )}
              {userAction.register && (
                <Register setUserAction={setUserAction} />
              )}
            </Box>
          </Box>
        </Container>
      </div>
    </div>
  );
};
