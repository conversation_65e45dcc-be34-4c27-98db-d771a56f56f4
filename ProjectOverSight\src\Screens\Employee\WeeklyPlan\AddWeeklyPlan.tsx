import {
  Autocomplete,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  InputLabel,
  TextField,
  TextareaAutosize,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import "../../../StyleSheets/WeeklyPlan.css";
import { useEffect, useState } from "react";
import { Project } from "../../../Models/Project/Project";
import { Get, Post } from "../../../Services/Axios";
import { useContextProvider } from "../../../CommonComponents/Context";
import { Category, CommonMaster } from "../../../Models/Common/CommonMaster";
import { PRIORITYTYPE } from "../../../Constants/Common/CommonMaster";
import { Regex } from "../../../Constants/Regex/Regex";
import { ConvertToISO, WeekEndingDate } from "../../../Utilities/Utils";
import { useForm } from "react-hook-form";
import { IWeeklyPlan } from "../../../Models/WeeklyPlan/WeeklyPlan";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import Swal from "sweetalert2";

type AddWeeklyPlanProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  setReload: (prev: boolean) => void;
};

const formFields = [
  "project",
  "category",
  "description",
  "notes",
  "priority",
  "estimatedHours",
  "status",
  "percenatge",
  "assignedTo",
  "dueDate",
  "weekEndingDate",
  "CreatedBy",
  "UpdatedBy",
];

export const AddWeeklyPlan = ({
  open,
  setOpen,
  setReload,
}: AddWeeklyPlanProps) => {
  const [projects, setProjects] = useState<Array<Project>>([]);
  const { commonMaster, user, category } = useContextProvider();
  const [porjectId, setProjectId] = useState<number>(-1);
  const { register, handleSubmit, resetField } = useForm();

  async function fetchData() {
    const response: any = await Get("/app/Project/GetEmployeeProjectlist");
    setProjects(response.data || []);
  }

  function reset() {
    formFields.map((e: string) => {
      resetField(e);
    });
  }

  useEffect(() => {
    fetchData();
  }, []);

  async function onSubmitHandler(data: any) {
    var weeklyPlan: IWeeklyPlan = data;
    weeklyPlan.projectId = porjectId;
    weeklyPlan.empId = "";

    const { error }: any = await Post("app/WeeklyPlan/AddWeeklyPlan", data);
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Project Added Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload(true);
      handleClose();
    });
  }

  function handleClose() {
    reset();
    setOpen(false);
  }

  return (
    <Dialog open={open}>
      <form onSubmit={handleSubmit(onSubmitHandler)}>
        <div className="dialog-title-container">
          <DialogTitle className="dialog-title">Add Weekly Plan</DialogTitle>
          <CancelOutlinedIcon
            className="close-icon"
            onClick={() => setOpen(false)}
          />
        </div>
        <DialogContent className="row popup d-flex justify-content-center">
          <div className="row">
            <Autocomplete
              options={projects.map((project: Project) => ({
                label: project.name,
                id: project.id,
              }))}
              onChange={(e: any, { id }: any) => {
                setProjectId(id);
                return e;
              }}
              className="col-6 mb-2"
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Project"
                  required
                  {...register("project")}
                />
              )}
            />
            <Autocomplete
              options={category.map((category: Category) => ({
                label: category.subCategory,
                id: category.id,
              }))}
              className="col-6 mb-2"
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Category"
                  required
                  {...register("category")}
                />
              )}
            />
          </div>
          <div className="row">
            <TextareaAutosize
              required
              className="col  m-2 form-control"
              {...register("description", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                },
              })}
              placeholder="Description*"
              style={{ height: 100 }}
            />
          </div>
          <div className="row">
            <TextField
              required
              className="col m-2"
              label="Notes"
              {...register("notes", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                },
              })}
              type="text"
              variant="outlined"
            />
            <Autocomplete
              options={commonMaster
                .filter((x) => x.codeType === PRIORITYTYPE)
                .map((commonMaster: CommonMaster) => ({
                  label: commonMaster.codeValue,
                  id: commonMaster.codeValue,
                }))}
              className="col m-2"
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Priority"
                  className="mx-2"
                  required
                  {...register("priority")}
                />
              )}
            />
          </div>
          <div className="row">
            <TextField
              required
              className="col m-2"
              label="Estimated Hours"
              {...register("estimatedHours", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.DECIMAL, "");
                },
              })}
              type="text"
              variant="outlined"
            />
            <TextField
              required
              className="col m-2"
              disabled
              label="Status"
              {...register("status", { value: "In Progress" })}
              type="text"
              variant="outlined"
            />
          </div>
          <div className="row">
            <TextField
              required
              className="col m-2"
              label="Percentage"
              disabled
              {...register("percenatge", {
                value: "0",
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.NUMBER, "");
                },
              })}
              type="text"
              variant="outlined"
            />
            <TextField
              required
              className="col m-2"
              label="Assigned To"
              disabled
              type="text"
              {...register("assignedTo", { value: user?.userName })}
              variant="outlined"
            />
          </div>
          <div className="row">
            <div className="col">
              <InputLabel id="start-date">Due Date*</InputLabel>
              <TextField
                required
                id="start-date"
                margin="dense"
                inputProps={{
                  min: new Date().toISOString().slice(0, 10),
                }}
                type="date"
                {...register("dueDate")}
                fullWidth
                variant="outlined"
              />
            </div>
            <div className="col">
              <InputLabel id="end-date">Week Ending Date*</InputLabel>
              <TextField
                required
                id="end-date"
                margin="dense"
                defaultValue={ConvertToISO(WeekEndingDate())}
                inputProps={{
                  min: new Date().toISOString().slice(0, 10),
                }}
                {...register("weekEndingDate")}
                type="date"
                fullWidth
                variant="outlined"
              />
            </div>
          </div>
          <input {...register("CreatedBy")} value="user" hidden />
          <input {...register("UpdatedBy")} value="user" hidden />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpen(false)}
            size="medium"
            variant="contained"
            color="error"
          >
            Cancel
          </Button>
          <Button
            size="medium"
            variant="contained"
            color="success"
            type="submit"
          >
            Save
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
