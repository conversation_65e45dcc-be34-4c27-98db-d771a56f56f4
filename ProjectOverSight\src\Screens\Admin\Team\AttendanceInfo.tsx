import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import TimelineItem from "@mui/lab/TimelineItem";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import TimelineConnector from "@mui/lab/TimelineConnector";
import TimelineContent from "@mui/lab/TimelineContent";
import TimelineDot from "@mui/lab/TimelineDot";
import {
  ConvertDate,
  ConvertTime,
  convertTo12HourFormat,
} from "../../../Utilities/Utils";
import { GetHours } from "../../../Services/TeamService";

const AttendanceInfo = ({ show, setShow, viewattendence }: any) => {
  return (
    <Modal
      show={show}
      onHide={() => setShow(false)}
      centered
      size="lg"
      className="mt-5"
    >
      <h3 className="mx-auto text-center mb-1 mt-2">Attendance Info</h3>
      <Modal.Body className="d-flex flex-row">
        <div
          className="border border-2 mx-1"
          style={{ width: "50rem", height: "450px" }}
        >
          <h5 className="m-2 text-decoration-underline">Timesheet</h5>
          <div
            className="border border-2 mx-2 p-4"
            style={{
              width: "350px",
              height: "100px",
              backgroundColor: "rgba(128, 128, 128, 0.1)",
            }}
          >
            <h6 className="mx-1">Logged In at</h6>
            <div className="m-1">
              {ConvertTime(viewattendence?.inTime, "")}
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              {ConvertDate(viewattendence?.inTime)}
            </div>
          </div>
          <div
            className="border border-4  m-4 mx-auto d-flex align-items-center justify-content-center"
            style={{
              borderRadius: "80%",
              height: "150px",
              width: "150px",
              backgroundColor: "rgba(128, 128, 128, 0.1)",
            }}
          >
            {GetHours(viewattendence?.inTime, viewattendence?.outTime)}
          </div>
          <div
            className="border border-2 mx-2 p-4"
            style={{
              width: "350px",
              height: "100px",
              backgroundColor: "rgba(128, 128, 128, 0.1)",
            }}
          >
            <h6 className="mx-1">Logout at</h6>
            <div className="m-1">
              {convertTo12HourFormat(viewattendence?.outTime)}
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              {ConvertDate(viewattendence?.outTime)}
            </div>
          </div>
        </div>

        <div
          className="border border-2 mx-1"
          style={{ width: "50rem", height: "450px" }}
        >
          <h5 className="m-2 text-decoration-underline">Activity</h5>
          <div className="d-flex flex-column w-75">
            <TimelineItem>
              <TimelineSeparator>
                <TimelineDot style={{ backgroundColor: "green" }} />
                <TimelineConnector />
              </TimelineSeparator>
              <TimelineContent>
                In Time <br />
                {ConvertTime(viewattendence?.inTime, "")}
              </TimelineContent>
            </TimelineItem>
            <TimelineItem>
              <TimelineSeparator>
                <TimelineDot style={{ backgroundColor: "red" }} />
              </TimelineSeparator>
              <TimelineContent>
                Out Time <br />
                {convertTo12HourFormat(viewattendence?.outTime)}
              </TimelineContent>
            </TimelineItem>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="danger" onClick={() => setShow(false)}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AttendanceInfo;
