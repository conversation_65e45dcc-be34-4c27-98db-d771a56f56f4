import "../../../StyleSheets/EmployeeAttendance.css";
import { useEffect, useState } from "react";
import LoginIcon from "@mui/icons-material/Login";
import SouthWestIcon from "@mui/icons-material/SouthWest";
import ArrowOutwardIcon from "@mui/icons-material/ArrowOutward";
import PersonOffIcon from "@mui/icons-material/PersonOff";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { Get } from "../../../Services/Axios";
import YEARS, {
  ConvertTime,
  ConvertDate,
  TimeSpanText,
  TimeSpanBg,
  convertTo12HourFormat,
} from "../../../Utilities/Utils";
import { Link, useLocation } from "react-router-dom";
import { Breadcrumbs, Typography } from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import { GoogleMaps } from "../../../CommonComponents/GoogleMaps";
import { useContextProvider } from "../../../CommonComponents/Context";
import { Employee } from "../../../Models/Employee/Employee";
import { Regex } from "../../../Constants/Regex/Regex";
import BackDrop from "../../../CommonComponents/BackDrop";

const months = [
  { id: 1, name: "January" },
  { id: 2, name: "February" },
  { id: 3, name: "March" },
  { id: 4, name: "April" },
  { id: 5, name: "May" },
  { id: 6, name: "June" },
  { id: 7, name: "July" },
  { id: 8, name: "August" },
  { id: 9, name: "September" },
  { id: 10, name: "October" },
  { id: 11, name: "November" },
  { id: 12, name: "December" },
];

const CURRENT_YEAR = new Date().getFullYear();
const CURRENT_MONTH = new Date().getMonth() + 1;

export const EmployeeAttendence = () => {
  const location = useLocation();
  const [showMap, setshowMap] = useState(false);
  const [rows, setRows] = useState<any>([]);
  const [employeeList, setEmployeeList] = useState<any>([]);
  const { role } = useContextProvider();
  const [userId, setUserId] = useState<number>(location.state.userId);
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [DeptTime, setDeptTime] = useState<any>([]);
  const [EmpTime, setEmpTime] = useState<any>([]);
  const [dateFilter, setDateFilter] = useState<any>({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  });
  const [coordinate, setCoordinate] = useState<any>({
    latitude: 0,
    longitude: 0,
    place: "",
  });

  const daysInMonth = new Date(dateFilter.year, dateFilter.month, 0).getDate();
  const dateLabels = Array.from({ length: daysInMonth }, (_, i) => {
    const currentDate = new Date(dateFilter.year, dateFilter.month - 1, i + 1);
    return currentDate.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "short",
      day: "2-digit",
    });
  });

  useEffect(() => {
    fetchData();
  }, [dateFilter, userId]);

  async function fetchData() {
    setLoading(true);
    try {
      const employeeLists: any = await Get("app/Employee/GetEmployeeList");

      const employeeAttendance: any = await Get(
        `app/Employee/GetAttendanceByUserId?userId=${userId}&month=${dateFilter.month}&year=${dateFilter.year}`
      );
      const DepartmentTimes: any = await Get(
        "app/DepartmentTime/GetDepartmentTimes"
      );

      setData(employeeAttendance?.data);
      setRows(employeeAttendance?.data?.employeeAttendances || []);
      setEmployeeList(employeeLists?.data);

      const EmpTimes = employeeLists?.data?.find(
        (x: any) => x.userId === userId
      );

      const matchedDepartments = DepartmentTimes?.data?.filter(
        (department: any) => {
          const employeeDepartment = department?.employeeDepartments;
          if (
            Array.isArray(employeeDepartment) &&
            employeeDepartment.length > 0
          ) {
            return employeeDepartment.some(
              (employee: any) => employee.userId === userId
            );
          } else {
            return false;
          }
        }
      );

      if (EmpTimes) {
        const officialIntime = EmpTimes.officialIntime;
        const officialOuttime = EmpTimes.officialOuttime;

        if (officialIntime && officialOuttime) {
          const convertTo12HourFormat = (time: any) => {
            const [hours, minutes] = time.split(":");
            let hour = Number(hours);
            const period = hour >= 12 ? "PM" : "AM";
            if (hour > 12) {
              hour -= 12;
            } else if (hour === 0) {
              hour = 12;
            }
            return `${hour}:${minutes} ${period}`;
          };

          const intime12HourFormat = convertTo12HourFormat(officialIntime);

          setEmpTime(intime12HourFormat);
        }
      }

      const startTime = new Date(matchedDepartments[0]?.startTime);
      const startTimeString = startTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
      setDeptTime(startTimeString);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  }

  function isDisabled(id: number): boolean {
    if (parseInt(dateFilter.year) === CURRENT_YEAR) {
      return id > CURRENT_MONTH;
    }
    return false;
  }

  function isSelected(id: number) {
    if (parseInt(dateFilter.year) === CURRENT_YEAR) {
      return id === CURRENT_MONTH;
    }
    return id === dateFilter.month;
  }

  function handleYearChange(year: string) {
    if (parseInt(year) === CURRENT_YEAR) {
      setDateFilter({ year: Number(year), month: CURRENT_MONTH });
    } else {
      setDateFilter({ ...dateFilter, year: Number(year) });
    }
  }

  return (
    <>
      {location.state.route === "attendence" && (
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link color="inherit" to={`/${role}/Attendance`}>
            <Typography sx={{ fontWeight: "bold" }}>Attendence</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>
            Employee Attendance
          </Typography>
        </Breadcrumbs>
      )}
      {location.state.route === "adminDashboard" && (
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>
            Employee Attendance
          </Typography>
        </Breadcrumbs>
      )}
      <div className="emp-det-box p-3 row">
        <div className="d-flex row m-auto mb-2" style={{ width: "91vw" }}>
          <h2 className="mb-3 fw-bold col-md-3" style={{ marginRight: "30%" }}>
            {data?.name || "-"}
          </h2>

          <div className="col-md">
            <select
              className="form-select m-1"
              onChange={(e) => {
                var value = e.target.value;
                setUserId(parseInt(value));
              }}
            >
              <option disabled>Select Employee</option>
              {employeeList
                .sort((a: any, b: any) => a.name.localeCompare(b.name))
                .filter((x: any) => x.isActive === true)
                .map((employee: Employee) => (
                  <option
                    key={employee.userId}
                    value={`${employee.userId}`}
                    selected={employee.userId === location.state.userId}
                  >
                    {employee.name?.replace(Regex.CHAR_SPACE, " ")}
                  </option>
                ))}
            </select>
          </div>

          <div className="col-md">
            <select
              className="form-select m-1 col-md-4"
              defaultValue={dateFilter.year}
              onChange={(e: any) => handleYearChange(e.target.value)}
            >
              <option selected disabled>
                Select Year
              </option>
              {YEARS.map((year: number) => (
                <option value={year} key={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>

          <div className="col-md">
            <select
              className="form-select m-1"
              defaultValue={dateFilter.month}
              onChange={(e: any) => {
                setDateFilter(() => {
                  return { ...dateFilter, month: Number(e.target.value) };
                });
              }}
            >
              <option selected disabled>
                Select Month
              </option>
              {months.map((month) => (
                <option
                  key={month.id}
                  value={month.id}
                  selected={isSelected(month.id)}
                  disabled={isDisabled(month.id)}
                >
                  {month.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="emp-info row m-auto">
          <div
            className="row p-3 d-flex "
            style={{ justifyContent: "flex-start" }}
          >
            <div className="col-sm-3">
              <label htmlFor="name" className="mb-1 text-center">
                Role:
              </label>
              <h5 id="name1" className="input1">
                Employee
              </h5>
            </div>

            <div className="col-sm-4">
              <label htmlFor="name" className="mb-1">
                PhoneNumber:
              </label>
              <h5 id="name2" className="input2">
                {data?.phoneNumber || "-"}
              </h5>
            </div>

            <div className="col-sm-5">
              <div className="form-group">
                <label htmlFor="name" className="mb-1">
                  Email:
                </label>
                <h5 id="name3" className="input3 ">
                  {data?.email || "-"}
                </h5>
              </div>
            </div>
          </div>
        </div>

        <div
          className="attce d-flex mt-3 m-auto flex-wrap "
          style={{ width: "100%",justifyContent:'space-around' }}
        >
          <div className="flex-1 m-2 d-flex align-items-center ">
            <LoginIcon
              className="icon m-3"
              style={{
                fontSize: 64,
                padding: 10,
              }}
            />
            <div className="mx-2">
              <h2>{data?.totalAttendance || "0"}</h2>
              <p>Total Attendance</p>
            </div>
          </div>
          <div className="flex-1 m-2 d-flex align-items-center">
            <SouthWestIcon
              className="icon m-3"
              style={{
                fontSize: 64,
                padding: 10,
              }}
            />
            <div className="mx-2">
              <h2>{convertTo12HourFormat(data?.averageInTime)}</h2>
              <p>Average In Time</p>
            </div>
          </div>{" "}
          <div className="flex-1 m-2 d-flex align-items-center">
            <ArrowOutwardIcon
              className="icon m-3"
              style={{
                fontSize: 64,
                padding: 10,
              }}
            />
            <div className="mx-2">
              <h2>{convertTo12HourFormat(data?.averageOutTime)}</h2>
              <p>Average Out Time</p>
            </div>
          </div>{" "}
          <div className="flex-1 m-2 d-flex align-items-center">
            <PersonOffIcon
              className="icon m-3"
              style={{
                fontSize: 64,
                padding: 10,
              }}
            />
            <div className="mx-2">
              <h2>{data?.totalAbsent || "0"}</h2>
              <p>Total Absent</p>
            </div>
          </div>{" "}
        </div>
      </div>
      <div className="emp-atte-his  overflow-scroll mb-4 pb-4">
        <h2 className="mx-5 mt-4">Attendance History</h2>

        <div className="mx-4 d-flex flex-wrap justify-content-between">
          {rows.map((e: any, index: number) => {
            var day = dateLabels[rows.length - index - 1]?.split(",")[0];
            return (
              <div
                className="flex-2 mt-4 d-flex align-items-center flex-column"
                key={e?.date}
              >
                <div className="d-flex align-items-center justify-content-between width">
                  <div className="d-flex align-items-center justify-content-center mx-3">
                    <CalendarMonthIcon
                      className="icon"
                      style={{
                        fontSize: 40,
                        padding: 8,
                      }}
                    />
                    <h5 className="mx-2 mt-2 d-flex flex-column">
                      <span className="fs-6">{ConvertDate(e?.date)}</span>
                      <span className="fw-bold fs-6 text-center">{day}</span>
                    </h5>
                  </div>
                  <p
                    className={`mx-3 float-end ${TimeSpanBg(
                      e?.inTime,
                      DeptTime,
                      EmpTime
                    )} mt-2`}
                  >
                    {e.holidayApplicable
                      ? e.holidayName
                      : TimeSpanText(e?.inTime, DeptTime, EmpTime)}
                  </p>
                </div>
                <div className="d-flex justify-content-evenly width-1">
                  <div className="col-sm-3 mx-4">
                    <div className="form-group">
                      <p className="mb-1 ">In Time</p>
                      <h5
                        id="name"
                        className="time d-flex"
                        style={{ width: "7em" }}
                      >
                        {ConvertTime(e?.inTime, "")}
                        {ConvertTime(e?.inTime, "") !== "-" && (
                          <LocationOnIcon
                            className="fs-4 mx-1"
                            onClick={() => {
                              setCoordinate({
                                latitude: e?.employeeGeo[0]?.latitude || 0,
                                longitude: e?.employeeGeo[0]?.longitude || 0,
                                place: "In Time Location",
                              });
                              setshowMap(true);
                            }}
                          />
                        )}
                      </h5>
                    </div>
                  </div>
                  <div className="col-sm-4 mx-4">
                    <div className="form-group">
                      <label htmlFor="name" className="mb-1">
                        Out Time
                      </label>
                      <h5
                        id="name"
                        className="time d-flex"
                        style={{ width: "6.7em" }}
                      >
                        {ConvertTime(e?.outTime, "")}
                        {ConvertTime(e?.outTime, "") !== "-" && (
                          <LocationOnIcon
                            className="fs-4 mx-1"
                            onClick={() => {
                              setCoordinate({
                                latitude: e?.employeeGeo[1]?.latitude || 0,
                                longitude: e?.employeeGeo[1]?.longitude || 0,
                                place: "Out Time Location",
                              });
                              setshowMap(true);
                            }}
                          />
                        )}
                      </h5>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
          {rows.length % 3 != 0 && (
            <div
              className="flex-2 m-1 d-flex align-items-center flex-column hidden"
              style={{ visibility: "hidden" }}
            ></div>
          )}
        </div>
      </div>
      <GoogleMaps
        open={showMap}
        setOpen={setshowMap}
        coordinates={coordinate}
      />
      <BackDrop open={loading} />
    </>
  );
};
