import {
  Autocomplete,
  Button,
  InputLabel,
  TextField,
  TextareaAutosize,
  Tooltip,
} from "@mui/material";
import { TaskDTO } from "../../../../Models/Task/Task";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { Get, Post } from "../../../../Services/Axios";
import { useContextProvider } from "../../../../CommonComponents/Context";
import { CommonMaster } from "../../../../Models/Common/CommonMaster";
import {
  PRIORITYTYPE  
} from "../../../../Constants/Common/CommonMaster";
import { TASK } from "../../../../Constants/Task/Task";
import { ConvertToISO, WeekEndingDate } from "../../../../Utilities/Utils";
import Swal from "sweetalert2";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import { TaskAssign } from "../../../../Services/TaskService";
import { Regex } from "../../../../Constants/Regex/Regex";
import { IWeeklyPlan } from "../../../../Models/WeeklyPlan/WeeklyPlan";
import { Roles } from "../../../../Constants/Common/Roles";

type AddTaskProp = {
  task: TaskDTO;
  setTask: Dispatch<SetStateAction<TaskDTO>>;
  handleClose: () => void;
  setReload: Dispatch<SetStateAction<boolean>>;
  subCategory: string;
};

type ModalError = {
  name: boolean;
  description: boolean;
  estimateStartDate: boolean;
  estimateEndDate: boolean;
  estTime: boolean;
  priority: boolean;
  taskType: boolean;
  classification: boolean;
};

export const AddTask = ({
  setTask,
  task,
  handleClose,
  setReload,
  subCategory,
}: AddTaskProp) => {
  const { commonMaster, user, role, category } = useContextProvider();
  const [loading, setLoading] = useState<boolean>(false);
  const [list, setList] = useState<number[]>([1]);
  const assignSelfRef = useRef<HTMLInputElement>(null);
  const addToDayPlanRef = useRef<HTMLInputElement>(null);
  const [modalError, setError] = useState<ModalError>({
    name: false,
    description: false,
    estimateStartDate: false,
    estimateEndDate: false,
    estTime: false,
    priority: false,
    taskType: false,
    classification: false,
  });
  var counter = 0;
  const [map, setMap] = useState<Map<number, string>>();
  const [selectedTaskType, setSelectedTaskType] = useState<string | null>(null);
  const [taskTypeOptions, setTaskTypeOptions] = useState<string[]>([]);
  const [selectedClassification, setSelectedClassification] = useState<
    string | null
  >(null);
  const [classificationOptions, setClassificationOptions] = useState<string[]>(
    []
  );
  // 1️⃣ Reset + reload taskTypeOptions when subCategory changes
  useEffect(() => {
    // Always clear both selects—and the corresponding fields in your DTO
    setSelectedTaskType(null);
    setSelectedClassification(null);
    setTask((prev) => ({ ...prev, taskType: "", classification: "" }));

    if (!subCategory) {
      setTaskTypeOptions([]);
      setClassificationOptions([]);
      return;
    }

    const opts = [
      ...new Set(
        category
          .filter((c) => c.subCategory === subCategory)
          .map((c) => c.taskType)
      ),
    ].sort((a, b) => a.localeCompare(b));

    setTaskTypeOptions(opts);
    setClassificationOptions([]); // clear old classifications
  }, [subCategory, category]);

  // 2️⃣ Reload classificationOptions whenever the user picks a Task Type
  useEffect(() => {
    if (!selectedTaskType) {
      setClassificationOptions([]);
      return;
    }

    const opts = [
      ...new Set(
        category
          .filter(
            (c) =>
              c.subCategory === subCategory && c.taskType === selectedTaskType
          )
          .map((c) => c.taskClassification)
      ),
    ].sort((a, b) => a.localeCompare(b));

    setClassificationOptions(opts);
  }, [selectedTaskType, subCategory, category]);

  function isWeekEndingDate(date: string): boolean {
    const selectedDate = new Date(date);
    if (selectedDate.getUTCDay() === 5) return true;
    return false;
  }

  const [weeklyPlans, setWeeklyPlans] = useState<IWeeklyPlan[]>([]);

  async function fetchData() {
    const response: any = await Get(
      `app/WeeklyPlan/GetWeeklyPlans?projectId=${task.projectId}`
    );
    setWeeklyPlans(response.data || []);
  }

  useEffect(() => {
    fetchData();
  }, [task.projectId]);

  type taskType = keyof typeof modalError;

  async function saveTask() {
    debugger;
    var errors: any = {};
    var isModelError = false;

    Object.keys(modalError).forEach((key: string) => {
      if (task[key as taskType]?.toString().length === 0) {
        errors[`${key}`] = true;
        isModelError = true;
      } else {
        errors[`${key}`] = false;
      }
    });

    setError(errors);
    if (isModelError) return;
    setLoading(true);

    var postUrl: string = "";
    if (subCategory === "CheckList" && task.uiId > 0) {
      task.checkListDescriptions = Array.from(map?.values() || []);
      postUrl = "app/Task/CreateTaskCheckList";
    } else {
      postUrl = "app/Task/CreateTask";
    }

    const response: any = await Post(postUrl, task);

    if (assignSelfRef?.current || role == Roles.CUSTOMER) {
      if (
        assignSelfRef?.current?.checked ||
        addToDayPlanRef?.current?.checked ||
        role == Roles.CUSTOMER
      ) {
        const responseData = response?.data;
        if (responseData && "estTime" in responseData) {
          const {
            estTime,
            priority,
            estimateStartDate,
            estimateEndDate,
            weekEndingDate,
          } = responseData;

          debugger;

          var assignTask = {
            EstTime: estTime,
            Priority: priority,
            StartDate: estimateStartDate,
            EndDate: estimateEndDate,
            Comment: task.comment,
            WeekEndingDate: weekEndingDate,
            EmployeeId: user?.employeeId,
          };

          var result: any = await TaskAssign(assignTask, response.data);

          if (addToDayPlanRef.current) {
            if (addToDayPlanRef.current.checked) {
              const DayPlan: any = {
                employeeId: user?.employeeId,
                taskId: response.data?.id,
                employeeTaskId: result?.data?.id,
                projectObjectiveId: 1,
                projectId: task.projectId,
                name: task.name,
                employeeName: "",
                projectName: "",
                comment: "",
                status: "In-Progress",
                description: task.description,
                estTime: Number(task.estTime),
                weekEndingDate: undefined,
                priority: task.priority,
                workedOn: task.estimateStartDate,
              };

              await Post("/app/EmployeeDailyTask/AddEmployeeDayPlan", DayPlan);
            }
          }
        }
      }
    }
    setLoading(false);

    var option: AlertOption;
    if (!response?.error) {
      option = {
        title: "Success",
        text: "Task Added Successfully!",
        icon: "success",
      };
    } else {
      option = {
        icon: "error",
        title: "Error Creating Task",
        text: "An error occurred while creating the task. Please try again.",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
    });

    setTask(TASK);
    handleClose();
  }

  return (
    <>
      <div className="row w-75 mx-auto">
        <TextField
          required
          className="col m-2"
          error={modalError?.name}
          onChange={(e: any) => {
            setError({ ...modalError, name: false });
            e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
            setTask((prev: TaskDTO) => {
              return { ...prev, name: e.target.value };
            });
          }}
          label="Task Name"
          type="text"
          variant="outlined"
        />
        <TextField
          required
          className="col m-2"
          disabled
          defaultValue={"Unassigned"}
          label="Status"
          type="text"
          variant="outlined"
        />
      </div>
      <div className="row w-75 mx-auto">
        <TextareaAutosize
          required
          className="col m-2 form-control"
          placeholder="Description *"
          onChange={(e: any) => {
            e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
            setError({ ...modalError, description: false });
            setTask((prev: TaskDTO) => {
              return { ...prev, description: e.target.value };
            });
          }}
          style={{
            height: 80,
            borderColor: `${modalError.description ? "red" : ""}`,
          }}
        />
      </div>
      <div className="row w-75 mx-auto">
        <div className="col">
          <InputLabel id="start-date">Start date *</InputLabel>
          <TextField
            required
            error={modalError?.estimateStartDate}
            id="start-date"
            margin="dense"
            label=""
            inputProps={{
              min: new Date().toISOString().slice(0, 10),
            }}
            onChange={(e: any) => {
              setError({ ...modalError, estimateStartDate: false });
              setTask((prev: TaskDTO) => {
                return { ...prev, estimateStartDate: e.target.value };
              });
            }}
            type="date"
            fullWidth
            variant="outlined"
          />
        </div>
        <div className="col">
          <InputLabel id="end-date">End date *</InputLabel>
          <TextField
            required
            error={modalError?.estimateEndDate}
            id="end-date"
            margin="dense"
            inputProps={{
              min: new Date().toISOString().slice(0, 10),
            }}
            onChange={(e: any) => {
              if (new Date(task.estimateStartDate) > new Date(e.target.value)) {
                e.target.value = "";
                Swal.fire({
                  title: "",
                  text: "End date should be greater than start date.",
                  icon: "warning",
                });
                return;
              }
              setError({ ...modalError, estimateEndDate: false });
              setTask((prev: TaskDTO) => {
                return { ...prev, estimateEndDate: e.target.value };
              });
            }}
            label=""
            type="date"
            fullWidth
            variant="outlined"
          />
        </div>
      </div>
      <div className="row w-75 mx-auto">
        <div className="col">
          <InputLabel id="end-date" className="mb-4"></InputLabel>
          <TextField
            required
            type="text"
            error={modalError?.estTime}
            inputProps={{ minLength: 5, maxLength: 5 }}
            variant="outlined"
            fullWidth
            label="Estimated Time"
            onBlur={(e: any) => {
              if (isNaN(e.target.value)) {
                Swal.fire({
                  icon: "warning",
                  text: "Invalid Estimated Time",
                });
                e.target.value = "";
                setTask((prev: TaskDTO) => {
                  return { ...prev, estTime: "" };
                });
              }
            }}
            onChange={(e: any) => {
              e.target.value = e.target.value.replace(Regex.DECIMAL, "");
              setError({ ...modalError, estTime: false });
              setTask((prev: TaskDTO) => {
                return { ...prev, estTime: e.target.value };
              });
            }}
            margin="dense"
          />
        </div>
        <div className="col">
          <InputLabel id="end-date">Week Ending Date *</InputLabel>
          <TextField
            required
            id="end-date"
            margin="dense"
            label=""
            type="date"
            defaultValue={ConvertToISO(WeekEndingDate())}
            inputProps={{
              min: new Date().toISOString().slice(0, 10),
            }}
            onChange={(e: any) => {
              var date: any = "";
              if (isWeekEndingDate(e.target.value)) {
                date = e.target.value;
              } else {
                date = task.weekEndingDate;
                e.target.value = task.weekEndingDate;
              }
              setTask((prev: TaskDTO) => {
                return { ...prev, weekEndingDate: date };
              });
            }}
            fullWidth
            variant="outlined"
          />
        </div>
      </div>
      <div className="row w-75 mx-auto">
        <Autocomplete
          disablePortal
          className="col mt-2"
          onChange={(e: any, value: any) => {
            setTask((prev: TaskDTO) => {
              return { ...prev, priority: value?.label ?? "" };
            });
            setError({ ...modalError, priority: false });
            return e;
          }}
          options={commonMaster
            .filter((x) => x.codeType === PRIORITYTYPE)
            .map((commonMaster: CommonMaster) => ({
              label: commonMaster.codeValue,
              id: commonMaster.codeValue,
            }))}
          renderInput={(params) => (
            <TextField
              {...params}
              required
              error={modalError?.priority}
              label="Priority"
            />
          )}
        />

        <Autocomplete
          disablePortal
          className="col mt-2"
          options={taskTypeOptions.map((t) => ({ label: t, id: t }))}
          value={
            selectedTaskType
              ? { label: selectedTaskType, id: selectedTaskType }
              : null
          }
          onChange={(_, value) => {
            const val = value?.label ?? null;
            setSelectedTaskType(val);
            setSelectedClassification(null); // clear downstream
            setTask((prev) => ({
              ...prev,
              taskType: val || "",
              classification: "",
            }));
            setError({ ...modalError, taskType: false });
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              required
              error={modalError.taskType}
              label="Task Type"
            />
          )}
        />
      </div>

      <div className="row w-75 mx-auto">
        <Autocomplete
          disablePortal
          className="col mt-3"
          options={classificationOptions.map((c) => ({ label: c, id: c }))}
          value={
            selectedClassification
              ? { label: selectedClassification, id: selectedClassification }
              : null
          }
          onChange={(_, value) => {
            const val = value?.label ?? "";
            setSelectedClassification(val);
            setTask((prev) => ({ ...prev, classification: val }));
            setError({ ...modalError, classification: false });
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              required
              error={modalError.classification}
              label="Classification"
            />
          )}
          disabled={!selectedTaskType}
          style={{ zIndex: 999 }}
        />

        <TextField
          className="col mt-3 mx-2"
          label="Comments"
          onChange={(e: any) => {
            e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
            setTask((prev: TaskDTO) => {
              return { ...prev, comment: e.target.value };
            });
          }}
          type="text"
          variant="outlined"
        />
      </div>
      {role !== Roles.CUSTOMER && (
        <div className="row w-75 mx-auto mt-1">
          <Autocomplete
            options={weeklyPlans
              .filter(
                (x: IWeeklyPlan) =>
                  x.weekEndingDate &&
                  x.weekEndingDate.toString().slice(0, 10) ===
                    ConvertToISO(WeekEndingDate())
              )
              .map((x: IWeeklyPlan) => ({
                label: x.description,
                id: x.id,
              }))}
            onChange={(e: any, value: any) => {
              setTask((prev: TaskDTO) => {
                return { ...prev, weeklyPlanId: value?.id ?? "" };
              });
              return e;
            }}
            className="col mt-3"
            renderInput={(params) => (
              <TextField {...params} label="Weekly Plan" />
            )}
          />
          <Autocomplete
            options={commonMaster
              .filter((x: CommonMaster) => x.codeName === "Recurring")
              .sort((a, b) => a.codeValue.localeCompare(b.codeValue))
              .map((x: CommonMaster) => ({
                label: x.codeValue,
                id: x.id,
              }))}
            onChange={(e: any, value: any) => {
              setTask((prev: TaskDTO) => {
                return { ...prev, recurring: value?.label ?? "" };
              });
              return e;
            }}
            className="col mt-3"
            renderInput={(params) => (
              <TextField {...params} label="Recurring" />
            )}
          />
        </div>
      )}
      {role !== Roles.CUSTOMER && (
        <div className="row w-75 mx-auto mt-1">
          <div className="col mt-3">
            <div>
              <input
                type="radio"
                name="name"
                className="mx-1"
                id="assign-self"
                ref={assignSelfRef}
              />
              <label htmlFor="assign-self">Assign Task to self</label>
            </div>
            <div>
              <input
                type="radio"
                name="name"
                className="mx-1"
                id="add-daily"
                ref={addToDayPlanRef}
              />
              <label htmlFor="add-daily">Add to My Day Plan</label>
            </div>
          </div>
        </div>
      )}
      <div className="row w-75 mx-auto">
        {subCategory === "CheckList" && (
          <>
            {list.map((e, index) => {
              counter++;
              return (
                <div className="d-flex align-items-center flex-row" key={index}>
                  <TextField
                    key={e}
                    id="field3"
                    label={`Check List ${counter}`}
                    variant="standard"
                    className="col mt-1"
                    onBlur={(event: any) => {
                      var temp = new Map<number, string>(map);
                      temp.set(e, event.target.value);
                      setMap(temp);
                    }}
                    onChange={(e: any) => {
                      e.target.value = e.target.value.replace(
                        Regex.CHAR_NUM,
                        ""
                      );
                    }}
                    sx={{
                      width:
                        index == list.length - 1 && list.length > 1 ? 370 : 400,
                      mb: 4,
                      boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.1)",
                    }}
                  />
                  {index == list.length - 1 && list.length > 1 && (
                    <Tooltip title="Remove Item">
                      <RemoveCircleOutlineIcon
                        className="mx-1 float-end"
                        onClick={() => {
                          var newMap = new Map<number, string>(map);
                          newMap.delete(list.length);
                          setMap(newMap);
                          var temp: number[] = list.filter((x) => x != e);
                          setList(temp);
                        }}
                      />
                    </Tooltip>
                  )}
                </div>
              );
            })}
            <div className="d-flex justify-content-center">
              <Tooltip title="Add Item">
                <AddCircleOutlineIcon
                  onClick={() => setList([...list, list[list.length - 1] + 1])}
                />
              </Tooltip>
            </div>
          </>
        )}
      </div>

      <div className="row w-25 mx-auto m-5">
        <Button
          variant="contained"
          color="success"
          className="w-50 mx-auto fw-bold"
          disabled={loading}
          onClick={() => saveTask()}
          style={{ backgroundColor: loading ? "#bebfb8" : "#08c441" }}
        >
          {loading ? "Saving..." : "Save"}
        </Button>
      </div>
    </>
  );
};
