import {
  Alert,
  Dialog,
  DialogTitle,
  TextField,
  Button,
  DialogContent,
  InputLabel,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
} from "@mui/material";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { Post } from "../../../Services/Axios";
import Swal from "sweetalert2";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { Regex } from "../../../Constants/Regex/Regex";
const formField = ["name", "startDate", "endDate", "id", "isActive"];

export const EditTeam = ({
  openDialog,
  setOpenDialog,
  setReload,
  Data,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [save, setSave] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const onSubmitHandler = async (data: any) => {
    setSave(true);
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }
    data.isActive = data.isActive === "true";
    const { error }: any = await Post("app/Team/Updateteam", data);
    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Team Updated Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ edit: false });
  };

  return (
    <div>
      <Dialog open={openDialog?.edit} onClose={handleClose}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Edit Team
              <span style={{ color: "blue" }}>{" - " + Data?.name}</span>
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <div className="row">
              <TextField
                required
                defaultValue={Data?.name}
                className="col m-2"
                {...register("name")}
                label="Name"
                type="text"
                variant="outlined"
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                }}
              />
              <FormControl required fullWidth className="col m-2">
                <InputLabel id="Team-Member">Status</InputLabel>
                <Select
                  {...register("isActive")}
                  label="Status"
                  defaultValue={Data?.isActive}
                >
                  <MenuItem value={"true"}>Active</MenuItem>
                  <MenuItem value={"false"}>In Active</MenuItem>
                </Select>
              </FormControl>
            </div>
            <div className="row">
              <div className="col">
                <InputLabel id="Team-Name">Start Date</InputLabel>
                <TextField
                  required
                  fullWidth
                  defaultValue={Data?.startDate?.slice(0, 10)}
                  {...register("startDate")}
                  type="date"
                  variant="outlined"
                />
              </div>
              <div className="col">
                <InputLabel id="Team-Name">End Date</InputLabel>
                <TextField
                  required
                  fullWidth
                  defaultValue={Data?.endDate?.slice(0, 10)}
                  {...register("endDate")}
                  type="date"
                  variant="outlined"
                />
              </div>
            </div>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="contained" color="error">
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="success"
              disabled={save}
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
          <input {...register("createdBy")} value="user" hidden />
          <input {...register("updatedBy")} value="user" hidden />
          <input {...register("id")} value={Data?.id} hidden />
        </form>
      </Dialog>
    </div>
  );
};
