import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  ListItemIcon,
  Checkbox,
  List,
  Divider,
  TextField,
} from "@mui/material";
import { Link, Navigate, useLocation } from "react-router-dom";
import { Employee } from "../../../Models/Employee/Employee";
import Grid from "@mui/material/Grid";
import React, { useEffect } from "react";
import { Get, Post } from "../../../Services/Axios";
import Swal from "sweetalert2";
import Card from "@mui/material/Card";
import CardHeader from "@mui/material/CardHeader";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import { ADMIN } from "../../../Constants/Common/Roles";
import { TeamEmployee } from "../../../Models/Employee/TeamEmployee";
import BackDrop from "../../../CommonComponents/BackDrop";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { AssignmentMapper } from "../../../Models/Common/AssignmentMapper";
import { Regex } from "../../../Constants/Regex/Regex";

function not(a: readonly Employee[], b: readonly Employee[]) {
  return a.filter((value) => b.indexOf(value) === -1);
}

function intersection(a: readonly Employee[], b: readonly Employee[]) {
  return a.filter((value) => b.indexOf(value) !== -1);
}

function union(a: readonly Employee[], b: readonly Employee[]) {
  return [...a, ...not(b, a)];
}

export const AssignTeamMember = () => {
  const [loading, setLoading] = React.useState(false);
  const [checked, setChecked] = React.useState<Employee[]>([]);
  const [left, setLeft] = React.useState<Employee[]>([]);
  const [right, setRight] = React.useState<Employee[]>([]);
  const [filter, setFilter] = React.useState<Employee[]>([]);
  const location = useLocation();
  const leftChecked = intersection(checked, left);
  const rightChecked = intersection(checked, right);
  const [assignmentMapper, setAssignmentMapper] =
    React.useState<AssignmentMapper>({
      assignedList: [],
      unassignedList: [],
    });
  var assignedList: TeamEmployee[] = [];
  var unassignedList: TeamEmployee[] = [];

  if (!location.state) {
    return <Navigate to="/Admin/EmployeeList" />;
  }

  async function fetchEmployees() {
    setLoading(true);
    const empList: any = await Get<Promise<any>>(
      "app/Employee/GetEmployeeList"
    );
    const teamEmpList: any = await Get(
      `app/Team/GetTeamEmployeelist?teamId=${location.state?.data?.id}`
    );

    let left: Array<Employee> = [];
    let right: Array<Employee> = [];
    empList?.data?.map((e: any) => {
      let condition = teamEmpList?.data?.find(
        (x: any) => x.employeeId === e.id
      );
      if (condition) {
        right.push(e);
      } else {
        left.push(e);
      }
    });
    // for (let i = 0; i < empList.data?.length; i++) {
    //   let temp = teamEmpList.data?.find(
    //     (e: any) => e.employeeId == empList.data[i].id
    //   );

    //   let employee: Employee = {
    //     userId: empList.data[i].id,
    //     name: empList.data[i].user?.name,
    //   };
    //   if (temp) {
    //     right.push(employee);
    //   } else {
    //     left.push(employee);
    //   }
    // }
    setFilter(left || []);
    setRight(right);
    setLeft(left);
    setLoading(false);
  }

  useEffect(() => {
    fetchEmployees();
  }, []);

  function toTeamEmployee(employee: Employee): TeamEmployee {
    return {
      teamId: location.state?.data?.id,
      employeeId: employee.id,
      CreatedBy: ADMIN,
      UpdatedBy: ADMIN,
      startDate: new Date(),
    };
  }

  async function SaveSkill() {
    const { error }: any = await Post(
      "app/Team/AssignEmployeeToTeam",
      assignmentMapper
    );
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Assigning!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Employee Assigned Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      showConfirmButton: true,
    });
    setAssignmentMapper({ assignedList: [], unassignedList: [] });
  }

  const handleToggle = (value: Employee) => () => {
    const currentIndex = checked.indexOf(value);
    const newChecked = [...checked];

    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    setChecked(newChecked);
  };

  const stringOfChecked = (items: readonly Employee[]) =>
    intersection(checked, items).length;

  const handleToggleAll = (items: readonly Employee[]) => () => {
    if (stringOfChecked(items) === items.length) {
      setChecked(not(checked, items));
    } else {
      setChecked(union(checked, items));
    }
  };

  const handleCheckedRight = () => {
    assignedList = [...assignmentMapper.assignedList];
    unassignedList = [...assignmentMapper.unassignedList];
    leftChecked.map((teamEmployee: Employee) => {
      var isAssigned = assignedList.find(
        (x) => x.employeeId === teamEmployee.id
      );
      if (!isAssigned) {
        unassignedList = unassignedList.filter(
          (x) => x.employeeId !== teamEmployee.id
        );
        assignedList.push(toTeamEmployee(teamEmployee));
      }
    });
    setAssignmentMapper({ unassignedList, assignedList });
    setRight(right.concat(leftChecked));
    setLeft(not(left, leftChecked));
    var result: Employee[] = [];
    filter.forEach((e) => {
      if (leftChecked.indexOf(e) === -1) result.push(e);
    });
    setFilter(result);
    setChecked(not(checked, leftChecked));
  };

  const handleCheckedLeft = () => {
    unassignedList = [...assignmentMapper.unassignedList];
    assignedList = [...assignmentMapper.assignedList];
    rightChecked.map((teamEmployee: Employee) => {
      var isNotAssigned = unassignedList.find(
        (x) => x.employeeId == teamEmployee.id
      );
      if (!isNotAssigned) {
        assignedList = assignedList.filter(
          (x) => x.employeeId !== teamEmployee.id
        );
        unassignedList.push(toTeamEmployee(teamEmployee));
      }
    });
    setAssignmentMapper({ assignedList, unassignedList });
    setLeft(left.concat(rightChecked));
    var result: Employee[] = [];
    filter.forEach((e) => {
      if (rightChecked.indexOf(e) === -1) result.push(e);
    });
    setFilter(result);
    setRight(not(right, rightChecked));
    setChecked(not(checked, rightChecked));
  };

  const handleSearch = (key: string) => {
    var temp = filter.filter(
      (e: any) => e.name?.toLowerCase().search(key?.toLowerCase()) >= 0
    );

    setLeft(temp);
  };

  const customList = (title: React.ReactNode, items: readonly any[]) => (
    <Card sx={{mt:'25px'}}>
      <div className="d-flex justify-content-between">
        <CardHeader
          sx={{ px: 2, py: 1 }}
          avatar={
            <>
              <Checkbox
                onClick={handleToggleAll(items)}
                checked={
                  stringOfChecked(items) === items.length && items.length !== 0
                }
                indeterminate={
                  stringOfChecked(items) !== items.length &&
                  stringOfChecked(items) !== 0
                }
                disabled={items.length === 0}
                inputProps={{
                  "aria-label": "all items selected",
                }}
              />
            </>
          }
          title={title}
          subheader={`${stringOfChecked(items)}/${items.length} selected`}
        />
        {title !== "Employees" ? (
          <div>
            <Button variant="contained" className="m-2" onClick={SaveSkill}>
              Save
            </Button>
          </div>
        ) : (
          <div>
            <TextField
              variant="outlined"
              label="Search"
              className="m-3"
              onChange={(event: any) => {
                handleSearch(event.target.value);
              }}
            />
          </div>
        )}
      </div>
      <Divider />
      <List
        sx={{
          width: 700,
          height: 300,
          bgcolor: "background.paper",
          overflow: "auto",
        }}
        dense
        component="div"
        role="list"
      >
        {items.map((value: any, index: number) => {
          const labelId = `transfer-list-all-item-${value}-label`;
          return (
            <ListItem key={index} role="listitem" onClick={handleToggle(value)}>
              <ListItemIcon>
                <Checkbox
                  checked={checked.indexOf(value) !== -1}
                  tabIndex={-1}
                  disableRipple
                  inputProps={{
                    "aria-labelledby": labelId,
                  }}
                />
              </ListItemIcon>
              <ListItemText
                id={labelId}
                primary={`${value.name.replace(Regex.CHAR_SPACE, " ")}`}
              />
            </ListItem>
          );
        })}
      </List>
    </Card>
  );
  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to="/Admin">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Link to="/Admin/Team">
          <Typography sx={{ fontWeight: "bold" }}>Teams</Typography>
        </Link>
        <Link
          to="/Admin/TeamQuadrant"
          state={{
            data: {
              id: location.state?.data?.id,
              name: location.state?.data?.name,
            },
          }}
        >
          <Typography sx={{ fontWeight: "bold" }}>Team Quadrant</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Assign Member</Typography>
      </Breadcrumbs>
      <Typography align="center" className="fw-bolder fs-3">
        Team Name: {location.state?.data?.name}
      </Typography>
      <Grid
        container
        spacing={2}
        justifyContent="space-between"
        alignItems="center"
        className="container m-3 mx-auto d-flex mt-5 mb-5"
      >
        <Grid xs={5}>
        <Grid item>{customList("Employees", left)}</Grid>
        </Grid>
        <Grid xs={2}>
        <Grid item>
          <Grid container direction="column" alignItems="center">
          <Button
              sx={{ my: 0.5 }}
              variant="outlined"
              size="small"
              className="mx-2"
              onClick={handleCheckedRight}
              disabled={leftChecked.length === 0}
              aria-label="move selected right"
            >
              <KeyboardDoubleArrowRightIcon />
            </Button>
            <Button
              sx={{ my: 0.5 }}
              variant="outlined"
              size="small"
              onClick={handleCheckedLeft}
              disabled={rightChecked.length === 0}
              aria-label="move selected left"
            >
               <KeyboardDoubleArrowLeftIcon />
            </Button>
          </Grid>
        </Grid>
        </Grid>
        <Grid xs={5}>
        <Grid item>{customList("Assigned Employees", right)}</Grid>
        </Grid>
      </Grid>
      <BackDrop open={loading} />
    </>
  );
};
