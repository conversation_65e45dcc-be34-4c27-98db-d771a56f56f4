import { TaskDTO } from "../../Models/Task/Task";
import { TaskFilter } from "../../Models/Task/TaskFilter";
import { ConvertToISO, WeekEndingDate } from "../../Utilities/Utils";

export const FILTER: TaskFilter = {
  name: null,
  projectName: null,
  status: null,
  percentage: null,
  category: null,
  subCategory: null,
  actualTime: null,
  estimatedTime: null,
  estStartDate: null,
  estEndDate: null,
  actStartDate: null,
  actEndDate: null,
  taskName: null,
  startDate: null,
  endDate: null,
  priority: null,
  weekEndingDate: null,
  projectType: null,
  teamName: null,
  employeeName: null,
  direction: "FIRST",
  pageNumber: 0,
  pageSize:50
}

export const TASK: TaskDTO = {
  projectId: 0,
  categoryId: 0,
  uiId: 0,
  userStoryId: 0,
  name: "",
  description: "",
  estTime:"",
  weekEndingDate: ConvertToISO(WeekEndingDate()),
  status: "Unassigned",
  priority: "",
  percentage: 0,
  estimateStartDate: "",
  estimateEndDate: "",
  taskType: "",
  classification: "",
  comment: "",
  employeeId: 0,
  weeklyPlanId:0,
  createdBy: "user",
  updatedBy: "user",
};


