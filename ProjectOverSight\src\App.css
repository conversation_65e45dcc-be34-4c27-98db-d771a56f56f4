a {
  text-decoration: none;
}

.squareCard {
  background-color: #c88df2;
  color: black;
}

.rectangleCard {
  color: black;
}

.teamReportCard {
  background-color: rgb(141, 242, 218);
}

.rectangleCard:hover {
  background-color: rgb(237, 233, 233);
  color: blue;
}

.hoverEffect:hover {
  background: #b86af0;
  color: white;
}

.download {
  background: #00d775;
  color: white;
}

.tableStyle {
  font-weight: 500 !important;
  font-size: 16px !important;
}

.MuiDataGrid-row:hover,
.MuiDataGrid-cell:hover {
  background-color: transparent !important;
}

.MuiDataGrid-cell:focus,
.MuiDataGrid-cell:focus-within,
.MuiDataGrid-row:focus .MuiDataGrid-cell,
.MuiDataGrid-row:focus-within .MuiDataGrid-cell {
  background-color: transparent !important;
  outline: none !important;
}

.selected-row {
  background-color: transparent !important;
  outline: none !important;
}

@font-face {
  font-family: "Product Sans";
  font-style: normal;
  font-weight: 400;
  src: local("Open Sans"), local("OpenSans"),
    url(https://fonts.gstatic.com/s/productsans/v5/HYvgU2fE2nRJvZ5JFAumwegdm0LZdjqr5-oayXSOefg.woff2)
      format("woff2");
}

* {
  font-family: "Product Sans";
}

.css-11xur9t-MuiPaper-root-MuiTableContainer-root {
  height: 60vh;
}

.css-1t1j96h-MuiPaper-root-MuiDialog-paper::-webkit-scrollbar {
  display: none;
}

.shadow {
  box-shadow: rgba(14, 30, 37, 0.12) 0px 2px 4px 0px,
    rgba(14, 30, 37, 0.2) 0px 2px 16px 0px;
}

.ui-tooltip,
.arrow:after {
  background: black;
  border: 2px solid white;
}

.ui-tooltip {
  padding: 10px 20px;
  color: white;
  border-radius: 20px;
  font: bold 14px "Helvetica Neue", Sans-Serif;
  text-transform: uppercase;
  box-shadow: 0 0 7px black;
}

.text-dec {
  text-decoration: none;
}

.popup::-webkit-scrollbar {
  display: none;
}

.rdt_Table > .sc-kgfleI {
  display: flex;
  width: 90vw;
  justify-content: center;
}
.swal2-container {
  z-index: 9999 !important;
}

.custom_zindex {
  z-index: 100;
}

.col-12 {
  width: 100%;
}

@media only screen and (min-width: 768px) {
  .col-s-3,
  .col-s-4 {
    width: 100%;
  }

  .dataTableClass .dataTables_paginate {
    position: absolute;
    top: 0;
    width: 94vw;
    background: #f0f3f7;
    color: black;
    text-align: right;
    margin-top: 0;
    height: 5vw;
    border-radius: 5px 5px 0 0;
  }

  .buttonClass {
    position: absolute;
    margin-top: 0;
  }
}

@media only screen and (min-width: 1200px) {
  .dataTableClass .dataTables_paginate {
    height: 8vw;
  }
}

.responsive-div {
  transition: padding-top 0.5s ease;
}
@media screen and (max-width: 1100px) {
  .responsive-div {
    padding-top: 4%;
  }
}

@media screen and (max-width: 779px) {
  .responsive-div {
    padding-top: 8%;
  }
}

@media screen and (max-width: 450px) {
  .responsive-div {
    padding-top: 50px;
  }
}

.blueTopBorder {
  border-top: 3px solid orangered;
}

.greenTopBorder {
  border-top: 3px solid green;
}

.menu {
  border-radius: 26px;
  box-shadow: rgba(14, 30, 37, 0.12) 0px 2px 4px 0px,
    rgba(14, 30, 37, 0.3) 0px 2px 16px 0px;
}

.firstTopBorder {
  border-top: 3px solid rgb(5, 235, 185);
  border-radius: 5px;
}

.pointer {
  cursor: pointer;
}

.heigth {
  height: 60vh;
}

.icon-hover:hover {
  transform: scale(1.2);
  transition: transform 0.3s ease-in-out;
}

.project-toggle {
  right: 1px;
  top: 3px;
}

.assign-hour-toggle {
  right: 1px;
  top: 8px;
}

.bg-grey-500 {
  background-color: rgb(226, 225, 225);
  border-radius: 4px;
  padding: 8px;
}

.loading-cell {
  height: 380px;
}

.fixTableHead {
  overflow-y: auto;
  height: 110px;
}

.fixTableHead thead th {
  position: sticky;
  top: 0;
  background: #bbc2f2;
}

table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  padding: 8px 15px;
  border: 2px solid #dedede;
}

th {
  background: #abdd93;
}

.pop-title-bg {
  background-color: #f0f0f0;
}
/* For desktop */
@media screen and (min-width: 768px) {
  .container {
    /* display: flex; */
    justify-content: center;
  }
  .grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  .item {
    width: calc(100% - 0px);
  }
}

/* For mobile */
@media screen and (max-width: 767px) {
  .grid {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .item {
    width: calc(100% - 20px);
  }
}

.squareCard {
  width: 200px;
  height: 100px;
  margin: 10px;
}

@media (max-width: 768px) {
  .squareCard {
    width: calc(50% - 20px);
    height: 200px;
  }
  .d-flex-inline {
    display: flex;
    flex-wrap: wrap;
  }
}

.voice-recorder-container {
  max-width: 750px;
  margin: 30px auto;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  font-family: "Segoe UI", sans-serif;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.record-btn {
  background-color: #007bff;
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.record-btn.recording {
  background-color: #dc3545;
}

.reset-btn {
  background-color: #6c757d;
  color: #ffffff;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.reset-btn:hover {
  background-color: #5a6268;
}

.output-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.transcript-box {
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  resize: none;
  background-color: #f9f9f9;
}
