import {
  Button,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  TextField,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
export const ViewEmployee = ({ viewEmployee, setviewEmployee, data }: any) => {
  const handleClose = () => {
    setviewEmployee({ view: false });
  };
  return (
    <>
      <Dialog open={viewEmployee?.view}>
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Employee Details
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
          <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                value={data.userId}
                label="User Id"
                fullWidth
                variant="outlined"
                disabled={data.userId ? false : true}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                id="name"
                label="Name"
                value={data?.user?.name}
                variant="outlined"
                disabled={data?.user?.name ? false : true}
              />
            </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                id="phoneNumber"
                value={data.phoneNumber}
                label="Phone Number"
                variant="outlined"
                disabled={data.phoneNumber ? false : true}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                id="secondaryPhoneNumber"
                value={data?.secondaryPhoneNumber}
                label="Secondary Phone Number"
                variant="outlined"
                disabled={data?.secondaryPhoneNumber ? false : true}
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                value={data?.user?.email}
                label="Email"
                variant="outlined"
                disabled={data?.user?.email ? false : true}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                id="secondaryEmail"
                value={data?.secondaryEmail}
                label="Secondary Email"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
                disabled={data?.secondaryEmail ? false : true}
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                value={data?.officialIntime}
                label="Official In Time"
                variant="outlined"
                disabled={data?.officialIntime ? false : true}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                id="secondaryEmail"
                value={data?.officialOuttime}
                label="official Out Time"
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
                disabled={data?.officialOuttime ? false : true}
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                value={data.category}
                label="category"
                fullWidth
                variant="outlined"
                disabled={data.category ? false : true}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                value={data.department}
                label="Department"
                fullWidth
                variant="outlined"
                disabled={data.department ? false : true}
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                value={data.location}
                label="Location"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
                disabled={data.location ? false : true}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                value={data.employeeCode}
                label="Employee Code"
                fullWidth
                variant="outlined"
                disabled={data.employeeCode ? false : true}
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                value={data.manager}
                label="Manager"
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
                disabled={data.manager ? false : true}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                className="read-only-input"
                // margin="dense"
                label="Employee Code"
                fullWidth
                style={{ visibility: "hidden" }}
              />
             </FormControl>
            </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={() => {
                setviewEmployee({ view: false });
              }}
            >
              Ok
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
};
