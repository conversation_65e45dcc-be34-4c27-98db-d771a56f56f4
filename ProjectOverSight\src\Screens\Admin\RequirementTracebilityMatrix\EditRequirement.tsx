
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputLabel,
} from "@mui/material";
import { useEffect, useRef } from "react";
import {
  Alert,
  TextField,
  FormControl,
  MenuItem,
  Select,
  Autocomplete,
  TextareaAutosize,
  Grid,
} from "@mui/material";
import { useState } from "react";
import { useForm } from "react-hook-form";
import AddIcon from "@mui/icons-material/Add";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { Get, Post } from "../../../Services/Axios";
import Swal from "sweetalert2";
import { Regex } from "../../../Constants/Regex/Regex";
import { ConvertToISO } from "../../../Utilities/Utils";
import React from "react";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { useContextProvider } from "../../../CommonComponents/Context";
import { CommonMaster } from "../../../Models/Common/CommonMaster";
import { AddRequirementType } from "./AddRequirementType";

const formField = [
  "RequirementSource",
  "RequirementType",
  "RequirementDefinition",
  "SourceDocumentSpecification",
  "sourceDocument",
  "ProjectName",
  "Stages",
  "Status",
  "EstimatedReleaseDate",
  "Design",
  "Development",
  "Testing",
  "Category",
  "IsActive",
];

const EditRequirement = ({
  openDialog,
  setOpenDialog,
  Data,
  setReload,
  data,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [Projects, setProjects] = useState<any>([]);
  const [save, setSave] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [SelCat,setSelCat] =  useState("");
  const [requirementtype,setrequirementtype] = useState<any>([]);
  const [open, setOpen] = useState<any>({ add: false });
  const { commonMaster } = useContextProvider();
  const [ProjectId, setprojectId] = useState<number>(-1);
  const subCategoryRef = useRef<any>(null);

  var objective: any[] = [];
  data?.data?.projectObjectiveMappings?.forEach((e: any) => {
    if (e?.userStoryId === Data?.id) objective.push(e?.projectObjectiveId);
  });


  const onSubmitHandler = async (data: any) => {
    debugger
    data.Id = Data.id;
    data.Status = Data.status;
    data.Stages = data.Design == 'In Progress' ? "Design" : (data.Development == 'In Progress'? "Development": (data.Testing == 'In Progress' ? "Testing" : "UAT"))
    data.ProjectId = ProjectId > -1 ? ProjectId : Data?.projectId;
    data.Category = !data.Category ? Data.category : data.Category;
    data.SubCategory = !data.SubCategory ? Data.subCategory : data.SubCategory;
    data.RequirementType = ! data.RequirementType ? Data?.requirementType : data.RequirementType;
    data.RequirementNumberAsPerSourceDocument = data.RequirementNumberAsPerSourceDocument 
    ? Number(data.RequirementNumberAsPerSourceDocument) 
    : Number(Data?.requirementNumberAsPerSourceDocument);
    data.EstimatedReleaseDate = data.EstimatedReleaseDate? data.EstimatedReleaseDate : null;
    data.UpdatedBy = "user";
    data.CreatedBy = "user";
    data.IsActive = SelCat == "No"? false : true;
    setSave(true);
    const response: any  = await Post(
      "app/Requirement/UpdateRequirements",
      data
    );

    var option: AlertOption;

    if (response.error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Requirement Updated Successfully!",
        icon: "success",
      };
    }
    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      let requirementList = Get(`app/Requirement/GetRequirements`);
      requirementList.then(() => {
        setReload((prev: boolean) => !prev);
      });
    });
    handleClose();
  };

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ edit: false });
  };

  const handleProjectChange = async (e: any, selectedOption: any) => {
    if (!selectedOption || !selectedOption.id || !e) {
      return;
    }

    const { id } = selectedOption;
    setSelectedProject(selectedOption);
    setprojectId(id);
  };

  useEffect(() => {
    async function getProject() {
      let projectGet: any = await Get(`app/Project/GetProjectList`);
      setProjects(projectGet.data);
    }
    getProject();
  }, []);

  useEffect(() => {
        const fetchRequirementType = async () => {
            let Getrequirementtype: any = await Get(`app/Requirement/GetRequirementType?ProjectId=${Data?.projectId ? Data?.projectId : selectedProject.id}`);
            setrequirementtype(Getrequirementtype?.data);
        };
        fetchRequirementType();
}, [selectedProject,setReload,open,openDialog?.edit,Data?.projectId]);

  console.log(Data);

  return (
    <div>
      <Dialog open={openDialog?.edit}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Edit Requirements
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
              <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <Autocomplete
                      id="project-name"
                      options={Projects?.map((project: any) => ({
                        label: project?.name,
                        id: project?.id,
                      }))}
                      // disabled={Data?.isActive === false }
                      defaultValue={Data?.projectName}
                      onChange={handleProjectChange}
                      renderInput={(params: any) => {
                        return (
                          <TextField
                            {...params}
                            // {...register("ProjectName")}
                            label="Project Name"
                            variant="outlined"
                            required
                          />
                        );
                      }}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <TextField
                      required
                      label="Requirement Source"
                      type="text"
                      variant="outlined"
                      // disabled={Data?.isActive === false }
                      defaultValue={Data?.requirementSource}
                      {...register("RequirementSource")}
                      onChange={(e: any) => {
                        e.target.value = e.target.value.replace(
                          Regex.CHARACTER,
                          ""
                        );
                      }}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <TextField
                      required
                      label="Source Document "
                      type="text"
                      variant="outlined"
                      // disabled={Data?.isActive === false }
                      defaultValue={Data?.sourceDocument}
                      {...register("SourceDocument")}
                     
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    label="RequirementNumberAsPerSourceDocument"
                    type="Number"
                    variant="outlined"
                    // disabled={Data?.isActive === false }
                    defaultValue={Data?.requirementNumberAsPerSourceDocument}
                    {...register("RequirementNumberAsPerSourceDocument")}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
  e.target.value = e.target.value.replace(Regex.NUMBER, "");
}}

                  />
                </FormControl>
              </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                  <Autocomplete
                  options={requirementtype?.length > 0 ? (requirementtype?.map((objective: any) => ({
                    label: objective?.requirementsType,
                    id: objective?.id,
                  }))): []}
                  // disabled={Data?.isActive === false }
                  defaultValue={Data?.requirementType}
                  renderInput={(params: any) => (
                    <TextField
                      {...params}
                      label="Requirement Type"
                      variant="outlined"
                      {...register("RequirementType")}
                    />
                  )}
                />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <Button
              //  disabled={Data?.isActive === false }
                variant="contained"
                onClick={() => setOpen({ add: true })}
              >
                Add Requirement Type
                <AddIcon className="mx-1" />
              </Button>
              </FormControl>
            </Grid>
                
                <Grid item xs={12}>
                <FormControl fullWidth>
                  <TextareaAutosize
                    required
                    // disabled={Data?.isActive === false }
                    placeholder="Requirement Definition"
                    defaultValue={Data?.requirementDefinition}
                    {...register("RequirementDefinition")}
                    onChange={(e: any) => {
                      e.target.value = e.target.value.repalce(
                        Regex.CHAR_NUM,
                        ""
                      );
                    }}
                    style={{ width: "100%", height: 100 }}
                  />
                  </FormControl>
                </Grid>
              
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <Autocomplete
                  id="Requirement"
                  // disabled={Data?.isActive === false }
                  options={commonMaster
                    ?.filter((x: CommonMaster) => x.codeType === "Requirement")
                    .filter(
                      (obj: any, index: number, self: any) =>
                        index ===
                        self.findIndex((t: any) => t.codeName === obj.codeName)
                    )
                    .map((master: CommonMaster) => ({
                      label: master?.codeName,
                      id: master?.id,
                    }))}
                  defaultValue={Data?.category}
                  renderInput={(params: any) => {
                    return (
                      <TextField
                        {...params}
                        {...register("Category")}
                        label="Requirement Category"
                        variant="outlined"
                        required
                      />
                    );
                  }}
                />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                    <Autocomplete
                    ref={subCategoryRef} // Assign the ref directly here
                    fullWidth
                    // disabled={Data?.isActive === false }
                    options={[
                      {
                        label: "New Application",
                        value: "New Application",
                      },
                      {
                        label: "Renewal",
                        value: "Renewal",
                      },
                      {
                        label: "Review",
                        value: "Review",
                      },
                      {
                        label: "Seek clarification",
                        value: "Seek clarification",
                      },
                      {
                        label: "Administration",
                        value: "Administration",
                      },
                      {
                        label: "Othres",
                        value: "Othres",
                      },
                    ]}
                    defaultValue={Data?.subCategory}
                    renderInput={(params: any) => (
                      <TextField
                        {...params}
                        {...register("SubCategory")}
                        label="Requirement Sub Category"
                        variant="outlined"
                      />
                    )}
                  />
                 </FormControl>
              </Grid>
                <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="outlined-read-only-input-label">
                    Design
                  </InputLabel>
                  <Select
                    labelId="outlined-read-only-input-label"
                    id="outlined-read-only-input"
                    defaultValue={Data?.design}
                    {...register("Design")}
                    label="Design"
                    // disabled={Data?.isActive === false }
                    displayEmpty
                  >
                    <MenuItem value="In Progress">In Progress</MenuItem>
                    <MenuItem value="Completed">Completed</MenuItem>
                  </Select>
                </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="outlined-read-only-input-label">
                    Development
                  </InputLabel>
                  <Select
                    labelId="outlined-read-only-input-label"
                    id="outlined-read-only-input"
                    defaultValue={Data?.development}
                    {...register("Development")}
                    label="Development"
                    // disabled={Data?.isActive === false }
                    displayEmpty
                  >
                    <MenuItem value="In Progress">In Progress</MenuItem>
                    <MenuItem value="Completed">Completed</MenuItem>
                  </Select>
                </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="outlined-read-only-input-label">
                    Testing
                  </InputLabel>
                  <Select
                    labelId="outlined-read-only-input-label"
                    id="outlined-read-only-input"
                    defaultValue={Data?.testing}
                    {...register("Testing")}
                    label="Testing"
                    // disabled={Data?.isActive === false }
                    displayEmpty
                  >
                    <MenuItem value="In Progress">In Progress</MenuItem>
                    <MenuItem value="Completed">Completed</MenuItem>
                  </Select>
                </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel id="Is-Active">Is Active</InputLabel>
                    <Select
                      labelId="Is-Active"
                      defaultValue={Data?.isActive == true? "Yes":"No"}
                      required
                      label="Is Active"
                      {...register("IsActive", {
                        onChange: (e: any) => {
                          console.log(e.target.value);
                          
                          setSelCat(e.target.value);
                          setErrorMsg({
                            message: "",
                            show: false,
                          });
                        },
                      })}
                    >
                      <MenuItem value={"Yes"}>Yes</MenuItem>
                      <MenuItem value={"No"}>No</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <InputLabel>Release Date</InputLabel>
                  <FormControl fullWidth>
                    <TextField
                      type="date"
                      // disabled={Data?.isActive === false }
                      defaultValue={ConvertToISO(`${Data?.estimatedReleaseDate}`)}
                      // label="Release Date"
                      variant="outlined"
                      {...register("EstimatedReleaseDate")}
                    />
                  </FormControl>
                </Grid>
                </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              size="medium"
              variant="contained"
              color="error"
              onClick={handleClose}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              color="success"
              type="submit"
              disabled={save}
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <AddRequirementType
        openDialog={open}
        setOpenDialog={setOpen}
        ProjectId={Data?.projectId}
        SetReload={setReload}
      />
    </div>
  );
};

export default EditRequirement;
