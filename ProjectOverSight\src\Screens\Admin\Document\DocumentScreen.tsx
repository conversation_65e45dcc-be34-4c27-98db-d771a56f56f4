import { useState, useEffect, useRef } from "react";
import { ConvertDate } from "../../../Utilities/Utils";
import Button from "@mui/material/Button";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import RefreshIcon from "@mui/icons-material/Refresh";
import Pagination from "@mui/material/Pagination";
import {
  Typography,
  Tooltip,
  Stack,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import Select from "react-select";
import { Get, Post } from "../../../Services/Axios";
import { Link } from "react-router-dom";
import "../../../StyleSheets/ProjectList.css";
import { useContextProvider } from "../../../CommonComponents/Context";
import BookmarkBorderIcon from "@mui/icons-material/BookmarkBorder";
import BookmarkIcon from "@mui/icons-material/Bookmark";
import BackDrop from "../../../CommonComponents/BackDrop";
import { styled, alpha } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import { SearchIcon } from "lucide-react";
import React from "react";
import { EmployeeProject } from "../../../Models/Employee/EmployeeProject";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { AssignEmployeeModal } from "../Projects/AssignEmployeeModal";

const Search = styled("div")(({ theme }) => ({
  position: "relative",
  borderRadius: theme.shape.borderRadius,
  backgroundColor: alpha(theme.palette.common.white, 0.15),
  "&:hover": {
    backgroundColor: alpha(theme.palette.common.white, 0.25),
  },
  marginRight: theme.spacing(2),
  marginLeft: 0,
  width: "100%",
  [theme.breakpoints.up("sm")]: {
    marginLeft: theme.spacing(3),
    width: "auto",
  },
}));

const SearchIconWrapper = styled("div")(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: "100%",
  position: "absolute",
  pointerEvents: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: "inherit",
  "& .MuiInputBase-input": {
    padding: theme.spacing(1, 1, 1, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create("width"),
    width: "100%",
    [theme.breakpoints.up("md")]: {
      width: "20ch",
    },
  },
}));

export const DocumentScreen = () => {
  const [rows, setRows] = useState<any>([]);
  const [ColorCode, setColorCode] = useState<any>([]);
  const [loading, setLoading] = useState<any>(true);
  const [reload, setReload] = useState<boolean>(true);
  const [filterRows, setfilterRows] = useState<any>([]);
  const [filter, setfilter] = useState<any>({});
  // const [documentView, setDocumentView] = useState<any>({
  //   view: false,
  //   edit: false,
  //   add: false,
  //   viewObj: false,
  // });
  var ProjectSet = new Set<any>();
  const { role } = useContextProvider();
  const label = { inputProps: { "aria-label": "Checkbox demo" } };
  const [searchTerm, setSearchTerm] = useState("");
  const statusRef = useRef<any>();
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const projectNameRef = useRef<HTMLInputElement>(null);
  const [showModal, setShowModal] = useState(false);
  const [lefts, setLeft] = React.useState<EmployeeProject[]>([]);
  const [rights, setRight] = React.useState<EmployeeProject[]>([]);
  const [projectId, setProjectId] = useState<number>(-1);
  const [rowsPerPage, setRowsPerPage] = React.useState(6);
  const [page, setPage] = React.useState(1);

  const handleChangePage = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
    return event;
  };

  const options = [
    { value: 6, label: "6" },
    { value: 10, label: "10" },
    { value: 20, label: "20" },
  ];

  const handleChangeRowsPerPage = (
    selectedOption: { value: number; label: string } | null
  ) => {
    if (selectedOption) {
      setRowsPerPage(selectedOption.value);
      setPage(1);
    }
  };

  // const handleClickOpen = () => {
  //   setDocumentView({ add: true });
  // };

  const handleShowModal = async (rowId: number) => {
    setLoading(true);
    const employees: any = await Get<Promise<any>>(
      "app/Employee/GetEmployeeList"
    );
    const employeeProject: any = await Get<Promise<any>>(
      `app/Employee/GetEmployeeProject?projectId=${rowId}`
    );
    setProjectId(rowId);
    let unassigned: EmployeeProject[] = [];
    let assigned: EmployeeProject[] = [];

    employees?.data?.map((e: any) => {
      let condition = employeeProject?.data?.find(
        (x: any) => x.employeeId === e.id
      );
      if (condition) {
        e.name = e.name.replace(/[^A-Za-z^ -]/g, "");
        assigned.push(e);
      } else {
        e.name = e.name.replace(/[^A-Za-z^ -]/g, "");
        unassigned.push(e);
      }
    });
    setLeft(unassigned);
    setRight(assigned);
    setLoading(false);
    setShowModal(true);
  };

  handleShowModal(0);

  rows?.forEach((row: any) => {
    ProjectSet.add(row?.projectName);
  });
  rows?.forEach((row: any) => {
    ProjectSet.add(row?.projectName);
  });

  useEffect(() => {
    let projectList = Get("app/Project/GetProjectList");
    projectList.then((response: any) => {
      setRows(response?.data || []);
      setfilterRows(response?.data || []);
      setLoading(false);
    });
    let ColorCodeList = Get("app/CommonMaster/GetColorCodes");
    ColorCodeList.then((response: any) => {
      setColorCode(response?.data || []);
      setLoading(false);
    });
  }, [reload]);

  function ApplyFilter() {
    let temp: any = [];

    if (filter.actStartDate != null) {
      if (filter.actEndDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].startDate.slice(0, 10) >= filter.actStartDate &&
          rows[i].endDate.slice(0, 10) <= filter.actEndDate
        ) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (searchTerm.trim() !== "") {
      temp = temp.filter((e: any) =>
        e?.name?.toLowerCase()?.includes(searchTerm.toLowerCase())
      );
      setfilterRows(temp);
      setPage(1);
    }

    if (filter.status != null) {
      temp = temp.filter((e: any) => {
        return e.status.toLowerCase() === filter.status?.toLowerCase();
      });
      setfilterRows(temp);
      setPage(1);
    }
  }

  function getColor(percentage: any) {
    try {
      const colorMappings = ColorCode;
      let left = 0; //binarysearch
      let right = colorMappings.length - 1;

      while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        const minPercentage = colorMappings[mid].minPercentage;
        const maxPercentage = colorMappings[mid].maxPercentage;

        if (percentage >= minPercentage && percentage <= maxPercentage) {
          return colorMappings[mid].color;
        } else if (percentage < minPercentage) {
          right = mid - 1;
        } else {
          left = mid + 1;
        }
      }

      // Default to green if percentage is outside defined ranges
      return "green";
    } catch (error) {
      console.error("Error determining color:", error);
      return "green"; // Default to green in case of error
    }
  }

  function reset() {
    setfilter({});
    if (statusRef.current) statusRef.current.clearValue();
    if (projectNameRef.current) projectNameRef.current.value = "";
    setSearchTerm("");
    setfilterRows(rows);
  }

  function stringToColor(string: string) {
    let hash = 0;

    for (let i = 0; i < string.length; i++) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (let i = 0; i < 3; i++) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }
    return color;
  }

  function stringAvatar(name: string) {
    if (!name) {
      return null;
    }
    var names = name ? name : "";
    return {
      sx: {
        bgcolor: stringToColor(name),
      },
      children: `${names[0]}`.toUpperCase(),
    };
  }
  stringAvatar("dh");
  const handleCheckboxChange = async (row: any) => {
    setLoading(true);
    let updatedRow = { ...row };
    const someValueToUpdate = row.projectWatchLists !== null ? null : 1;
    if (someValueToUpdate === 1) {
      const dataToSend = {
        projectId: row.id,
        crudValue: "Add",
      };
      const response: any = await Post(
        "app/Project/AddProjectWatchList",
        dataToSend
      );
      updatedRow = {
        ...updatedRow,
        projectWatchLists: {
          ...updatedRow.projectWatchLists,
          projectId: response.data.projectId,
          userId: response.data.userId,
          id: response.data.id,
        },
      };
    } else {
      const dataToSend = {
        projectId: row.id,
        crudValue: "Update",
        id: updatedRow.projectWatchLists.id,
      };

      await Post("app/Project/AddProjectWatchList", dataToSend);
      updatedRow.projectWatchLists = null;
    }
    const updatedRows = rows.map((r: any) => {
      return r.id === row.id ? updatedRow : r;
    });
    setRows(updatedRows);
    setfilterRows(updatedRows);
    setLoading(false);
  };

  const handleMyProjectsCheckboxChange = async (isChecked: boolean) => {
    setLoading(true);
    var projectList: any;
    if (isChecked) {
      projectList = await Get("app/Project/GetProjectWatchList");
    } else {
      projectList = await Get("app/Project/GetProjectList");
    }
    setRows(projectList?.data || []);
    setfilterRows(projectList?.data || []);
    setLoading(false);
  };

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>DocumentDashboard</Typography>
      </Breadcrumbs>
      <div
        className="mx-4 shadow rounded-5 p-3 mt-3"
        style={{
          height: "100vh",
          background: "white",
          overflowY: "hidden",
          overflowX: "hidden",
        }}
      >
        <div
          className="container top-div pt-3"
          style={{ margin: "auto", borderRadius: "10px" }}
        >
          <div
            className="d-flex"
            style={{
              width: "100%",
              margin: "auto",
              justifyContent: "flex-start",
              alignItems: "center",
            }}
          >
            <div
              className="form-group col-3"
              style={{
                borderRadius: "10px",
                boxShadow: "rgba(0, 0, 0, 0.15) 0px 5px 15px 0px",
              }}
            >
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="status"
                ref={statusRef}
                className="select-dropdown"
                onInputChange={(inputValue: string) => {
                  const alphabeticValue = inputValue.replace(
                    /[^A-Za-z\s]/g,
                    ""
                  );
                  return alphabeticValue;
                }}
                onChange={(selectedOption: any) => {
                  if (selectedOption) {
                    setfilter((prevState: any) => ({
                      ...prevState,
                      status:
                        selectedOption.label.trim() === ""
                          ? null
                          : selectedOption.label,
                    }));
                  }
                }}
                options={[
                  {
                    label: "Completed",
                    value: "Completed",
                  },
                  {
                    label: "In Progress",
                    value: "In Progress",
                  },
                  {
                    label: "Pending",
                    value: "Pending",
                  },
                ]}
                placeholder="Status"
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
            <div
              className="col-4 mx-2"
              style={{
                borderRadius: "10px",
                boxShadow: "rgba(0, 0, 0, 0.15) 0px 5px 15px 0px",
              }}
            >
              <Search className="m-0">
                <SearchIconWrapper>
                  <SearchIcon />
                </SearchIconWrapper>
                <StyledInputBase
                  inputProps={{ "aria-label": "search" }}
                  placeholder="Search by Project Name"
                  ref={projectNameRef}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  sx={{
                    border: "2px solid #bfbaba",
                    borderRadius: "10px",
                    width: "100%",
                  }}
                />
              </Search>
            </div>

            <div className="mx-3">
              <Button
                variant="contained"
                size="medium"
                className=""
                onClick={ApplyFilter}
              >
                <Tooltip title={"Search"}>
                  <SearchIcon />
                </Tooltip>
              </Button>
              <Button
                variant="contained"
                size="medium"
                className="mx-2"
                onClick={() => reset()}
              >
                <Tooltip title={"Reset"}>
                  <RefreshIcon />
                </Tooltip>
              </Button>
            </div>
          </div>

          <div
            className="mt-3 mb-3 d-flex justify-center "
            style={{
              justifyContent: "space-between",
              // border: '1px solid lightgrey',
              borderRadius: "10px",
              boxShadow: "rgba(0, 0, 0, 0.15) 0px 5px 15px 0px",
            }}
          >
            <div className="col-2 mx-5">
              <div>
                <FormControlLabel
                  control={
                    <Checkbox
                      onChange={(e) =>
                        handleMyProjectsCheckboxChange(e.target.checked)
                      }
                    />
                  }
                  label="Watch List"
                />
              </div>
              <div>
                <p>Total Projects : {filterRows?.length}</p>
              </div>
            </div>

            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <div
                style={{
                  width: "90%",
                  display: "flex",
                  justifyContent: "end",
                  alignItems: "center",
                }}
              >
                <p className="text-muted mt-2 mx-1">Rows per page : </p>
                <Select
                  value={{ value: rowsPerPage, label: rowsPerPage.toString() }}
                  options={options}
                  onChange={handleChangeRowsPerPage}
                  aria-label="Rows per page"
                />
              </div>
              <Stack className="mx-5" spacing={2} style={{ width: "100%" }}>
                <Pagination
                  count={Math.ceil(filterRows?.length / rowsPerPage)}
                  page={page}
                  onChange={handleChangePage}
                  color="primary"
                />
              </Stack>
            </div>
          </div>
        </div>

        <div
          className="row mt-1 overflow-y-hidden overflow-scroll"
          style={{ minHeight: "65vh", maxHeight: "100vh" }}
        >
          {filterRows?.length > 0 ? (
            filterRows
              .slice((page - 1) * rowsPerPage, page * rowsPerPage)
              .map((row: any, index: number) => (
                <div className="col-lg-4 col-md-12 box" key={index}>
                  <div
                    className="shadow  m-2 mt-3 pt-2 redTopBorder bg-light"
                    style={{ height: 230 }}
                  >
                    <div className="progress mx-4 ">
                      <div
                        className="progress-bar"
                        style={{
                          width: `${row.percentage}%`,
                          backgroundColor: getColor(row.percentage),
                        }}
                        aria-valuenow={row.percentage}
                        aria-valuemin={0}
                        aria-valuemax={100}
                      >
                        {row.percentage}%
                      </div>
                    </div>
                    <div className="mx-4 ">
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <div
                          style={{
                            fontSize: "18px",
                            lineHeight: "28px",
                            color: "#6777ef",
                            paddingRight: "10px",
                            fontWeight: "900",
                            marginTop: "3%",
                          }}
                          title={row.name.length > 17 ? row.name : ""}
                        >
                          <a className="underline-on-hover">
                            <Link
                              to={`/${role}/DocumentOverView`}
                              state={{
                                projectId: row?.id,
                                projectName: row?.name,
                                projectuserstory: row,
                                startDate: row?.startDate,
                                endDate: row?.endDate,
                                status: row?.status,
                                route: "projectList",
                              }}
                            >
                              {row.name.length > 17
                                ? ` ${row.name.slice(0, 17)}...`
                                : ` ${row.name}`}
                            </Link>
                          </a>{" "}
                          - (<small className="text-dark">{row.type}</small>)
                        </div>

                        <div className="mt-1">
                          <Checkbox
                            {...label}
                            icon={<BookmarkBorderIcon />}
                            checkedIcon={<BookmarkIcon />}
                            checked={row.projectWatchLists !== null}
                            onChange={() => handleCheckboxChange(row)}
                          />
                        </div>
                      </div>
                      <div>
                        <p>
                          {row.description.length > 80 ? (
                            <Tooltip title={row.description}>
                              <p>{` ${row.description.slice(0, 80)}...`}</p>
                            </Tooltip>
                          ) : (
                            ` ${row.description}`
                          )}
                        </p>
                      </div>
                      <div className="mt-2">
                        <div>
                          <span className="badge badge-primary text-uppercase ">
                            {row.status}
                          </span>
                          <span className="float-right ">
                            <span> Start Date : </span>
                            <b>{ConvertDate(row.startDate)} </b>
                          </span>
                        </div>
                        <div className="float-right">
                          <span> End Date : </span>
                          <b>{ConvertDate(row.endDate)} </b>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
          ) : (
            <h6 className="mt-3 d-flex align-items-center justify-content-center">
              <ErrorOutlineIcon className="mx-1" />
              No Data
            </h6>
          )}
        </div>
      </div>
      <BackDrop open={loading} />
      {/* </div> */}

      {showModal && (
        <AssignEmployeeModal
          showModal={showModal}
          lefts={lefts}
          rights={rights}
          setRight={setRight}
          setShowModal={setShowModal}
          projectId={projectId}
          setReload={setReload}
        />
      )}
    </>
  );
};
