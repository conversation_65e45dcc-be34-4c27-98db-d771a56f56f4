import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import "./index.css";
import { QueryClient, QueryClientProvider } from "react-query";
import { ContextProvider } from "./CommonComponents/Context";

const queryClient = new QueryClient();

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <BrowserRouter>
    <React.StrictMode>
      <QueryClientProvider client={queryClient}>
        <ContextProvider>
          <App />
        </ContextProvider>
      </QueryClientProvider>
    </React.StrictMode>
  </BrowserRouter>
);
