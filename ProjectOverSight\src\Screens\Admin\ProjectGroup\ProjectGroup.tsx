import {
  <PERSON><PERSON>,
  <PERSON><PERSON>c<PERSON><PERSON>,
  <PERSON>rid,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import { Link } from "react-router-dom";
import { useContextProvider } from "../../../CommonComponents/Context";
import projectgroup from "../../../assets/projectgroup.webp";
import { Card, CardContent, CardMedia } from "@mui/material";
import { AlertCircle, Edit, SearchIcon } from "lucide-react";
import { styled, alpha } from "@mui/material/styles";
import { useEffect, useState } from "react";
import AddProjectGroup from "./AddProjectGroup";
import AddIcon from "@mui/icons-material/Add";
import { Get } from "../../../Services/Axios";
import { Regex } from "../../../Constants/Regex/Regex";
import { TProjectGroup } from "../../../Models/Project/ProjectGroup";
import { AssignProject } from "./AssignProject";
import { Project } from "../../../Models/Project/Project";
import AddCircle from "../../../assets/Add-Circle.svg";
import { EditGroup } from "./EditGroup";

const Search = styled("div")(({ theme }) => ({
  position: "relative",
  backgroundColor: alpha(theme.palette.common.white, 0.15),
  marginLeft: 0,
  width: "100%",
  background: "#13a4ba",
  borderRadius: "30px",
  [theme.breakpoints.up("sm")]: {
    marginLeft: theme.spacing(1),
    width: "auto",
  },
}));

const SearchIconWrapper = styled("div")(({ theme }) => ({
  padding: theme.spacing(1, 2),
  height: "100%",
  position: "absolute",
  pointerEvents: "none",
  alignItems: "center",
  justifyContent: "center",
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: "inherit",
  "& .MuiInputBase-input": {
    padding: theme.spacing(1, 1, 1, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create("width"),
    width: "100%",
    [theme.breakpoints.up("sm")]: {
      width: "15ch",
      "&:focus": {
        width: "20ch",
      },
    },
  },
}));

export const ProjectGroup = () => {
  const { role } = useContextProvider();
  const [searchTerm, setSearchTerm] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [reload, setReload] = useState<boolean>(true);
  const [filterRows, setfilterRows] = useState<any>([]);
  const [projectGroup, setProjectGroup] = useState<TProjectGroup[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const [edit, SetEdit] = useState<boolean>(false);
  const [left, setLeft] = useState<Project[]>([]);
  const [right, setRight] = useState<Project[]>([]);
  const [data, setData] = useState<TProjectGroup>();
  const [projectGroupId, setProjectGroupId] = useState<number | undefined>(0);

  const closeModal = () => {
    setIsModalOpen(false);
  };

  async function AssignProjects(projects: Project[] | undefined) {
    var projectList: any = await Get("app/Project/GetProjectList");
    let unassigned: Project[] = [];
    let assigned: Project[] = [];

    projectList.data.map((obj: Project) => {
      let condition = projects?.find((x: Project) => x.id === obj.id);
      if (condition) {
        assigned.push(obj);
      } else {
        unassigned.push(obj);
      }
    });

    setLeft(unassigned);
    setRight(assigned);
    setOpen(true);
  }

  function stringToColor(string: string) {
    let hash = 0;

    for (let i = 0; i < string.length; i++) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (let i = 0; i < 3; i++) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }
    return color;
  }

  function stringAvatar(name: string) {
    if (!name) {
      return null;
    }
    var names = name ? name : "";
    return {
      sx: {
        bgcolor: stringToColor(name),
      },
      children: `${names[0]}`.toUpperCase(),
    };
  }

  async function fetchData() {
    let response: any = await Get("app/Project/GetProjectgrouplist");
    setProjectGroup(response?.data || []);
    setfilterRows(response?.data || []);
  }

  useEffect(() => {
    fetchData();
  }, [reload]);

  const handleSearchChange = (event: any) => {
    var filteredData = filterRows.filter(
      (e: any) =>
        e.name?.toLowerCase().search(event.target.value?.toLowerCase()) >= 0
    );
    setProjectGroup(filteredData);
    setSearchTerm(event.target.value);
  };

  function calPercetage(projects: Project[]) {
    var totalPercentage = 0;
    projects.forEach((e) => {
      totalPercentage += e.percentage!;
    });
    return totalPercentage / projects.length;
  }

  function handleEdit(projectGroup: TProjectGroup) {
    setData(projectGroup);
    SetEdit(true);
  }

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Project Group</Typography>
      </Breadcrumbs>
      <Grid className="d-flex m-4">
        <button
          onClick={() => setIsModalOpen(true)}
          className="btn btn-primary"
        >
          Create for Group
          <AddIcon className="mx-1" />
        </button>

        <Search
          sx={{
            backgroundColor: "whitesmoke",
          }}
          className="shadow border border-2 mx-auto w-25 p-1"
        >
          <SearchIconWrapper>
            <SearchIcon />
          </SearchIconWrapper>
          <StyledInputBase
            placeholder="Search Groups"
            inputProps={{ "aria-label": "search" }}
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </Search>
      </Grid>
      <Grid container className="col-s-7 mt-4 heigth">
        {projectGroup.length === 0 ? (
          <div className="fs-4 mx-auto h-100 d-flex align-items-center">
            <AlertCircle className="mx-1" />
            <b>No Data</b>
          </div>
        ) : (
          projectGroup.map((data: TProjectGroup) => (
            <Card
              className="mt-3 mx-3"
              style={{ width: "25rem", height: "23rem" }}
              key={data.id}
            >
              <CardMedia
                className="my-1"
                component="img"
                alt="Project Image"
                style={{ width: "25rem", height: "10rem" }}
                src={projectgroup}
              />
              <CardContent>
                <Typography
                  variant="h5"
                  component="div"
                  sx={{ fontWeight: "bold" }}
                >
                  {data.name}
                </Typography>
                <Typography
                  variant="h6"
                  color="text.secondary"
                  className="mt-2"
                >
                  {data.description}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mt-2"
                >
                  Overall Percentage: {calPercetage(data.projects)}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mt-2"
                >
                  Projects
                </Typography>
              </CardContent>
              <Stack direction="row" spacing={2} className="mx-3">
                {data.projects
                  ?.slice(0, 6)
                  .map((projects: Project, index: any) => (
                    <Tooltip
                      key={index}
                      title={
                        projects?.name?.replace(Regex.CHAR_SPACE, " ") || ""
                      }
                    >
                      <Avatar
                        key={index}
                        {...stringAvatar(`${projects?.name}`)}
                        style={{
                          width: "30px",
                          height: "30px",
                          fontSize: "medium",
                        }}
                      />
                    </Tooltip>
                  ))}
                {data.projects.length > 6 && (
                  <span className="project_remaining_user">
                    <b> + {data.projects?.length - 6}</b>
                  </span>
                )}

                <a
                  title="Add Project"
                  onClick={() => {
                    setProjectGroupId(data?.id);
                    AssignProjects(data.projects);
                  }}
                >
                  <img
                    className="assignee__avatar p-1 cursor-pointer"
                    src={AddCircle}
                    alt="Add Icon"
                  />
                </a>
                <Edit
                  className="float-end pointer"
                  onClick={() => handleEdit(data)}
                />
              </Stack>
            </Card>
          ))
        )}
      </Grid>
      <AddProjectGroup
        isOpen={isModalOpen}
        onClose={closeModal}
        setReload={setReload}
      />
      {open && (
        <AssignProject
          show={open}
          setShow={setOpen}
          left={left}
          right={right}
          setReload={setReload}
          projectGroupId={projectGroupId}
        />
      )}

      <EditGroup
        show={edit}
        setShow={SetEdit}
        projectGroup={data}
        setReload={setReload}
      />
    </>
  );
};

export default ProjectGroup;
