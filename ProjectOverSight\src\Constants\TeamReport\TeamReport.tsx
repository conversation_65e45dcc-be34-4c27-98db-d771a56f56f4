import { TaskListDto } from "../../Models/Task/Task";
import { TeamDetailsVM, TeamFilter, TeamReportVM } from "../../Models/Team/Team";

export const TEAM_DETAILS: any = [
    {
      field: "id",
      name: "SL.NO",
      width: "5rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row:any, index: number) => (
        <p className={`tableStyle ${row.name}`}>{index + 1}</p>
      ),
    },
    {
      field: "name",
      name: "Project Name",
      width: "20rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row:TeamDetailsVM) => <p className="tableStyle">{row.projectName}</p>,
    },
    {
      field: "taskDescription",
      name: "Team Member Name",
      width: "19rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TeamDetailsVM) => (
        <p className="tableStyle">{row.employeeName?.replace(/[^A-Za-z ]/g, "")}</p>
      ),
    },
    {
      field: "uIName",
      name: "Total Tasks",
      width: "13rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TeamDetailsVM) => <p className="tableStyle">{row.totalTaskCount}</p>,
    },
    {
      field: "teamName",
      name: "Employee Actual Hrs",
      width: "13rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TeamDetailsVM) => (
        <p className="tableStyle">{row.totalActualHrs}</p>
      ),
    },
  ];

  export const TODAYSTASK_LIST: any = [
    {
      field: "SL.NO",
      name: "SL.NO",
      width: "5rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any, index: number) => (
        <p className={`tableStyle ${row.name}`}>{index + 1}</p>
      ),
    },
    {
      field: "name",
      name: "Task Name",
      width: "30rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => <p className="tableStyle">{row.name}</p>,
    },
    {
      field: "description",
      name: "Description",
      width: "80rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => (
        <p className="tableStyle">{row.description}</p>
      ),
    },
    {
      field: "usName",
      name: "User Story",
      width: "12rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => <p className="tableStyle">{row.usName}</p>,
    },
    {
      field: "uiName",
      name: "User Interface",
      width: "12rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => <p className="tableStyle">{row.uiName}</p>,
    },
    {
      field: "teamName",
      name: "Team Name",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => (
        <p className="tableStyle">{row.teamName}</p>
      ),
    },
    {
      field: "employeeName",
      name: "Employee Name",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => (
        <p className="tableStyle">
          {row.employeeName?.replace(/[^A-Za-z ]/g, "")}
        </p>
      ),
    },
    {
      field: "category",
      name: "Category",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => (
        <p className="tableStyle">{row.category}</p>
      ),
    },
    {
      field: "subCategory",
      name: "Sub Category",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => (
        <p className="tableStyle">{row.subCategory}</p>
      ),
    },
    {
      field: "status",
      name: "Status",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => <p className="tableStyle">{row.status}</p>,
    },
    {
      field: "percentage",
      name: "Percentage",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => (
        <p className="tableStyle">{row.percentage}</p>
      ),
    },
    {
      field: "estimateTime",
      name: "Estimate Time",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => (
        <p className="tableStyle">{row.estimateTime}</p>
      ),
    },
    {
      field: "actualTime",
      name: "Actual Time",
      width: "10rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: TaskListDto) => (
        <p className="tableStyle">{row.actualTime}</p>
      ),
    },
  ];

  export const TEAMREPORT: TeamReportVM = {
    teamtotalprojects: [],
    teamtotalobjectives: [],
    teamMemebers: [],
    teamtotaltasks: [],
    teamdetails: [],
    todaysTasks: [],
  };

  export const TEAMDETAILS: TeamDetailsVM ={
    projectName: null,
    employeeName: null,
    totalTaskCount: null,
    totalActualHrs: null,
    employeeId: null,
    id: null,
  };

  export const FILTER: TeamFilter = {
    projectName: "",
    employeeName: "",
  };