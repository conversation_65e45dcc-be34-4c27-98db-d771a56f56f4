export interface Attendence {
  TeamId ?:number;
  EmployeeName?: string;
  TeamId?: number;
  TeamName?:string;
  Department?: string;
  Date?: Date;
  DayId?: number;
  InTime?: string;
  OutTime?: string;
  Status?: string;
}

export interface EmployeeAttendanceDto {
  date?: Date;
  inTime?: Date;
  outTime?: Date;
  holidayName?: string | null;
  holidayApplicable?: boolean | null;
  employeeGeo: EmployeeGeo[];
}

export interface EmployeeAttendanceVM {
  name: string;
  phoneNumber: string;
  email: string;
  role: string;
  totalAttendance: number;
  totalAbsent: number;
  averageInTime?: string | null;
  averageOutTime?: string | null;
  employeeAttendances: EmployeeAttendanceDto[];
}
