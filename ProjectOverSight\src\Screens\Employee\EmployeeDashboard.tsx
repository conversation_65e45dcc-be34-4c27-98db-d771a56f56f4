import { Grid, Typography } from "@mui/material";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import EmployeeTask from "../Employee/EmployeeDashboard/EmployeeTask";
import { Get } from "../../../src/Services/Axios";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty";
import { useEffect, useState } from "react";
import { useContextProvider } from "../../CommonComponents/Context";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import TaskIcon from "@mui/icons-material/Task";

export const EmployeeDashboard = () => {
  const { user } = useContextProvider();
  const [data, setData] = useState<any>([]);

  async function fetchData() {
    const employeeTasks: any = await Get(`app/EmployeeTask/GetEmployeeTasks`);
    setData(employeeTasks?.data);
  }

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div style={{ background: "rgba(0,0,0,0.1)" }}>
        <Breadcrumbs className="mx-3" separator=">">
          <Typography sx={{ fontWeight: "bold", mt: 2 }}>Home</Typography>
        </Breadcrumbs>
        <Grid container>
          <h1 className="fw-bold mx-auto mb-3">{user?.userName}</h1>
          <div className="shadow w-100 bg-light mx-5 mb-3 row p-4 d-flex justify-content-evenly rounded">
            <div className="card bg-primary text-light text-center p-2 col-md m-1">
              <TaskIcon className="fs-1" />
              <h3 className="fs-2 fw-bold text-center">
                {data?.length || "0"}
              </h3>
              <h3 className="fs-2 fw-bold text-center">Total Task</h3>
            </div>
            <div
              className="card text-light text-center p-2 col-md m-1"
              style={{ background: "#09ed3b" }}
            >
              <CheckCircleOutlineIcon className="fs-1" />
              <h3 className="fs-2 fw-bold text-center">
                {data?.filter((x: any) => x.percentage === 100).length || "0"}
              </h3>
              <h3 className="fs-2 fw-bold text-center">Completed Task</h3>
            </div>
            <div className="card bg-warning text-light text-center p-2  col-md m-1">
              <HourglassEmptyIcon className="fs-1" />
              <div className="fs-2 fw-bold text-center">
                {data?.filter(
                  (x: any) => x.percentage < 100 && x.percentage > 0
                ).length || "0"}
              </div>
              <span className="fs-2 fw-bold text-center">In Progress</span>
            </div>
            <div className="card bg-danger text-light text-center p-2  col-md m-1">
              <ErrorOutlineIcon className="fs-1" />
              <div className="fs-2 fw-bold text-center">
                {data?.filter((x: any) => x.percentage === 0).length || "0"}
              </div>
              <span className="fs-2 fw-bold text-center">Not Started</span>
            </div>
          </div>
          <Grid container>
            <EmployeeTask />
          </Grid>
        </Grid>
      </div>
    </>
  );
};
