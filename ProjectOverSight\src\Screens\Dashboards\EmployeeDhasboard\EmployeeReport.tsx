import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Box } from "@mui/material";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import { useLocation } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import Select from "react-select";
import DataTable from "react-data-table-component";
import AccordionDetails from "@mui/material/AccordionDetails";
import { Get } from "../../../Services/Axios";
import { useQuery } from "react-query";
import { Link } from "react-router-dom";
import { useContextProvider } from "../../../CommonComponents/Context";
import { ToolTip } from "../../../CommonComponents/ToolTip";
import { TaskFilter } from "../../../Models/Task/TaskFilter";
import { FILTER } from "../../../Constants/Task/Task";
import { UserStory } from "../../../Models/Project/UserStory";
import { UserInterface } from "../../../Models/Project/UserInterface";
import BackDrop from "../../../CommonComponents/BackDrop";

const Accordionstyle = {
  backgroundColor: "#3be3cf",
  color: "black",
  maxHeight: "2%",
  height: "85%",
  margintop: "2%",
  fontWeight: "bold",
  fontSize: "55%",
};

const headersStyle = {
  textAlign: "left",
  ml: "2%",
  color: "black",
  fontSize: "215%",
};

export const EmployeeReport = () => {
  const location = useLocation();
  const { role } = useContextProvider();
  const statusRef = useRef<any>();
  const projectNameRef = useRef<any>();
  const UserStoryRef = useRef<any>();
  const UserInterfaceNameRef = useRef<any>();
  const [filterRows, setfilterRows] = useState<any>([]);
  const [rows, setRows] = useState<any>([]);
  const [filter, setfilter] = useState<TaskFilter>(FILTER);
  const [USfilter, USsetfilter] = useState<UserStory>({});
  const [UIfilter, UIsetfilter] = useState<UserInterface>({});
  var [Projects, setProjects] = useState<string[]>([]);

  const columns: any = [
    {
      name: "SI.No",
      width: "5rem",
      right: true,
      selector: (row: any, index: number) => (
        <p className={`tableStyle ${row}`}>{index + 1}</p>
      ),
    },
    {
      name: "Task Name",
      width: "11rem",
      selector: (row: any) => (
        <ToolTip title={row?.name}>
          <p className="tableStyle">{row?.name}</p>
        </ToolTip>
      ),
    },
    {
      name: "Description",
      width: "16rem",
      selector: (row: any) => (
        <ToolTip title={row?.taskDescription}>
          <p className="tableStyle">{row?.taskDescription || "-"}</p>
        </ToolTip>
      ),
    },
    {
      name: "User Story",
      width: "16rem",
      selector: (row: any) => (
        <ToolTip title={row?.userStory}>
          <p className="tableStyle">{row?.userStory || "-"}</p>
        </ToolTip>
      ),
    },
    {
      name: "User Interface",
      width: "15rem",
      selector: (row: any) => (
        <ToolTip title={row?.userInterface}>
          <p className="tableStyle">{row?.userInterface || "-"}</p>
        </ToolTip>
      ),
    },
    {
      name: "Project Name",
      width: "13rem",
      selector: (row: any) => (
        <ToolTip title={row?.userInterface}>
          <p className="tableStyle">{row?.projectName || "-"}</p>
        </ToolTip>
      ),
    },
    {
      name: "Task Status",
      width: "12rem",
      selector: (row: any) => (
        <p className="tableStyle">{row?.status || "-"}</p>
      ),
    },
  ];

  const currentDate = new Date();
  async function EmployeeDetails() {
    const totalattendance = await Get(
      `app/Employee/GetAttendanceByUserId?userId=${location.state.data}&month=${
        currentDate.getMonth() + 1
      }&year=${currentDate.getFullYear()}`
    );
    const EmployeeDetail = await Get(
      `app/Employee/GetEachEmployeeDashboards?employeeId=${location.state.employeeId}`
    );
    return { totalattendance, EmployeeDetail };
  }
  const { data, isLoading }: any = useQuery(
    "Employeetotaldetails",
    EmployeeDetails
  );

  useEffect(() => {
    if (data) {
      setRows(data?.EmployeeDetail?.data?.employeeDailyTask || []);
      setfilterRows(data?.EmployeeDetail?.data?.employeeDailyTask || []);
      setProjects(data?.EmployeeDetail?.data?.projectDetails || []);
    }
  }, [data]);

  const uniqueProjects = new Set();
  const uniqueStatus = new Set();
  const NumberofProject = data?.EmployeeDetail?.data?.projectDetails?.map(
    (e: any) => e?.id
  );
  const NumberofTask = data?.EmployeeDetail?.data?.employeeDailyTask?.map(
    (e: any) => e?.taskId
  );
  const NumberofProjectInprogress =
    data?.EmployeeDetail?.data?.projectDetails?.filter(
      (e: any) => e?.status === "In Progress"
    );
  const NumberofProjectCompleted =
    data?.EmployeeDetail?.data?.projectDetails?.filter(
      (e: any) => e?.status === "Completed"
    );
  const NumberofTaskInprogress =
    data?.EmployeeDetail?.data?.employeeDailyTask?.filter(
      (e: any) => e?.status === "In-Progress"
    );
  const NumberofTaskCompleted =
    data?.EmployeeDetail?.data?.employeeDailyTask?.filter(
      (e: any) => e?.status === "Completed"
    );
  const countInprogress = NumberofProjectInprogress?.length || 0;
  const countCompleted = NumberofProjectCompleted?.length || 0;
  const Taskstatus = data?.EmployeeDetail?.data?.employeeDailyTask?.filter(
    (e: any) => e?.status
  );

  const TotalActualHours = data?.EmployeeDetail?.data?.employeeDailyTask
    ?.filter((e: any) => e?.actTime)
    .reduce((sum: number, task: any) => sum + (task?.actTime || 0), 0);

  NumberofProject?.forEach((row: any) => {
    if (row) {
      uniqueProjects.add(row);
    }
  });
  Taskstatus?.forEach((row: any) => {
    if (row) {
      uniqueStatus.add(row);
    }
  });

  const uniqueStatusValues = Array.from(
    new Set(Taskstatus?.map((item: any) => item.status))
  );
  const uniqueuserStoryValues = Array.from(
    new Set(Taskstatus?.map((item: any) => item.userStory))
  );
  const uniqueuserInterfaceValues = Array.from(
    new Set(Taskstatus?.map((item: any) => item.userInterface))
  );

  const Statusoptions = uniqueStatusValues
    .slice()
    .sort((a: any, b) => a.localeCompare(b))
    .map((status) => ({
      value: status,
      label: status,
    }));

  const uniqueuserStoryValuesoptions = uniqueuserStoryValues
    .filter((status) => status !== null && status !== "")
    .sort((a: any, b) => a.localeCompare(b))
    .map((status) => ({
      value: status,
      label: status,
    }));

  const uniqueuserInterfaceoptions = uniqueuserInterfaceValues
    .filter((status) => status !== null && status !== "")
    .sort((a: any, b: any) => (a.label || "").localeCompare(b.label || ""))
    .map((status) => ({
      value: status,
      label: status,
    }));

  function ApplyFilter() {

    let temp: any = [];

    if (filter.weekEndingDate != null) {
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (rows[i].weekEndingDate?.slice(0, 10) == filter.weekEndingDate) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.status != null) {
      temp = temp.filter((e: any) => {
        if (typeof filter.status === "string")
          return (
            e.status.trim().toLowerCase() ===
            filter.status?.trim().toLowerCase()
          );
      });
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.projectName != null) {
      temp = temp.filter((e: any) => {
        if (typeof filter.projectName === "string")
          return (
            e.projectName
              .toLowerCase()
              .search(filter.projectName?.toLowerCase()) >= 0
          );
      });
      setfilterRows(temp);
    }

    if (USfilter && USfilter.name != null) {
      temp = temp.filter((e: any) => {
        const userStory = e.userStory || "";
        return userStory
          ?.toLowerCase()
          ?.includes(USfilter?.name?.toLowerCase());
      });
      setfilterRows(temp);
    }

    if (UIfilter && UIfilter.userInterfaceName != null) {
      temp = temp.filter((e: any) => {
        const userInterface = e.userInterface || "";
        return userInterface
          .toLowerCase()
          .includes(UIfilter?.userInterfaceName?.toLowerCase());
      });
      setfilterRows(temp);
    }
  }

  function reset() {
    setfilter(FILTER);
    USsetfilter({});
    UIsetfilter({});
    if (projectNameRef.current) projectNameRef.current.clearValue();
    if (statusRef.current) statusRef.current.clearValue();
    if (UserStoryRef.current) UserStoryRef.current.value = "";
    if (UserInterfaceNameRef.current) UserInterfaceNameRef.current.value = "";
    setfilterRows(rows);
  }

  function parse(type: string) {
    var empData = data?.EmployeeDetail?.data?.employeedetailsleave;
    var result: any;
    if (empData) {
      switch (type) {
        case "leavePending":
          result = empData[0].leavePending;
          break;
        case "leaveTaken":
          result = empData[0].leaveTaken;
          break;
      }
      return result;
    }
    return 0;
  }

  return (
    <>
      <div>
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link color="inherit" to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link color="inherit" to={`/${role}/Employee`}>
            <Typography sx={{ fontWeight: "bold" }}>Employee</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>Employee Report</Typography>
        </Breadcrumbs>
        <Grid container sx={{ mt: "1%" }}>
          <Grid xs={3}>
            <Typography
              sx={{
                fontSize: { xs: "90%", sm: "105%", md: "105%" },
                marginLeft: "8%",
              }}
            >
              Employee Name :{" "}
              <span style={{ fontWeight: "bold" }}>
                {location.state?.Employeename}
              </span>
            </Typography>
          </Grid>
          <Grid xs={3}>
            <Typography
              sx={{ fontSize: { xs: "90%", sm: "105%", md: "105%" } }}
            >
              Team Name :{" "}
              <span style={{ fontWeight: "bold" }}>
                {data?.EmployeeDetail?.data?.teamDetails[0]?.teamName}
              </span>
            </Typography>
          </Grid>
          <Grid xs={3}>
            <Typography
              sx={{
                fontSize: { xs: "90%", sm: "105%", md: "105%" },
                ml: { xs: "25%", sm: "15%", md: "35%" },
              }}
            >
              Joining Date:{" "}
              <span style={{ fontWeight: "bold" }}>
                {data?.EmployeeDetail?.data?.joiningDate
                  ? new Date(
                      data.EmployeeDetail.data.joiningDate
                    ).toLocaleDateString("en-GB")
                  : "N/A"}
              </span>
            </Typography>
          </Grid>
          <Grid xs={3}>
            <Typography
              sx={{
                fontSize: { xs: "90%", sm: "105%", md: "105%" },
                float: "right",
                mr: "8%",
              }}
            >
              Department :{" "}
              <span style={{ fontWeight: "bold" }}>
                {location.state?.Department}
              </span>
            </Typography>
          </Grid>
        </Grid>
        <div style={{ width: "96%", marginLeft: "2%", marginTop: "1%" }}>
          <div>
            <div
              className="card mt-2 pt-2 pb-2 mb-2 text-light text-center"
              style={Accordionstyle}
            >
              <Typography sx={headersStyle}>
                Employee General Details
              </Typography>
            </div>
            <div
              className="mt-3 text-dark bg-light"
              style={{ border: "3px solid #f0e4cc", marginTop: "0.5%" }}
            >
              <Grid
                container
                spacing={1}
                sx={{ ml: { xs: "12%", sm: "2%", md: "1%" }, width: "99.5%" }}
              >
                <Grid
                  item
                  xs={10}
                  sm={6}
                  md={4}
                  lg={2.4}
                  sx={{ justifyContent: "center", alignItems: "center" }}
                >
                  <div
                    className="card m-1 pt-2 text-light text-center"
                    style={{
                      width: "85%",
                      backgroundColor: "#deaff0",
                      height: "90%",
                    }}
                  >
                    <span
                      className="m-0 fs-6 fw-bold text-center"
                      style={{ color: "#032552" }}
                    >
                      Average In Time
                    </span>
                    <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                      {data?.totalattendance?.data?.averageInTime || "-"}
                    </Typography>
                  </div>
                </Grid>
                <Grid item xs={10} sm={6} md={4} lg={2.4}>
                  <div
                    className="card m-1 pt-2 text-light text-center"
                    style={{
                      width: "85%",
                      backgroundColor: "#deaff0",
                      height: "90%",
                    }}
                  >
                    <span
                      className="m-0 fs-6 fw-bold text-center"
                      style={{ color: "#032552" }}
                    >
                      Average Out Time
                    </span>
                    <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                      {data?.totalattendance?.data?.averageOutTime || "-"}
                    </Typography>
                  </div>
                </Grid>
                <Grid item xs={10} sm={6} md={4} lg={2.4}>
                  <div
                    className="card m-1 pt-2 text-light text-center"
                    style={{
                      width: "85%",
                      backgroundColor: "#deaff0",
                      height: "90%",
                    }}
                  >
                    <span
                      className="m-0 fs-6 fw-bold text-center"
                      style={{ color: "#032552" }}
                    >
                      Total No. Leaves Taken
                    </span>
                    <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                      {parse("leaveTaken")}
                    </Typography>
                  </div>
                </Grid>
                <Grid item xs={10} sm={6} md={4} lg={2.4}>
                  <div
                    className="card m-1 pt-2 text-light text-center"
                    style={{
                      width: "85%",
                      backgroundColor: "#deaff0",
                      height: "90%",
                    }}
                  >
                    <span
                      className="m-0 fs-6 fw-bold text-center"
                      style={{ color: "#032552" }}
                    >
                      Total No. Leaves Pending
                    </span>
                    <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                      {parse("leavePending")}
                    </Typography>
                  </div>
                </Grid>
                <Grid item xs={10} sm={6} md={4} lg={2.4}>
                  <div
                    className="card m-1 pt-2 text-light text-center"
                    style={{
                      width: "85%",
                      backgroundColor: "#deaff0",
                      height: "90%",
                    }}
                  >
                    <span
                      className="m-0 fs-6 fw-bold text-center"
                      style={{ color: "#032552" }}
                    >
                      Employees Skills
                    </span>
                    <Typography sx={{ fontSize: "65%", color: "#032552" }}>
                      {data?.EmployeeDetail?.data?.employeeskills?.length ||
                        "0"}
                    </Typography>
                  </div>
                </Grid>
              </Grid>
            </div>
          </div>
          <div style={{ marginTop: "1%" }}>
            <div
              className="card mt-2 pt-2 pb-2 mb-2 text-light text-center"
              style={Accordionstyle}
            >
              <Typography sx={headersStyle}>
                Employee Project Details
              </Typography>
            </div>
            <div>
              <div
                className="mt-3 text-dark bg-light"
                style={{ border: "3px solid #deaff0", marginTop: "0.5%" }}
              >
                <Grid container sx={{ border: "6px sloid black" }} spacing={1}>
                  <Grid
                    item
                    xs={4}
                    className="setCards"
                    sm={2.5}
                    md={2.1}
                    sx={{ mt: { md: "6%", sm: "10%", xs: "25%" } }}
                  >
                    <div
                      className="card m-1 text-light text-center"
                      style={{ height: "40%", width: "85%" }}
                    >
                      <Typography sx={{ fontSize: "30%", color: "#032552" }}>
                        Pending
                      </Typography>
                    </div>
                    <div
                      className="card m-1 text-light text-center"
                      style={{ height: "40%", width: "85%" }}
                    >
                      <Typography sx={{ fontSize: "30%", color: "#032552" }}>
                        Completed
                      </Typography>
                    </div>
                  </Grid>
                  <Grid item xs={4} sm={3.5} md={2.1}>
                    <div
                      className="card m-1 pt-2 text-light text-center"
                      style={{
                        width: "85%",
                        backgroundColor: "#deaff0",
                        height: "50%",
                      }}
                    >
                      <span
                        className="m-0 fs-6 fw-bold text-center"
                        style={{ color: "#032552" }}
                      >
                        Project Assigned
                      </span>
                      <Typography
                        sx={{
                          fontSize: { xs: "40%", sm: "55%", md: "55%" },
                          color: "#032552",
                        }}
                      >
                        {uniqueProjects.size || "0"}
                      </Typography>
                    </div>
                    <div
                      className="card m-1 text-light text-center"
                      style={{ height: "20%", width: "85%" }}
                    >
                      <Typography
                        sx={{
                          fontSize: "30%",
                          color: "black",
                          fontWeight: "bold",
                        }}
                      >
                        {countInprogress}
                      </Typography>
                    </div>
                    <div
                      className="card m-1  text-light text-center"
                      style={{ height: "20%", width: "85%" }}
                    >
                      <Typography
                        sx={{
                          fontSize: "30%",
                          color: "black",
                          fontWeight: "bold",
                        }}
                      >
                        {countCompleted}
                      </Typography>
                    </div>
                  </Grid>
                  <Grid item xs={4} sm={4} md={2.1}>
                    <div
                      className="card m-1 pt-2 text-light text-center"
                      style={{
                        width: "85%",
                        backgroundColor: "#deaff0",
                        height: "50%",
                      }}
                    >
                      <span
                        className="m-0 fs-6 fw-bold text-center"
                        style={{ color: "#032552" }}
                      >
                        No of Tasks Assigned
                      </span>
                      <Typography
                        sx={{
                          fontSize: { xs: "40%", sm: "55%", md: "55%" },
                          color: "#032552",
                        }}
                      >
                        {NumberofTask?.length || "0"}
                      </Typography>
                    </div>
                    <div
                      className="card m-1 text-light text-center"
                      style={{ height: "20%", width: "85%" }}
                    >
                      <Typography
                        sx={{
                          fontSize: "30%",
                          color: "black",
                          fontWeight: "bold",
                        }}
                      >
                        {NumberofTaskInprogress?.length || 0}
                      </Typography>
                    </div>
                    <div
                      className="card m-1  text-light text-center"
                      style={{ height: "20%", width: "85%" }}
                    >
                      <Typography
                        sx={{
                          fontSize: "30%",
                          color: "black",
                          fontWeight: "bold",
                        }}
                      >
                        {NumberofTaskCompleted?.length || 0}
                      </Typography>
                    </div>
                  </Grid>
                </Grid>
              </div>
            </div>
          </div>
          <div style={{ marginTop: "1%" }}>
            <div
              className="card mt-2 pt-2 pb-2 mb-2 text-light text-center"
              style={Accordionstyle}
            >
              <Typography sx={headersStyle}>Employee Task Details</Typography>
            </div>
            <AccordionDetails>
              <Grid container>
                <div className="well mx-auto mt-1">
                  <div className="row">
                    <div className="col-sm-2">
                      <div className="form-group">
                        <label>Task Status</label>
                        <Select
                          id="category-simple-select"
                          isClearable={true}
                          ref={statusRef}
                          className="col mt-1"
                          onChange={(selectedOption: any) => {
                            if (selectedOption) {
                              setfilter((prevState: any) => ({
                                ...prevState,
                                status:
                                  selectedOption.label.trim() === ""
                                    ? null
                                    : selectedOption.label,
                              }));
                            }
                          }}
                          options={Statusoptions}
                          styles={{
                            menu: (provided) => ({
                              ...provided,
                              zIndex: 1000,
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="col-sm-2">
                      <div className="form-group">
                        <label>Project</label>
                        <Select
                          id="category-simple-select"
                          isClearable={true}
                          ref={projectNameRef}
                          className="col mt-1"
                          onChange={(selectedOption: any) => {
                            setfilter((prevState: TaskFilter) => {
                              return {
                                ...prevState,
                                projectName: selectedOption
                                  ? selectedOption.value
                                  : null,
                              };
                            });
                          }}
                          options={Projects.filter(
                            (e: any) => typeof e === "object" && e.name
                          )
                            .slice()
                            .sort((a: any, b: any) =>
                              (a.name || "").localeCompare(b.status || "")
                            )
                            .map((e: any) => ({
                              value: e.name,
                              label: e.name,
                            }))}
                          styles={{
                            menu: (provided) => ({
                              ...provided,
                              zIndex: 1000,
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="col-sm-2">
                      <div className="form-group">
                        <label>User Story</label>
                        <Select
                          id="category-simple-select"
                          isClearable={true}
                          ref={UserStoryRef}
                          className="col mt-1"
                          onChange={(selectedOption: any) => {
                            USsetfilter((prevState: any) => {
                              return {
                                ...prevState,
                                name: selectedOption
                                  ? selectedOption.value
                                  : null,
                              };
                            });
                          }}
                          options={uniqueuserStoryValuesoptions}
                          styles={{
                            menu: (provided) => ({
                              ...provided,
                              zIndex: 1000,
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="col-sm-2">
                      <div className="form-group">
                        <label>User Interface</label>
                        <Select
                          id="category-simple-select"
                          isClearable={true}
                          ref={UserInterfaceNameRef}
                          className="col mt-1"
                          onChange={(selectedOption: any) => {
                            UIsetfilter((prevState: any) => {
                              return {
                                ...prevState,
                                userInterfaceName: selectedOption
                                  ? selectedOption.value
                                  : null,
                              };
                            });
                          }}
                          options={uniqueuserInterfaceoptions}
                          styles={{
                            menu: (provided) => ({
                              ...provided,
                              zIndex: 1000,
                            }),
                          }}
                        />
                      </div>
                    </div>
                    <div className="container">
                      <div className="row justify-content-end">
                        <div className="col-auto">
                          <Button
                            variant="contained"
                            endIcon={<SearchIcon />}
                            className="mx-3 mt-3 "
                            onClick={() => ApplyFilter()}
                          >
                            Search
                          </Button>
                          <Button
                            variant="contained"
                            endIcon={<RefreshIcon />}
                            className="mx-3 mt-3"
                            onClick={() => reset()}
                          >
                            Reset
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Grid>
              <Grid container>
                <div
                  className="card mt-2 mb-3 pt-2 text-light text-center"
                  style={{
                    backgroundColor: "#deaff0",
                    height: "25%",
                    marginLeft: "2%",
                    width: "100%",
                    maxWidth: "200px",
                    marginRight: "auto",
                  }}
                >
                  <span
                    className="m-0 fs-6 fw-bold text-center"
                    style={{ color: "#032552" }}
                  >
                    Actual Hours Taken
                  </span>
                  <Typography sx={{ fontSize: "55%", color: "#032552" }}>
                    {TotalActualHours || "0"}
                  </Typography>
                </div>
              </Grid>
              <Grid container sx={{ mt: { xs: "12%", sm: "10%", md: "6%" } }}>
                <div className="col-3 col-s-3">
                  <Box style={{ width: "94vw", position: "relative" }}>
                    <DataTable
                      columns={columns}
                      fixedHeader={true}
                      responsive
                      persistTableHead
                      progressPending={isLoading}
                      data={filterRows || []}
                      customStyles={{
                        table: {
                          style: {
                            height: "50vh",
                            border: "1px solid rgba(0,0,0,0.1)",
                            overflowY: "scroll",
                          },
                        },

                        headRow: {
                          style: {
                            background: "#1e97e8",
                            fontSize: "16px",
                            color: "white",
                            fontFamily: "inherit",
                          },
                        },
                        pagination: {
                          style: {
                            position: "absolute",
                            width: "94vw",
                            background: "#daeef0",
                            color: "black",
                            textAlign: "right",
                            top: -55,
                            borderRadius: "5px 5px 0 0",
                          },
                        },
                      }}
                      pagination
                      paginationPerPage={50}
                      paginationRowsPerPageOptions={[50, 100, 200]}
                      pointerOnHover={true}
                    />
                  </Box>
                </div>
              </Grid>
            </AccordionDetails>
          </div>
        </div>
      </div>
      <BackDrop open={isLoading} />
    </>
  );
};
