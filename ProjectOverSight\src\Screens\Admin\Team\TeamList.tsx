import Breadcrumbs from "@mui/material/Breadcrumbs";
import { Link } from "react-router-dom";
import { Typography, Grid, Tooltip } from "@mui/material";
import Select from "react-select";
import { useEffect, useRef, useState } from "react";
import DataTable from "react-data-table-component";
import { Listteam } from "../../../Models/Team/Teamfilter";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import { Get } from "../../../Services/Axios";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import AddIcon from "@mui/icons-material/Add";
import { AddTeam } from "./AddTeam";
import { EditTeam } from "./EditTeam";
import { ViewTeam } from "./ViewTeam";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import TaskIcon from "@mui/icons-material/Task";
import DashboardIcon from "@mui/icons-material/Dashboard";
import { useContextProvider } from "../../../CommonComponents/Context";
import { ADMIN } from "../../../Constants/Common/Roles";
import LeaveReport from "../../../assets/leaveReport.png";
import HourglassTopIcon from "@mui/icons-material/HourglassTop";
import { IOSSwitch } from "../../../CommonComponents/IOSSwitch";

export const TeamList = () => {
  const [rows, setRows] = useState<any>([]);
  const TeamnameRef = useRef<any>(null);
  const estStartDateRef = useRef<HTMLInputElement>(null);
  const [viewTeamData, setviewTeamData] = useState<any>({});
  const [weekEndingDates, setweekEndingDates] = useState<string[]>([]);
  const estEndDateRef = useRef<HTMLInputElement>(null);
  const statusRef = useRef<any>(null);
  const [TeamListRows, setTeamListRows] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [reload, setReload] = useState(false);
  const [filter, setfilter] = useState<Listteam>({});
  const { role } = useContextProvider();
  const [active, setActive] = useState(true);
  const [TeamView, setTeamView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      type: "Date",
      width: "20rem",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (row: any) => {
        return (
          <>
            <Tooltip
              title="View"
              className="mx-1"
              onClick={() => {
                setTeamView({ view: true });
                setviewTeamData(row);
              }}
            >
              <VisibilityIcon className="fs-4 text-info" />
            </Tooltip>
            {role === ADMIN && (
              <>
              <Tooltip
                title="Edit"
                className="mx-1"
                onClick={() => {
                  setTeamView({ edit: true });
                  setviewTeamData(row);
                }}
              >
                <EditIcon className="fs-4 text-warning" />
              </Tooltip>
              <Link
                to={row.isActive && "/Admin/AssignProject"}
                state={{ data: { id: row.id, name: row.name } }}
              >
                <Tooltip
                  title={`${
                    row.isActive ? "Assign Project" : "Team is not active"
                  }`}
                  className="mx-1"
                >
                  <TaskIcon className="fs-4 text-primary" />
                </Tooltip>
              </Link>
            <Link
              to={row.isActive && `/${role}/TeamTaskQuadrant`}
              state={{
                data: {
                  teamId: row.id,
                  teamName: row.name,
                  teamRoute: true,
                },
                weekEndDate: weekEndingDates,
                route: "teamList",
              }}
            >
              <Tooltip
                title={`${
                  row.isActive
                    ? "Team Loading & availability"
                    : "Team is not active"
                }`}
                className="mx-1"
              >
                <HourglassTopIcon
                  className="fs-4 "
                  style={{ color: "brown" }}
                />
              </Tooltip>
            </Link>
            <Link
              to={row.isActive && `/${role}/TeamReport`}
              state={{
                data: {
                  teamId: row.id,
                  teamName: row.name,
                  teamRoute: true,
                  startDate: row.startDate,
                  endDate: row.endDate,
                },
              }}
            >
              <Tooltip
                title={`${
                  row.isActive ? "Team Dashboard" : "Team is not active"
                }`}
                className="mx-1"
              >
                <DashboardIcon className="fs-4" sx={{ color: "#03fcfc" }} />
              </Tooltip>
            </Link>
            
            <Link
              to={row.isActive && `/Admin/EmployeeleaveReport`}
              state={{
                data: { id: row.id, name: row.name },
              }}
            >
              <Tooltip
                title={`${
                  row.isActive ? "Employee Leave Report" : "Team is not active"
                }`}
                className="mx-1"
              >
                <img
                  src={LeaveReport}
                  width={22}
                  height={22}
                  className="fs-4 text-report-color"
                />
              </Tooltip>
            </Link>
            </>
      )}
          </>
        );
      },
    },
    {
      field: "name",
      name: "Team Name",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => (
        <Link
          className={`tableStyle ${!row.isActive && "text-dark"}`}
          to={row.isActive && `/${role}/TeamQuadrant`}
          style={{ textDecoration: "none" }}
          state={{ data: { id: row.id, name: row.name } }}
        >
          {row.name}
        </Link>
      ),
    },
    {
      field: "startDate",
      name: "Start Date",
      type: "Date",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => {
        const result = new Date(row.startDate)
          .toLocaleDateString("en-bz")
          .replaceAll("/", "-");
        return <p className="tableStyle">{result}</p>;
      },
    },
    {
      field: "endDate",
      name: "End Date",
      type: "Date",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => {
        const result = new Date(row.endDate)
          .toLocaleDateString("en-bz")
          .replaceAll("/", "-");
        return <p className="tableStyle">{result}</p>;
      },
    },
  ];

  useEffect(() => {
    let teamList = Get("app/Team/GetTeamList");
    let taskList = Get("app/Task/GetTaskList");
    teamList.then((response: any) => {
      setRows(response?.data);
      setTeamListRows(response?.data || []);
      setLoading(false);
    });

    let dates = new Set<string>();
    taskList.then((response: any) => {
      response?.data?.forEach((row: any) => {
        dates.add(row.weekEndDate);
      });
      setweekEndingDates([...dates]);
    });
  }, [reload]);

  const handleClickOpen = () => {
    setTeamView({ add: true });
  };

  function ApplyFilter() {
    let temp: any = [];

    if (filter.StartDate != null) {
      if (filter.EndDate == null) {
        estEndDateRef.current?.focus();
        return;
      }
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].startDate.slice(0, 10) >= filter.StartDate &&
          rows[i].endDate.slice(0, 10) <= filter.EndDate
        ) {
          temp.push(rows[i]);
        }
      }
      setTeamListRows(temp);
    } else {
      temp = rows;
    }

    if (filter.Name != null) {
      temp = rows.filter((row: any) => {
        return row.name.toLowerCase().search(filter.Name?.toLowerCase()) >= 0;
      });
      setTeamListRows(temp);
    }

    if (filter.IsActive != null) {
      temp = rows.filter((row: any) => {
        return row.isActive === filter.IsActive;
      });
      setTeamListRows(temp);
    }
  }

  function reset() {
    setfilter({});
    if (TeamnameRef.current) TeamnameRef.current.clearValue();
    if (estStartDateRef.current) estStartDateRef.current.value = "";
    if (estEndDateRef.current) estEndDateRef.current.value = "";
    if (statusRef.current) statusRef.current.clearValue();
    setTeamListRows(rows);
  }

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Team</Typography>
      </Breadcrumbs>
      <div className="well mx-auto mt-4">
        <div className="container">
          <div className="row">
            <div className="col-sm-12 col-md-6 col-lg-2">
              <div className="form-group">
                <label>Team Name</label>
                <Select
                  id="name"
                  aria-label="Floating label select example"
                  
                  ref={TeamnameRef}
                  placeholder="Team Name"
                  className="mt-1"
                  onChange={(code: any) => {
                    if (code) {
                      setfilter((prevState) => {
                        return {
                          ...prevState,
                          Name: code.label == "" ? null : code.label,
                        };
                      });
                    } else {
                      setfilter((prevState) => {
                        return {
                          ...prevState,
                          Name: code,
                        };
                      });
                    }
                  }}
                  options={TeamListRows.filter(
                    (x: any) => x.isActive === active
                  )
                    .sort((a: any, b: any) => a.name.localeCompare(b.name))
                    .map((team: any) => {
                      return {
                        value: team.name,
                        label: team.name,
                      };
                    })}
                  styles={{
                    menu: (provided) => ({
                      ...provided,
                      zIndex: 1000,
                    }),
                  }}
                  isSearchable={true}
                />
              </div>
            </div>
            {/* <div className="col-sm-12 col-md-6 col-lg-2">
              <div className="form-group">
                <label>Status</label>
                <Select
                  options={[
                    {
                      value: true,
                      label: "Active",
                    },
                    {
                      value: false,
                      label: "In Active",
                    },
                  ]}
                  ref={statusRef}
                  onChange={(code: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        IsActive: code?.value,
                      };
                    });
                  }}
                />
              </div>
            </div> */}
            <div className="col-sm-12 col-md-6 col-lg-2">
              <div className="form-group">
                <label className="mx-1">Start Date</label>
                <input
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        StartDate: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                  ref={estStartDateRef}
                  type="date"
                  id="estimated-start-date"
                  placeholder="Estimated Start Date"
                  className="m-1  form-control"
                />
              </div>
            </div>
            <div className="col-sm-12 col-md-6 col-lg-2">
              <div className="form-group">
                <label className="mx-1">End Date</label>
                <input
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        EndDate: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                  ref={estEndDateRef}
                  type="date"
                  id="estimated-End-date"
                  placeholder="Estimated End Date"
                  className="m-1  form-control"
                />
              </div>
            </div>
            <div className="col-sm-12 col-md-6 col-lg-6">
              <div className="row justify-content-end">
                <div className="col-auto d-flex align-items-center">
                  <span className="mt-4">
                    <IOSSwitch
                      className="mx-2"
                      defaultChecked
                      onChange={(e: any) => {
                        setActive(e.target.checked);
                      }}
                    />
                    <span>{active ? "Active" : "In Active"}</span>
                  </span>
                  <Button
                    variant="contained"
                    endIcon={<SearchIcon />}
                    className="mx-2 mt-4"
                    onClick={() => ApplyFilter()}
                  >
                    Search
                  </Button>
                  <Button
                    variant="contained"
                    endIcon={<RefreshIcon />}
                    className="mx-2 mt-4"
                    onClick={() => reset()}
                  >
                    Reset
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center mb-5">
        <div className="col-11 col-s-4">
          <Grid>
            {role === ADMIN && (
              <Button
                variant="contained"
                className="mb-2 float-md-start"
                onClick={handleClickOpen}
                sx={{ ml: "3%" }}
              >
                Add Team
                <AddIcon className="mx-1" />
              </Button>
            )}
          </Grid>
        </div>
        <Grid item xs={12} sm={11} sx={{ mt: "4%" }}>
          <div className=" responsive-div col-3 col-s-3">
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                fixedHeader={true}
                responsive
                persistTableHead
                progressPending={loading}
                data={
                  TeamListRows.filter((x: any) => x.isActive === active) || []
                }
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },

                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
              />
            </Box>
          </div>
        </Grid>
      </div>
      <AddTeam
        openDialog={TeamView}
        setOpenDialog={setTeamView}
        setReload={setReload}
      />
      <EditTeam
        openDialog={TeamView}
        setOpenDialog={setTeamView}
        setReload={setReload}
        Data={viewTeamData}
      />
      <ViewTeam
        openDialog={TeamView}
        setOpenDialog={setTeamView}
        Data={viewTeamData}
      />
    </>
  );
};
