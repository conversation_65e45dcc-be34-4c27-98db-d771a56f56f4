import { Grid } from "@mui/material";
import { useState } from "react";
import { Breadcrumbs, Typography } from "@mui/material";
import { Link, useLocation } from "react-router-dom";
import { useContextProvider } from "../../../CommonComponents/Context";
// import { Get } from "../../../Services/Axios";
import * as React from "react";
import Box from "@mui/material/Box";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

export const DocumentOverView = () => {
  // const [rows, setRows] = useState<any>([]);
  // const [activeTab, setActiveTab] = useState("ProjectQuadrant");
  const location = useLocation();
  const { role } = useContextProvider();
  // const navigate = useNavigate();
  // const [projects, setProjects] = useState([]);
  const [reload, SetReload] = useState<boolean>(true);

  const [value, setValue] = React.useState(0);

  // const [data, setData] = useState([]);

  // const handleTabClicks = (tab: any) => {
  //   setActiveTab(tab);
  // };

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    return event;
  };

  // const [selectedProject, setSelectedProject] = useState<any>(null);
  // const [documentView, setDocumentView] = useState<any>({
  //   view: false,
  //   edit: false,
  //   add: false,
  //   viewObj: false,
  // });

  // useEffect(() => {
  //   var objective: any = Get(
  `app/Project/GetProjectObjective?ProjectId=${location.state?.projectId}`;
  // );
  // setData(objective?.data?.projectObjectives || []);
  // }, [reload,activeTab]);

  const handleEditClick = (project: any) => {
    SetReload(!reload);
    return project;
    // setSelectedProject(project);
  };

  // const ProjectList = () => {
  //   navigate(`/${role}/Project`);
  // };

  return (
    <>
      <Grid container>
        <Grid>
          {location.state.route === "projectList" && (
            <Breadcrumbs className="mt-3 mx-3" separator=">">
              <Link color="inherit" to={`/${role}`}>
                <Typography sx={{ fontWeight: "bold" }}>
                  DocumentDashboard
                </Typography>
              </Link>
              <Link color="inherit" to={`/${role}/DocumentScreen`}>
                <Typography sx={{ fontWeight: "bold" }}>Documents</Typography>
              </Link>
              <Typography sx={{ fontWeight: "bold" }}>
                Document Overview
              </Typography>
            </Breadcrumbs>
          )}
          {location.state.route === "adminDashboard" && (
            <Breadcrumbs className="mt-3 mx-3" separator=">">
              <Link color="inherit" to={`/${role}`}>
                <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
              </Link>
              <Typography sx={{ fontWeight: "bold" }}>
                Project Overview
              </Typography>
            </Breadcrumbs>
          )}
        </Grid>
        <div
          className="container shadow bg-light"
          style={{
            maxHeight: "610vh",
            height: "50vh",
            borderTopLeftRadius: "10px",
            borderTopRightRadius: "10px",
            marginTop: "1%",
          }}
        >
          <Grid
            container
            sx={{
              pt: "1%",
              pb: "1%",
              mb: "1%",
              borderBottom: "2px solid skyblue",
            }}
          >
            <Grid item xs={8}>
              <Typography
                className="fw-bolder fs-3"
                sx={{ mr: "45%", textAlign: "center", color: "#52a9eb" }}
              >
                Project Name : {location.state?.projectName}
              </Typography>
            </Grid>
            <Grid xs={2.5} md={3} item>
              <button
                type="button"
                className="btn btn-outline-warning float-end"
                style={{ fontWeight: "bold" }}
                onClick={() => {
                  handleEditClick(location.state);
                  // setDocumentView({ edit: true });
                }}
              >
                Edit
              </button>
            </Grid>
            <Grid xs={1.5} md={1} item>
              <button
                type="button"
                className="btn btn-outline-secondary float-end"
                style={{ fontWeight: "bold" }}
                // onClick={ProjectList}
              >
                Back
              </button>
            </Grid>
          </Grid>

          <nav className="d-flex" style={{}}>
            <Box sx={{ width: "100%" }}>
              <Tabs
                value={value}
                onChange={handleChange}
                centered
                TabIndicatorProps={{ style: { backgroundColor: "#006ec1" } }}
              >
                <Tab
                  label="Business Analyst"
                  sx={{
                    flex: 1,
                    fontWeight: "medium",
                    color: "black",
                    textTransform: "capitalize",
                  }}
                />
                <Tab
                  label="Project Close Out"
                  sx={{
                    flex: 1,
                    fontWeight: "medium",
                    color: "black",
                    textTransform: "capitalize",
                  }}
                />
                <Tab
                  label="Munites of Meeting"
                  sx={{
                    flex: 1,
                    fontWeight: "medium",
                    color: "black",
                    textTransform: "capitalize",
                  }}
                />
                <Tab
                  label="Input Documents"
                  sx={{
                    flex: 1,
                    fontWeight: "medium",
                    color: "black",
                    textTransform: "capitalize",
                  }}
                />
                <Tab
                  label="Output Documents"
                  sx={{
                    flex: 1,
                    fontWeight: "medium",
                    color: "black",
                    textTransform: "capitalize",
                  }}
                />
                <Tab
                  label="Key Documents"
                  sx={{
                    flex: 1,
                    fontWeight: "medium",
                    color: "black",
                    textTransform: "capitalize",
                  }}
                />
              </Tabs>
            </Box>
          </nav>
        </div>
      </Grid>
    </>
  );
};
