import { X } from "lucide-react";
import { Modal } from "react-bootstrap";
import VoiceRecorder from "./VoiceRecorder";
import { useEffect, useState } from "react";

type TakeScrumProps = {
  show: boolean;
  setShow: (show: boolean) => void;
};

function TakeScrum({ show, setShow }: TakeScrumProps) {
  const [questions, setQuestions] = useState<any>([
    {
      id: 1,
      question: "What did you do yesterday?",
      answer: "",
      audio: "",
    },
    {
      id: 2,
      question: "What will you do today?",
      answer: "",
      audio: "",
    },
    {
      id: 3,
      question: "What are your blockers?",
      answer: "",
      audio: "",
    },
    {
      id: 4,
      question: "What are your goals for the next week?",
      answer: "",
      audio: "",
    },
  ]);

  useEffect(() => {
    console.log(questions);
  }, [show]);

  // Reset all questions when modal is closed
  const resetAllQuestions = () => {
    setQuestions((prev: any[]) =>
      prev.map((q: any) => {
        return {
          ...q,
          answer: "",
          audio: null,
        };
      })
    );
  };

  // Handle modal close with reset (for cancel/close actions)
  const handleModalClose = () => {
    // Check if there are any recordings or answers
    const hasContent = questions.some((q: any) => q.audio || q.answer);

    if (hasContent) {
      const confirmClose = window.confirm(
        "Are you sure you want to close? All recordings and transcripts will be lost."
      );
      if (!confirmClose) {
        return;
      }
    }

    resetAllQuestions();
    setShow(false);
  };

  // Handle submit - could add submission logic here in the future
  const handleSubmit = () => {
    // TODO: Add submission logic here if needed
    console.log("Submitting scrum data:", questions);

    // For now, reset and close after submit
    resetAllQuestions();
    setShow(false);
  };

  return (
    <Modal
      show={show}
      onHide={handleModalClose}
      centered
      size="lg"
      className="mt-4"
    >
      <Modal.Header>
        <Modal.Title>Take Scrum</Modal.Title>
        <X onClick={handleModalClose} />
      </Modal.Header>
      <Modal.Body style={{ maxHeight: "60vh", overflowY: "auto" }}>
        {questions.map((item: any, index: number) => (
          <div key={index} className="mb-3">
            <label>
              {index + 1}. {item.question}
            </label>
            <VoiceRecorder question={item} setQuestions={setQuestions} />
          </div>
        ))}
      </Modal.Body>
      <Modal.Footer>
        <button className="btn btn-secondary" onClick={handleModalClose}>
          Close
        </button>
        <button className="btn btn-primary" onClick={handleSubmit}>
          Submit
        </button>
      </Modal.Footer>
    </Modal>
  );
}

export default TakeScrum;
