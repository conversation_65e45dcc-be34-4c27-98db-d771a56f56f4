import { X } from "lucide-react";
import { Modal } from "react-bootstrap";
import VoiceRecorder from "./VoiceRecorder";
import { useEffect, useState } from "react";

type TakeScrumProps = {
  show: boolean;
  setShow: (show: boolean) => void;
};

function TakeScrum({ show, setShow }: TakeScrumProps) {
  const [questions, setQuestions] = useState<any>([
    {
      id: 1,
      question: "What did you do yesterday?",
      answer: "",
      audio: "",
    },
    {
      id: 2,
      question: "What will you do today?",
      answer: "",
      audio: "",
    },
    {
      id: 3,
      question: "What are your blockers?",
      answer: "",
      audio: "",
    },
    {
      id: 4,
      question: "What are your goals for the next week?",
      answer: "",
      audio: "",
    },
  ]);

  useEffect(() => {
    console.log(questions);
  }, [show]);

  return (
    <Modal
      show={show}
      onHide={() => setShow(false)}
      centered
      size="lg"
      className="mt-4"
    >
      <Modal.Header>
        <Modal.Title>Take Scrum</Modal.Title>
        <X onClick={() => setShow(false)} />
      </Modal.Header>
      <Modal.Body style={{ maxHeight: "60vh", overflowY: "auto" }}>
        {questions.map((item: any, index: number) => (
          <div key={index} className="mb-3">
            <label>
              {index + 1}. {item.question}
            </label>
            <VoiceRecorder question={item} setQuestions={setQuestions} />
          </div>
        ))}
      </Modal.Body>
      <Modal.Footer>
        <button className="btn btn-secondary" onClick={() => setShow(false)}>
          Close
        </button>
        <button className="btn btn-primary" onClick={() => setShow(false)}>
          Submit
        </button>
      </Modal.Footer>
    </Modal>
  );
}

export default TakeScrum;
