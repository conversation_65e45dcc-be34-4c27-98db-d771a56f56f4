import { X } from "lucide-react";
import { Modal } from "react-bootstrap";
import VoiceRecorder from "./VoiceRecorder";
import { useEffect, useState } from "react";
import { Get, PostFiles } from "../../../Services/Axios";
import Swal from "sweetalert2";

type TakeScrumProps = {
  show: boolean;
  setShow: (show: boolean) => void;
};

function TakeScrum({ show, setShow }: TakeScrumProps) {
  const [questions, setQuestions] = useState<any>([]);

  useEffect(() => {
    async function fetchScrumQuestions() {
      const response: any = await Get("app/Scrum/GetScrumQuestions");
      response.data = response.data.map((item: any, index: number) => ({
        id: index + 1,
        question: item.questionText,
        answer: "",
        audio: "",
        file: null,
      }));
      setQuestions(response.data || []);
    }

    fetchScrumQuestions();
  }, [show]);

  // Reset all questions when modal is closed
  const resetAllQuestions = () => {
    setQuestions((prev: any[]) =>
      prev.map((q: any) => {
        return {
          ...q,
          answer: "",
          audio: null,
          file: null,
        };
      })
    );
  };

  // Handle modal close with reset (for cancel/close actions)
  const handleModalClose = () => {
    // Check if there are any recordings or answers
    const hasContent = questions.some((q: any) => q.audio || q.answer);

    if (hasContent) {
      const confirmClose = window.confirm(
        "Are you sure you want to close? All recordings and transcripts will be lost."
      );
      if (!confirmClose) {
        return;
      }
    }

    resetAllQuestions();
    setShow(false);
  };

  // Handle submit - could add submission logic here in the future
  const handleSubmit = async () => {
    const json: any = sessionStorage.getItem("user") || null;
    const sessionUser: any = JSON.parse(json);

    const hasContent = questions.every((q: any) => q.audio && q.answer);
    if (!hasContent) {
      Swal.fire({
        title: "",
        text: "Please record your answer for all questions.",
        icon: "warning",
        confirmButtonColor: "#3085d6",
      });
      return;
    }

    const jsonData = questions.map((item: any) => ({
      employeeId: sessionUser.employeeId,
      projectId: 1,
      ScrumQuestionId: item.id,
      responseText: item.answer,
    }));

    const files = questions
      .filter((item: any) => item.file)
      .map((item: any) => item.file);

    const formData = new FormData();
    formData.append("jsonData", JSON.stringify(jsonData));
    files.forEach((file: any) => {
      formData.append("files", file);
    });

    const response: any = await PostFiles(
      "app/Scrum/CreateScrumAnswers",
      formData
    );
    if (response.data) {
      Swal.fire({
        title: "Success",
        text: "Scrum Submitted Successfully!",
        icon: "success",
        confirmButtonColor: "#3085d6",
      });

      resetAllQuestions();
      setShow(false);
    } else {
      Swal.fire({
        title: "Error",
        text: "Error Submitting Scrum!",
        icon: "error",
        confirmButtonColor: "#3085d6",
      });
    }
  };

  return (
    <Modal
      show={show}
      onHide={handleModalClose}
      centered
      size="lg"
      className="mt-4"
    >
      <Modal.Header>
        <Modal.Title>Take Scrum</Modal.Title>
        <X onClick={handleModalClose} />
      </Modal.Header>
      <Modal.Body style={{ maxHeight: "60vh", overflowY: "auto" }}>
        {questions.map((item: any, index: number) => (
          <div key={index} className="mb-3">
            <label>
              {index + 1}. {item.question} <b className="text-danger">*</b>
            </label>
            <VoiceRecorder question={item} setQuestions={setQuestions} />
          </div>
        ))}
      </Modal.Body>
      <Modal.Footer>
        <button className="btn btn-secondary" onClick={handleModalClose}>
          Close
        </button>
        <button className="btn btn-primary" onClick={handleSubmit}>
          Submit
        </button>
      </Modal.Footer>
    </Modal>
  );
}

export default TakeScrum;
