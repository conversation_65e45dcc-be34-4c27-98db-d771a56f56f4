import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Grid, Typography } from "@mui/material";
import { SearchIcon } from "lucide-react";
import RefreshIcon from "@mui/icons-material/Refresh";
import { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import DataTable from "react-data-table-component";
import { Get } from "../../../Services/Axios";
import ScrumData from "./ScrumData";

function Scrum() {
  const startDateRef = useRef<HTMLInputElement>(null);
  const endDateRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);
  const [selectedScrum, setSelectedScrum] = useState<any>([]);
  const [show, setShow] = useState(false);
  const [scrum, setscrum] = useState([]);

  const [filter, setfilter] = useState({
    StartDate: null,
    EndDate: null,
  });

  async function fetchScrumData(startDate: any = "", endDate: any = "") {
    var url = "app/Scrum/GetAllEmployeeScrumDetailsForAdmin";

    if (startDate != "") {
      url += `?startDate=${startDate}`;
    }
    if (endDate != "") {
      url += `&endDate=${endDate}`;
    }
    setLoading(true);
    const response: any = await Get(url);
    setscrum(response.data || []);
    setLoading(false);
  }

  useEffect(() => {
    fetchScrumData();
  }, []);

  const ApplyFilter = async () => {
    setLoading(true);
    await fetchScrumData(filter?.StartDate, filter?.EndDate);
    setLoading(false);
  };

  const reset = () => {
    setfilter({
      StartDate: null,
      EndDate: null,
    });
    if (startDateRef.current) startDateRef.current.value = "";
    if (endDateRef.current) endDateRef.current.value = "";
    fetchScrumData();
  };

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => (
        <Button
          className="tableStyle"
          variant="contained"
          disabled={row.responses.length === 0}
          onClick={() => {
            setSelectedScrum(row.responses);
            setShow(true);
          }}
        >
          View Scrum
        </Button>
      ),
    },
    {
      field: "employeeName",
      name: "Employee Name",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => <p className="tableStyle">{row?.employeeName}</p>,
    },
    {
      field: "employeeCode",
      name: "Employee Code",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => <p className="tableStyle">{row?.employeeCode}</p>,
    },
    {
      field: "department",
      name: "Dxepartment",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => <p className="tableStyle">{row?.department}</p>,
    },
    {
      field: "createdDate",
      name: "Date",
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      flex: 1,
      selector: (row: any) => {
        if (row.responses.length > 0) {
          return (
            <p className="tableStyle">
              {row?.responses[0].createdDate?.slice(0, 10)}
            </p>
          );
        }
        return <p className="tableStyle">-</p>;
      },
    },
  ];
  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to="/Employee">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Scrum</Typography>
      </Breadcrumbs>
      <div className="well mx-auto mt-4">
        <div className="container">
          <div className="row">
            <div className="col-md-2">
              <div className="form-group">
                <label className="mx-1">Start Date</label>
                <input
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        StartDate: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                  ref={startDateRef}
                  type="date"
                  id="estimated-start-date"
                  placeholder="Estimated Start Date"
                  className="m-1  form-control"
                />
              </div>
            </div>
            <div className="col-md-2">
              <div className="form-group">
                <label className="mx-1">End Date</label>
                <input
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        EndDate: e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                  ref={endDateRef}
                  type="date"
                  id="estimated-End-date"
                  placeholder="Estimated End Date"
                  className="m-1  form-control"
                />
              </div>
            </div>

            {/* <div className="col-md-2">
              <div className="form-group">
                <label className="mx-1">Week Ending Date</label>
                <input
                  onChange={(e: any) => {
                    setfilter((prevState) => {
                      return {
                        ...prevState,
                        WeekEndingDate:
                          e.target.value == "" ? null : e.target.value,
                      };
                    });
                  }}
                  ref={weekEndingDateRef}
                  type="date"
                  id="estimated-End-date"
                  placeholder="Estimated End Date"
                  className="m-1  form-control"
                />
              </div>
            </div> */}
            <div className="col-md-12">
              <div className="row justify-content-end">
                <div className="col-auto">
                  <Button
                    variant="contained"
                    endIcon={<SearchIcon />}
                    className="mx-2 mt-4"
                    onClick={() => ApplyFilter()}
                  >
                    Search
                  </Button>
                  <Button
                    variant="contained"
                    endIcon={<RefreshIcon />}
                    className="mx-2 mt-4"
                    onClick={() => reset()}
                  >
                    Reset
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center mb-5">
        <Grid item xs={12} sm={11}>
          <Box style={{ width: "94vw" }}>
            <DataTable
              columns={columns}
              fixedHeader
              responsive
              persistTableHead
              progressPending={loading}
              data={scrum}
              customStyles={{
                table: {
                  style: {
                    height: "80vh",

                    border: "1px solid rgba(0,0,0,0.1)",
                  },
                },

                headRow: {
                  style: {
                    background: "#1e97e8",
                    fontSize: "16px",
                    color: "white",
                    fontFamily: "inherit",
                  },
                },
              }}
              pagination
              paginationPerPage={50}
              paginationRowsPerPageOptions={[50, 100, 200]}
              pointerOnHover={true}
            />
          </Box>
        </Grid>
      </div>
      <ScrumData show={show} setShow={setShow} questions={selectedScrum} />
    </>
  );
}

export default Scrum;
