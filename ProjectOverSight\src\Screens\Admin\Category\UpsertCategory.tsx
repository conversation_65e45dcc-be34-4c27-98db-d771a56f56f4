import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { CloseButton } from "react-bootstrap";
import { useForm } from "react-hook-form";
import { Category } from "../../../Models/Common/CommonMaster";
import { Regex } from "../../../Constants/Regex/Regex";
import { Post } from "../../../Services/Axios";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import Swal from "sweetalert2";
import { Dispatch, SetStateAction } from "react";

const formField = [
  "id",
  "categories",
  "subCategory",
  "categorySequence",
  "subCategorySequence",
  "uiApplicable",
  "userStoryApplicable",
  "isActive",
];

type UpsertCategoryProps = {
  open: boolean;
  setOpen: (args: boolean) => void;
  action: "CREATE" | "EDIT";
  setLoading: Dispatch<SetStateAction<boolean>>;
  category: Category | undefined;
};

export const UpsertCategory = ({
  open,
  setOpen,
  action,
  setLoading,
  category,
}: UpsertCategoryProps) => {
  const { register, handleSubmit, resetField } = useForm();

  async function onSubmitHandler(data: any) {
    const URL =
      action === "CREATE"
        ? "app/Common/AddCategory"
        : "app/Common/UpdateCategory";

    if (action === "EDIT") {
      data.isActive = JSON.parse(data.isActive);
      data.uiApplicable = JSON.parse(data.uiApplicable);
      data.userStoryApplicable = JSON.parse(data.userStoryApplicable);
    }

    const { error }: any = await Post(URL, data);

    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: `Category ${
          action === "CREATE" ? "added" : "updated"
        } successfully!`,
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setLoading((prev: boolean) => !prev);
    });
    handleClose();
  }

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  function handleClose() {
    reset();
    setOpen(false);
  }

  return (
    <Dialog open={open}>
      <form onSubmit={handleSubmit(onSubmitHandler)}>
        <DialogTitle
          className="fw-bold fs-5 d-flex justify-content-between align-items-center"
          style={{ background: "rgb(226,232,240)" }}
        >
          <h5>{action == "EDIT" ? "Edit Category" : "Add Category"}</h5>
          <CloseButton className="fs-6" onClick={handleClose} />
        </DialogTitle>
        <DialogContent className="row p-0 d-flex justify-content-center">
          <div className="row mt-3 d-flex justify-content-center ">
            <TextField
              className="col-md-5 mx-1"
              label="Category"
              {...register("categories", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                },
              })}
              defaultValue={category?.categories}
              required
            />
            <TextField
              className="col-md-5 mx-1"
              label="Sub Category"
              {...register("subCategory")}
              required
              defaultValue={category?.subCategory}
            />
          </div>
          <div className="row mt-3 d-flex justify-content-center">
            <TextField
              label="Category Sequence"
              className="col-md-5 mx-1"
              {...register("categorySequence", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                },
              })}
              defaultValue={category?.categorySequence}
              required
            />
            <TextField
              className="col-md-5 mx-1"
              label="Sub Category Sequence"
              {...register("subCategorySequence", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(Regex.NUMBER, "");
                },
              })}
              defaultValue={category?.subCategorySequence}
              required
            />
          </div>
          <div className="row mt-3 d-flex justify-content-center">
            <FormControl className="col-md-5 mx-1">
              <InputLabel id="uiApplicable">User Interface*</InputLabel>
              <Select
                labelId="uiApplicable"
                id="UIApplicable"
                label="User Interface*"
                {...register("uiApplicable")}
                defaultValue={category?.uiApplicable}
                required
              >
                <MenuItem value={true as any}>Applicable</MenuItem>
                <MenuItem value={false as any}>Not Applicable</MenuItem>
              </Select>
            </FormControl>
            <FormControl className="col-md-5 mx-1">
              <InputLabel id="usApplicable">User Story*</InputLabel>
              <Select
                labelId="usApplicable"
                id="USApplicable"
                label="User Story*"
                {...register("userStoryApplicable")}
                defaultValue={category?.userStoryApplicable}
                required
              >
                <MenuItem value={true as any}>Applicable</MenuItem>
                <MenuItem value={false as any}>Not Applicable</MenuItem>
              </Select>
            </FormControl>
          </div>
          <div className="row mt-3 d-flex justify-content-center">
            <FormControl className="col-md-5 mx-1">
              <InputLabel id="status">Status*</InputLabel>
              <Select
                labelId="status"
                id="Status"
                label="Status*"
                defaultValue={category?.isActive}
                {...register("isActive")}
                required
              >
                <MenuItem value={true as any}>Active</MenuItem>
                <MenuItem value={false as any}>In Active</MenuItem>
              </Select>
            </FormControl>
            <input className="col-md-5 mx-1" style={{ visibility: "hidden" }} />
          </div>
          <input
            type="text"
            {...register("createdBy")}
            value={"Admin"}
            hidden
          />
          <input
            type="text"
            {...register("updatedBy")}
            value={"Admin"}
            hidden
          />
          <input
            type="text"
            {...register("id")}
            value={category?.id || 0}
            hidden
          />
        </DialogContent>
        <DialogActions>
          <button
            className="btn text-light"
            style={{ backgroundColor: "rgb(239,68,68)" }}
            type="button"
            onClick={handleClose}
          >
            CANCEL
          </button>
          <button
            className="text-light btn"
            type="submit"
            style={{ backgroundColor: "rgb(59,130,246)" }}
          >
            SAVE
          </button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
