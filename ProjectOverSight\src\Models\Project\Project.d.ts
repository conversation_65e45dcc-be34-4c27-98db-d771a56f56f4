import { IBase, Nullable } from "../Common/BaseModel";
import { Task, TaskListDto } from "../Task/Task";
import { UserInterface } from "./UserInterface";
import { UserStory } from "./UserStory";

export interface Project extends IBase {
  id?: number | null;
  name?: string | null;
  description?: string | null; 
  type?: string | null;
  status?: string | null;
  percentage?: number | null;
  startDate?: string | null;
  endDate?: string | null;
  isActive?: boolean | null;
  actStartDate?: Date | null;
  estStartDate?: Date | null;
  actEndDate?: Date | null;
  projectName?: string | null;
  projectType?: string | null;
  priorities?: string | null;
  pageNumber?: number | null;
  pageSize?: number | null;
  direction?: string | null;
}

export type NProject = Nullable<Project>

export interface ProjectReportVM {
  highPriorityTasks: Array<TaskListDto>;
  todaysTasks: Array<TaskListDto>;
  userStory: Array<UserStory>;
  userInterface: Array<UserInterface>;
  task: Array<Task>;
  totalResource: number;
  totalResourceHours: number;
  completedPercentage: number;
  pendingPercentage: number;
}


export type ReportFilter = {
  TeamId?: number | null;
  TeamName?: string | null; 
  startDate: string | null,
  endDate: string | null,
  userStory: string | null,
  userInterface: string | null,
  category: string | null,
  subCategory: string | null
}

