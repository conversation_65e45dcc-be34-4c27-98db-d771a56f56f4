import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  TextField,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  DialogActions,
  Grid,
} from "@mui/material";
import * as React from "react";
import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import Swal from "sweetalert2";
import "./../../../App.css";
import { Get, Post } from "../../../Services/Axios";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { TransitionProps } from "@mui/material/transitions";
import Slide from "@mui/material/Slide";
import { Regex } from "../../../Constants/Regex/Regex";
import { CommonMaster } from "../../../Models/Common/CommonMaster";
const formField = [
  "UserName",
  "email",
  "skills",
  "phoneNumber",
  "secondaryPhoneNumber",
  "secondaryEmail",
  "department",
  "category",
  "Id",
  "Role",
  "OfficialIntime",
  "OfficialOuttime",
  "employeeCode",
  "location",
  "manager",
];

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export const EditEmployee = ({
  editEmployee,
  setEditEmployee,
  data,
  emailList,
  setReload,
  setRefetch,
  rows,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [officialIntime, setOfficialIntime] = useState<string>("");
  const [officialOuttime, setOfficialOuttime] = useState<string>("");
  const [commonMaster, setCommonMaster] = useState<any>([]);
  const [category, setCategory] = useState<any>([]);
  const [save, setSave] = useState<boolean>(false);
  const [selCat, setSelCat] = useState<any>("");
  var department: any = new Set();
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  function extractTime(timeString: string | undefined): string {
    if (!timeString) return "";

    const timeParts = timeString.split(":");
    if (timeParts.length < 2) return "";

    return `${timeParts[0]}:${timeParts[1]}`;
  }

  useEffect(() => {
    const response = Get("app/CommonMaster/GetCodeTableList");
    response.then((res: any) => {
      setCommonMaster(res.data);
    });
    if (data?.category) {
      setCategory([data?.category]);
      setSelCat(data?.category);
    }
    setOfficialOuttime(
      data?.officialIntime ? extractTime(data.officialOuttime) : ""
    );
    setOfficialIntime(
      data?.officialOuttime ? extractTime(data.officialOuttime) : ""
    );
  }, [data]);

  commonMaster?.map((e: any) => {
    if (e.codeType === "EmployeeCategory") {
      department.add(e.codeName);
    }
  });

  const handleDepartmentChange = (event: any) => {
    let temp: any = [];
    setSelCat("");
    commonMaster.map((e: any) => {
      if (event.target.value === e.codeName) {
        temp.push(e.codeValue);
      }
    });
    setCategory(temp);
  };

  const onSubmitHandler = async (data: any) => {
    data.OfficialIntime = officialIntime ? officialIntime : "NULL";
    data.OfficialOuttime = officialOuttime ? officialOuttime : "NULL";

    if (selCat === "") {
      setErrorMsg({
        message: "Please select category!",
        show: true,
      });
      return;
    }

    if (data?.phoneNumber.replaceAll(" ", "").length != 10) {
      setErrorMsg({
        message: "Please enter 10 digit phone number!",
        show: true,
      });
      return;
    }
    const email = emailList.find((e: any) => {
      return e.trim().toLowerCase() === data?.email.trim().toLowerCase();
    });
    const employeeCode = rows.find(
      (row: any) =>
        row.userId !== Number(data.Id) && row.employeeCode === data.employeeCode
    );

    if (employeeCode) {
      setErrorMsg({
        message: "Employee code already exists!",
        show: true,
      });
      return;
    }

    if (email) {
      setErrorMsg({
        message: "Email already exists!",
        show: true,
      });
      return;
    }
    setSave(true);
    data.isActive = data?.isActive === "Yes" ? true : false;
    const { error }: any = await Post("app/Employee/UpdateEmployee", data);
    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Data Not Updated!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Employee Updated Successfully!",
        icon: "success",
      };
    }
    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setRefetch((prev: boolean) => !prev);
      setReload((prev: boolean) => !prev);
    });
    handleClose();
  };
  const handleClose = () => {
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setEditEmployee({ edit: false });
    reset();
    setOfficialIntime("");
    setOfficialOuttime("");
  };

  const handleOfficialIntimeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const intime = event.target.value;
    setOfficialIntime(intime);

    if (intime.trim() === "") {
      setOfficialOuttime("");
      return;
    }

    const [hours, minutes] = intime.split(":").map(Number);
    const outHours = (hours + 9) % 24;
    let outtimeString = `${outHours < 10 ? "0" : ""}${outHours}`;

    if (!isNaN(minutes)) {
      outtimeString += `:${minutes < 10 ? "0" : ""}${minutes}`;
    } else {
      outtimeString += `:00`;
    }

    setOfficialOuttime(outtimeString);
  };

  return (
    <div>
      <Dialog
        open={editEmployee?.edit || false}
        TransitionComponent={Transition}
      >
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              display: "flex",
              backgroundColor: "#f0f0f0",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Edit Employee Details
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3 ">
                {errorMsg.message}.
              </Alert>
            )}
            <input {...register("Id")} value={data?.userId} hidden />
            <input {...register("Role")} value="role" hidden />
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
              <TextField
                required
                // className="col m-2 "
                {...register("UserName")}
                label="Name"
                defaultValue={data?.user?.name}
                variant="outlined"
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_SPACE, "");
                }}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                required
                // className="col m-2"
                {...register("phoneNumber", {
                  onChange: () => {
                    setErrorMsg({ show: false });
                  },
                })}
                defaultValue={data?.phoneNumber}
                label="Phone Number"
                variant="outlined"
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.NUMBER, "");
                }}
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                required
                // className="col m-2"
                defaultValue={data?.user?.email}
                {...register("email", {
                  onChange: () => {
                    setErrorMsg({ show: false });
                  },
                })}
                label="Email"
                variant="outlined"
                onChange={(e: any) => {
                  e.target.value = e.target.value;
                }}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth >
                <InputLabel required id="Team-Member">
                  Department
                </InputLabel>
                <Select
                  labelId="department"
                  required
                  label="Department"
                  variant="outlined"
                  defaultValue={data?.department}
                  {...register("department", {
                    onChange: (e: any) => handleDepartmentChange(e),
                  })}
                >
                  {[...department].map((e: any) => {
                    return (
                      <MenuItem value={e} key={e}>
                        {e}
                      </MenuItem>
                    );
                  })}
                </Select>
                </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                // className="col m-2"
                margin="dense"
                {...register("secondaryEmail", {
                  onChange: () => {
                    setErrorMsg({ show: false });
                  },
                })}
                defaultValue={data?.secondaryEmail}
                label="Secondary Email"
                variant="outlined"
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.EMAIL, "");
                }}
              />
              </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="Team-Member">Category</InputLabel>
                <Select
                  labelId="category"
                  required
                  label="Category"
                  variant="outlined"
                  defaultValue={data?.category}
                  {...register("category", {
                    onChange: (e: any) => {
                      setSelCat(e.target.value);
                      setErrorMsg({
                        message: "",
                        show: false,
                      });
                    },
                  })}
                >
                  {category.map((e: any) => {
                    return (
                      <MenuItem value={e} key={e}>
                        {e}
                      </MenuItem>
                    );
                  })}
                </Select>
              </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                type="time"
                // className="col m-2"
                {...register("OfficialIntime", {
                  onChange: () => {
                    setErrorMsg({ show: false });
                  },
                })}
                defaultValue={
                  data?.officialIntime ? extractTime(data.officialIntime) : ""
                }
                label="Official In time"
                variant="outlined"
                onChange={handleOfficialIntimeChange}
              />
               </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                // className="col m-2"
                label="Official Outtime"
                variant="outlined"
                value={officialOuttime}
                {...register("OfficialOuttime", {
                  onChange: () => {
                    setErrorMsg({ show: false });
                  },
                })}
                InputProps={{ readOnly: true }}
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel required id="location">
                  Work Location
                </InputLabel>
                <Select
                  labelId="location"
                  required
                  label="Work Location"
                  defaultValue={data?.location}
                  {...register("location", {
                    onChange: () => {
                      setErrorMsg({ show: false });
                    },
                  })}
                >
                  {commonMaster
                    .filter((x: CommonMaster) => x.codeType === "Location")
                    .sort(
                      (a: CommonMaster, b: CommonMaster) =>
                        a.displaySequence - b.displaySequence
                    )
                    .map((option: any) => (
                      <MenuItem key={option.codeValue} value={option.codeValue}>
                        {option.codeValue}
                      </MenuItem>
                    ))}
                </Select>
                </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                required
                // className="col m-2"
                {...register("employeeCode", {
                  onChange: () => {
                    setErrorMsg({ show: false });
                  },
                })}
                defaultValue={data.employeeCode}
                label="Employee Code"
                variant="outlined"
              />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="Is-Active">Is Active</InputLabel>
                <Select
                  labelId="Is-Active"
                  required
                  label="Is Active"
                  variant="outlined"
                  defaultValue={data?.isActive ? "Yes" : "No"}
                  {...register("isActive", {
                    onChange: (e: any) => {
                      setSelCat(e.target.value);
                      setErrorMsg({
                        message: "",
                        show: false,
                      });
                    },
                  })}
                >
                  <MenuItem value={"Yes"}>Yes</MenuItem>
                  <MenuItem value={"No"}>No</MenuItem>
                </Select>
                </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <TextField
                // className="col m-2"
                {...register("manager")}
                defaultValue={data?.manager}
                label="Manager"
                variant="outlined"
              />
            </FormControl>
            </Grid>
            </Grid>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                color="error"
                onClick={() => {
                  handleClose();
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="success"
                disabled={save}
              >
                {save ? "Saving..." : "Save"}
              </Button>
            </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
