import { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>readcrum<PERSON>,
  TextareaAutosize,
  Button,
  InputLabel,
  Grid,
} from "@mui/material";
import { Link } from "react-router-dom";
import LoadingMap from "../../assets/LocoMovingImage.gif";
import { Get, Post } from "../../Services/Axios";
import Swal from "sweetalert2";
import { ConvertDate, ConvertTime } from "../../Utilities/Utils";
import { AlertOption } from "../../Models/Common/AlertOptions";
import Container from "@mui/material/Container";
import { Regex } from "../../Constants/Regex/Regex";
import LocationOffIcon from '@mui/icons-material/LocationOff';

type LoginDetails = {
  id?: number;
  inTime: Date | null;
  outTime: Date | null;
  comments?: string;
  latitude?: number;
  longitude?: number;
  attendanceType?: string | null;
  status?: string;
};

export const EmployeeTime = () => {
  const [comment, setComment] = useState<string>("");
  const [refetch, setRefetch] = useState<boolean>(false);
  const [coordinate, setCoordinate] = useState<{ latitude: number; longitude: number }>({ latitude: 0, longitude: 0 });
  const [loading, setLoading] = useState<boolean>(false);
  const [isClockIn, setIsClockIn] = useState<boolean>(false);
  const commentRef = useRef<any>();
  const [loginDetails, setLoginDetails] = useState<any>([]);
  // const [coordinate, setCoordinate] = useState<coordinate>({
  //   latitude: 0,
  //   longitude: 0,
  // });

  async function fetchLoginDetails() {
    debugger
    const response: any = await Get("app/EmployeeTime/GetEmployeeTimeDetails");
    setLoginDetails(response?.data || []);
    setIsClockIn(response?.data?.length == 0 ? false : true);
  }

  useEffect(() => {
    fetchLoginDetails();
  }, [refetch]);

  useEffect(() =>{
    getLocation();
  },[coordinate])


  function getLocation() {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCoordinate({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        () => {}
      );
    } else {
      alert("Geolocation is not available in this browser.");
    }
  }

  function calculateHourDifference(start: any, end: any) {
    if (!(start instanceof Date) || !(end instanceof Date)) {
      return 0;
    }

    const diffMilliseconds = Math.abs(end.getTime() - start.getTime());
    const diffHours = diffMilliseconds / (1e3 * 60 * 60);
    return diffHours;
  }

  function determineAttendanceType(
    inTime: Date | null,
    outTime: Date | null
  ): string {
    if (inTime && outTime) {
      const diffHours = calculateHourDifference(inTime, outTime);
      return diffHours >= 8 ? "Full Day" : "Half Day";
    }
    return "";
  }

  function determineStatus(inTime: Date | null) {
    if (!inTime) return "Leave";
    const officeTime = new Date(inTime);
    officeTime.setHours(10, 5, 1);
    const InTime = new Date(inTime);
    if (InTime.getTime() > officeTime.getTime()) return "Late";
    return "On Time";
  }

  async function handleSave() {
    if (coordinate.latitude === 0 || coordinate.longitude === 0) {
      Swal.fire({ 
        title: "Error",
        text: "Turn on location?",
        icon: "error",
      });
      return;
    }

    setLoading(true);
    setRefetch(!refetch);
    setLoading(false);
    if (commentRef.current) commentRef.current.value = "";

    var id = 0;
    var inTime: Date | null = null;
    var outTime: Date | null = null;
    var lastIdx: number = loginDetails.length - 1;

    if (loginDetails.length === 0 || (loginDetails[lastIdx].inTime !== null && loginDetails[lastIdx].outTime !== null)) {
      id = 0;
      inTime = new Date();
      setIsClockIn(true); 
    } else if (loginDetails[lastIdx].inTime !== null && loginDetails[lastIdx].outTime === null) {
      id = loginDetails[lastIdx].id;
      inTime = loginDetails[lastIdx].inTime;
      outTime = new Date();
      setIsClockIn(false); 
    }

    const attendanceType = determineAttendanceType(inTime, outTime);

    const status = determineStatus(inTime);

    const loginDetail: LoginDetails = {
      id: id,
      inTime: inTime,
      outTime: outTime,
      comments: comment || "",
      latitude: coordinate.latitude,
      longitude: coordinate.longitude,
      attendanceType: attendanceType,
      status: status,
    };

    const { error }: any = await Post(
      "app/EmployeeTime/AddEmployeeTimeDetails",
      loginDetail
    );
    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Details Added successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    });

    setRefetch((prev) => !prev);
    setLoading(false);
    if (commentRef.current) commentRef.current.value = "";
  }
  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link to="/Employee">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Employee Time</Typography>
      </Breadcrumbs>
      <Container maxWidth="xl">
        <div className="w-100 d-flex flex-column-reverse flex-md-row ">
          <div className="m-2 w-100 border" style={{ height: 500 }}>
            <Grid>
              <Typography
                className="mt-3"
                sx={{
                  textAlign: "center",
                  fontWeight: "bold",
                  fontSize: "25px",
                }}
              >
                Your Login Time
              </Typography>
             
{coordinate.latitude < 1 && coordinate.longitude < 1 ? (
  
              <div>
                    {/* {skleton.map((e) => (
                      <Skeleton key={e + 7} />
                    ))} */}
                     <div className="d-flex flex-column justify-content-center align-items-center" style={{marginTop:'120px'}}>
                 <LocationOffIcon  sx={{ fontSize: 150, color:"orangered" }} /> 
                 <Typography
  variant="h5"
  sx={{
    color: 'blue',
    textAlign: 'center',
    fontWeight: 'bold',
    marginTop: '20px',
  }}
>
  Please Allow Location Permission <span style={{color:'orangered'}}> ....!</span>
</Typography>

                </div>
                  </div>
              ): (
                <>
              <div
                className=" mb-5 mt-1"
                style={{ height: "18vh" }}
              >
                <table className="table table-bordered mx-auto w-50 mt-5" >
                  <thead
                    style={{
                      position: "static",
                      top: " 50px",
                      background: "white",
                      color: "black",
                    }}
                  >
                    <tr className="bg-info text">
                      <th>In Time</th>
                      <th>Out Time</th>
                      <th style={{textAlign:'center'}}>Comments</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loginDetails?.map((e: any, index: number) => {
                      return (
                        <tr key={index}>
                          <td>
                            {ConvertDate(e?.inTime)} <br />
                            {ConvertTime(e?.inTime, "")}
                          </td>
                          <td>
                            {ConvertDate(e?.outTime)} <br />
                            {ConvertTime(e?.outTime, "")}
                          </td>
                          <td>{e?.comments}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
              <div className="d-flex flex-column justify-content-center align-items-center">
              
                <>
                <InputLabel className="mb-3 fw-bold" style={{ width: "26vw" }}>
                  Comment
                </InputLabel>

                <TextareaAutosize
                  className="border border-1 border-dark"
                  style={{ width: "80%", maxWidth: 400, minHeight: 100}}
                  ref={commentRef}
                  onChange={(e) => {
                    e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                    setComment(e.target.value);
                  }}
                />

                <Button
                  variant="contained"
                  className="m-3"
                  color={loading ? "inherit" : isClockIn ? "error" : "success"}
                  onClick={handleSave}
                  disabled={loading}
                >
                  {loading ? "Saving..." : isClockIn ? "Clock Out" : "Clock In"}
                </Button>
              </>
              
              
              </div>
              </>
               )}
            </Grid>
         
          </div>

          <iframe
            className="m-2 w-100 border "
            width="750"
            height="500"
            style={{
              background: `url(${LoadingMap})`,
              objectFit: "contain",
            }}
            src={`https://maps.google.com/maps?q=${coordinate.latitude},${coordinate.longitude}&t=&z=13&ie=UTF8&iwloc=&output=embed`}
          ></iframe>
        </div>
      </Container>
    </>
  );
};
