import { useState, useEffect, useRef } from "react";
import Box from "@mui/material/Box";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import { Typography, Grid, Tooltip } from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import { Get, Post } from "../../../Services/Axios";
import Button from "@mui/material/Button";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import { TaskFilter } from "../../../Models/Task/TaskFilter";
import Select from "react-select";
import { Link } from "react-router-dom";
import { DownloadTaskList } from "../../../Services/TaskService";
import {
  ConvertDate,
  ConvertToISO,
  WeekEndingDate,
} from "../../../Utilities/Utils";
import Swal from "sweetalert2";
import AddIcon from "@mui/icons-material/Add";
// import { SelectCategory } from "./SelectCategory";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DataTable from "react-data-table-component";
import { AssignTask } from "./AssignTask";
import { ViewTask } from "./ViewTask";
import { EditTask } from "./EditTask";
import TodayIcon from "@mui/icons-material/Today";
import { AddDailyTask } from "./AddDailyTask";
import Assign from "../../../assets/Assign.png";
import OfflinePinIcon from "@mui/icons-material/OfflinePin";
import { FILTER } from "../../../Constants/Task/Task";
import { Team } from "../../../Models/Team/Team";
// import { Project } from "../../../Models/Project/Project";
// import SelectProject from "./SelectProject";
import { CreateTask } from "./AddTask/CreateTask";
import ErrorIcon from "@mui/icons-material/Error";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import "../../../App.css";
import BackDrop from "../../../CommonComponents/BackDrop";

const UNASSIGNED = "unassigned";

export const EmpTaskList = () => {
  const [rows, setRows] = useState<any>([]);
  const [open, setOpen] = useState<boolean>(false);
  const json: any = sessionStorage.getItem("user") || null;
  const sessionUser = JSON.parse(json);
  const [categories, setcategories] = useState<any>([]);
  const [subCategories, setSubCategories] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [reload, setReload] = useState<boolean>(true);
  const [teamNames, setTeamNames] = useState<string[]>([]);
  const [selecteRow, setSelectRow] = useState([]);
  const nameRef = useRef<HTMLInputElement>(null);
  const [Page, setPage] = useState(0);
  const projectNameRef = useRef<any>();
  const statusRef = useRef<any>();
  const percentageRef = useRef<HTMLInputElement>(null);
  const categoryRef = useRef<any>();
  const subCategoryRef = useRef<any>();
  const actTimeRef = useRef<HTMLInputElement>(null);
  const estTimeRef = useRef<HTMLInputElement>(null);
  const actStartDateRef = useRef<HTMLInputElement>(null);
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const estStartDateRef = useRef<HTMLInputElement>(null);
  const estEndDateRef = useRef<HTMLInputElement>(null);
  const weekEndDateRef = useRef<HTMLInputElement>(null);
  const [filter, setfilter] = useState<TaskFilter>(FILTER);
  const taskNameRef = useRef<any>(null);
  const teamNameRef = useRef<any>(null);
  const empNameRef = useRef<HTMLInputElement>(null);

  var categorySet = new Set<any>();
  var category: string[] = [];
  var ProjectSet = new Set<any>();
  var [Projects, setProjects] = useState<string[]>([]);

  const [taskListView, setTaskListView] = useState<any>({
    view: false,
    edit: false,
    add: false,
    assign: false,
    daily: false,
    selectProject: false,
  });

  const columns: any = [
    {
      name: "Action",
      width: "12rem",
      selector: (row: any) => (
        <>
          <Tooltip
            className="mx-2"
            title="View"
            onClick={() => {
              setTaskListView({ view: true });
              setSelectRow(row);
            }}
          >
            <VisibilityIcon className="fs-4 text-info" />
          </Tooltip>

          <Tooltip
            className="mx-2"
            title={row.percentage === 100 ? "Completed" : "Edit Task"}
            onClick={() => {
              if (!(row.percentage === 100)) {
                setTaskListView({ edit: true });
                setSelectRow(row);
              }
            }}
            describeChild={row.percentage === 100}
          >
            {row.percentage === 100 ? (
              <TaskAltIcon className="fs-4 text-success" />
            ) : (
              <EditIcon className="fs-4 text-warning" />
            )}
          </Tooltip>
          {row.status?.toLowerCase() !== UNASSIGNED ? (
            <>
              {row?.percentage !== 100 ? (
                <Tooltip
                  className="mx-2"
                  onClick={() => {
                    if (
                      !row.dayPlanAdded &&
                      row.weekEndingDate.slice(0, 10) ===
                        ConvertToISO(WeekEndingDate())
                    ) {
                      setSelectRow(row);
                      setTaskListView({ daily: true });
                    }
                  }}
                  title={
                    row.dayPlanAdded ||
                    row.weekEndingDate.slice(0, 10) !==
                      ConvertToISO(WeekEndingDate())
                      ? "Pending"
                      : "Add Daily task"
                  }
                >
                  {row.dayPlanAdded ||
                  row.weekEndingDate.slice(0, 10) !==
                    ConvertToISO(WeekEndingDate()) ? (
                    <ErrorIcon color="warning" />
                  ) : (
                    <TodayIcon />
                  )}
                </Tooltip>
              ) : (
                <Tooltip className="mx-2" title={"Completed"}>
                  <OfflinePinIcon style={{ color: "#04d93d" }} />
                </Tooltip>
              )}
            </>
          ) : (
            <Tooltip
              className="mx-2"
              onClick={() => {
                if (row.status?.toLowerCase() === UNASSIGNED) {
                  setSelectRow(row);
                  setTaskListView({ assign: true });
                }
              }}
              title={
                row.status?.toLowerCase() === UNASSIGNED
                  ? "Assign Task"
                  : row.status
              }
            >
              <img
                src={Assign}
                style={{ objectFit: "contain" }}
                width={25}
                height={25}
              />
            </Tooltip>
          )}
        </>
      ),
    },
    {
      name: "Task Name",
      width: "15rem",
      selector: (row: any) => (
        <Tooltip title={row.name} style={{ textDecoration: "none" }}>
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? "text-danger"
                : ""
            }`}
          >
            {row?.name}
          </p>
        </Tooltip>
      ),
    },
    {
      name: "Project Name",
      width: "15rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row?.projectName}
        </p>
      ),
    },
    {
      name: "Priority",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.priority || "-"}
        </p>
      ),
    },
    {
      name: "Team Name",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.teamName}
        </p>
      ),
    },
    {
      name: "Status",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.status || "-"}
        </p>
      ),
    },
    {
      name: "Week Ending Date",
      type: "Date",
      width: "10rem",
      selector: (row: any) => {
        const result = ConvertDate(row.weekEndingDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? "text-danger"
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "Actual Start Date",
      width: "10rem",
      selector: (row: any) => {
        const result = ConvertDate(row.actualStartDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? "text-danger"
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "Actual End Date",
      width: "10rem",
      selector: (row: any) => {
        const result = ConvertDate(row.actualEndDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? "text-danger"
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "Estimated Start Date",
      type: "Date",
      width: "12rem",
      selector: (row: any) => {
        const result = ConvertDate(row.estimateStartDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? "text-danger"
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "Estimated End Date",
      width: "12rem",
      selector: (row: any) => {
        const result = ConvertDate(row.estimateEndDate);
        return (
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? "text-danger"
                : ""
            }`}
          >
            {result}
          </p>
        );
      },
    },
    {
      name: "US Name",
      width: "15rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.usName || "-"}
        </p>
      ),
    },
    {
      name: "UI Name",
      width: 200,
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.uiName || "-"}
        </p>
      ),
    },
    {
      name: "Description",
      width: "25rem",
      selector: (row: any) => (
        <Tooltip title={row.description} style={{ textDecoration: "none" }}>
          <p
            className={`tableStyle ${
              ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
              row?.percentage != 100
                ? "text-danger"
                : ""
            }`}
          >
            {row?.description.slice(0, 45)}
            {row?.description.length > 45 ? "..." : ""}
          </p>
        </Tooltip>
      ),
    },
    {
      name: "Category",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.category || "-"}
        </p>
      ),
    },
    {
      name: "Sub Category",
      width: "10rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.subCategory || "-"}
        </p>
      ),
    },
    {
      name: "Percentage (%)",
      right: true,
      width: "9rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.percentage || "-"}
        </p>
      ),
    },
    {
      name: "Estimated Time (hrs)",
      right: "true",
      width: "12rem",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.estimateTime || "-"}
        </p>
      ),
    },
    {
      name: "Actual Time (hrs)",
      width: "12rem",
      right: "true",
      selector: (row: any) => (
        <p
          className={`tableStyle ${
            ConvertToISO(row.weekEndingDate) < ConvertToISO(new Date()) &&
            row?.percentage != 100
              ? "text-danger"
              : ""
          }`}
        >
          {row.actualTime || "-"}
        </p>
      ),
    },
  ];

  async function GetData() {
    let teamList: any = await Get("app/Employee/GetEmployeeTeamList");
    let projectList: any = await Get("app/Project/GetEmployeeProjectlist");

    let teamNames: Array<string> = [];
    let projects: Array<string> = [];

    teamList?.data?.forEach((team: Team) => {
      teamNames?.push(team?.name);
    });

    projectList?.data?.forEach((project: any) => {
      projects?.push(project?.name);
    });

    setTeamNames([...teamNames]);
    setProjects(projects);
  }

  useEffect(() => {
    setLoading(true);
    let taskList = Post(
      `app/Task/GetEmployeeTaskListSP?employeeId=${sessionUser?.employeeId}`,
      filter
    );

    let categoriesList = Get("app/Project/GetCategoriesList");
    GetData();
    taskList.then((response: any) => {
      setRows(response?.data || []);
      setLoading(false);
    });

    categoriesList.then((response: any) => {
      setcategories(response?.data || []);
    });

    rows?.forEach((row: any) => {
      ProjectSet.add(row?.projectName);
    });

    let dates = new Set<string>();

    rows?.forEach((row: any) => {
      dates.add(row.weekEndingDate);
    });
  }, [reload]);

  categories?.forEach((element: any) => {
    categorySet.add(element.categories);
  });

  category = [...categorySet];
  category.sort((a: any, b: any) => {
    return a.toLowerCase() < b.toLowerCase() ? -1 : 1;
  });

  const handleCategoryChange = (event: any) => {
    let temp: any = [];
    setfilter((prevState: TaskFilter) => {
      return { ...prevState, category: event?.label };
    });
    categories?.forEach((element: any) => {
      if (element.categories === event?.label) {
        temp.push(element.subCategory);
      }
    });

    temp.sort((a: any, b: any) => {
      return a.toLowerCase() < b.toLowerCase() ? -1 : 1;
    });
    setSubCategories(temp);
  };

  const handleChangePage = (page: number) => {
    setfilter((prevState) => {
      return {
        ...prevState,
        direction: Page > page ? "PREVIOUS" : "NEXT",
      };
    });
    setfilter((prevState) => {
      return {
        ...prevState,
        pageNumber: page,
      };
    });
    setPage(page);
    setLoading((prev) => !prev);
    setReload((prev) => !prev);
  };

  const handleChangeRowsPerPage = (currentRowsPerPage: number, _: number) => {
    setfilter((prevState) => {
      return {
        ...prevState,
        pageSize: currentRowsPerPage,
      };
    });
    setfilter((prevState) => {
      return {
        ...prevState,
        pageNumber: 0,
      };
    });
    setLoading((prev) => !prev);
    setReload((prev) => !prev);
  };


  function ApplyFilter() {
    if (filter.actStartDate != null && filter.estStartDate != null) {
      Swal.fire({
        text: "Please Select Either Actual Dates or Estimated Dates!",
        icon: "warning",
        confirmButtonColor: "#3085d6",
      });
      return;
    }

    if (filter.actStartDate != null) {
      if (filter.actEndDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
    }

    if (filter.estStartDate != null) {
      if (filter.estEndDate == null) {
        estEndDateRef.current?.focus();
        return;
      }
    }
    setReload((prev) => !prev);
  }

  function reset() {
    setfilter(FILTER);
    if (nameRef.current) nameRef.current.value = "";
    if (projectNameRef.current) projectNameRef.current.clearValue();
    if (statusRef.current) statusRef.current.clearValue();
    if (categoryRef.current) categoryRef.current.clearValue();
    if (subCategoryRef.current) subCategoryRef.current.clearValue();
    if (percentageRef.current) percentageRef.current.value = "";
    if (actTimeRef.current) actTimeRef.current.value = "";
    if (estTimeRef.current) estTimeRef.current.value = "";
    if (actStartDateRef.current) actStartDateRef.current.value = "";
    if (actEndDateRef.current) actEndDateRef.current.value = "";
    if (estStartDateRef.current) estStartDateRef.current.value = "";
    if (estEndDateRef.current) estEndDateRef.current.value = "";
    if (weekEndDateRef.current) weekEndDateRef.current.value = "";
    if (taskNameRef.current) taskNameRef.current.clearValue();
    if (teamNameRef.current) teamNameRef.current.clearValue();
    if (empNameRef.current) empNameRef.current.value = "";
    setReload((prev) => !prev);
  }

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to="/Employee">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Task</Typography>
      </Breadcrumbs>
      <div className="well mx-auto mt-4">
        <div className="row">
          <div className="col-sm-2">
            <div className="form-group">
              <label>Team Name</label>
              <Select
                aria-label="Floating label select example"
                name="status"
                ref={teamNameRef}
                className="mt-1"
                onChange={(selectedOption: any) => {
                  setfilter((prevState: TaskFilter) => ({
                    ...prevState,
                    teamName: selectedOption ? selectedOption.label : null,
                  }));
                }}
                options={teamNames.map((name: string) => ({
                  value: name,
                  label: name,
                }))}
                isSearchable={true}
              />
            </div>
          </div>

          <div className="col-sm-2">
            <div className="form-group">
              <label>Project Name</label>
              <Select
                id="project-name"
                ref={projectNameRef}
                isMulti
                className="col mt-1 custom-select"
                onChange={(selectedOption: any) => {
                  var projectName: string[] = [];
                  selectedOption.forEach((e: any) => {
                    projectName.push(e.value);
                  });
                  setfilter((prevState: TaskFilter) => {
                    return {
                      ...prevState,
                      projectName: projectName.length > 0 ? projectName : null,
                    };
                  });
                }}
                options={Projects?.map((e: any) => ({
                  label: e,
                  value: e,
                }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                aria-label="Floating label select example"
                name="status"
                isMulti
                ref={statusRef}
                className="mt-1"
                onChange={(selectedOption: any) => {
                  var status: string[] = [];
                  selectedOption.forEach((e: any) => {
                    status.push(e.value);
                  });
                  setfilter((prevState) => ({
                    ...prevState,
                    status: status.length > 0 ? status : null,
                  }));
                }}
                options={[
                  {
                    label: "Un Assigned",
                    value: "Un Assigned",
                  },
                  {
                    label: "Assigned",
                    value: "Assigned",
                  },
                  {
                    label: "Completed",
                    value: "Completed",
                  },
                  {
                    label: "Ready-For-UAT",
                    value: "Ready-For-UAT",
                  },
                  {
                    label: "In Progress",
                    value: "In Progress",
                  },
                  {
                    label: "On Hold",
                    value: "On Hold",
                  },
                ]}
                isSearchable={true}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Percentage</label>
              <input
                id="percentage"
                maxLength={3}
                className="m-1 form-control col"
                placeholder="Percentage"
                ref={percentageRef}
                onChange={(e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  setfilter((prevState: TaskFilter) => ({
                    ...prevState,
                    percentage: parseInt(e.target.value),
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Actual Time</label>
              <input
                id="actual-time"
                className="m-1 form-control col"
                ref={actTimeRef}
                placeholder="Actual Time"
                onChange={(e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  setfilter((prevState: TaskFilter) => ({
                    ...prevState,
                    actualTime: parseInt(e.target.value),
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Estimated Time</label>
              <input
                id="estimated-time"
                className="m-1 col form-control"
                ref={estTimeRef}
                placeholder="Estimated Time"
                onChange={(e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                  setfilter((prevState: TaskFilter) => ({
                    ...prevState,
                    estimatedTime: parseInt(e.target.value),
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Category</label>
              <Select
                id="category-simple-select"
                ref={categoryRef}
                className="col mt-1"
                onChange={(selectedOption: any) => {
                  handleCategoryChange(selectedOption);
                }}
                options={category.map((opt: any) => ({
                  label: opt,
                  value: opt,
                }))}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Sub Category</label>
              <Select
                id="sub-category-simple-select"
                className="mt-1"
                ref={subCategoryRef}
                options={subCategories.map((opt: any) => ({
                  label: opt,
                  value: opt,
                }))}
                onChange={(selectedOption: any) => {
                  setfilter((prevState: TaskFilter) => ({
                    ...prevState,
                    subCategory: selectedOption ? selectedOption.value : null,
                  }));
                }}
                isSearchable={true}
                styles={{
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 1000,
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label className="mx-1">Estimated Start Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: TaskFilter) => {
                    return {
                      ...prevState,
                      estStartDate:
                        e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                ref={estStartDateRef}
                type="date"
                id="estimated-start-date"
                placeholder="Estimated Start Date"
                className="m-1  form-control"
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label className="mx-1">Estimated End Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: TaskFilter) => {
                    return {
                      ...prevState,
                      estEndDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                ref={estEndDateRef}
                type="date"
                id="estimated-end-date"
                placeholder="Estimated End Date"
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="container">
            <div className="row">
              <div className="col-md-2">
                <div className="form-group">
                  <label className="mx-1">Actual Start Date</label>
                  <input
                    onChange={(e: any) => {
                      setfilter((prevState: TaskFilter) => {
                        return {
                          ...prevState,
                          actStartDate:
                            e.target.value == "" ? null : e.target.value,
                        };
                      });
                    }}
                    type="date"
                    id="actual-start-date"
                    placeholder="Actual Start Date"
                    ref={actStartDateRef}
                    className="m-1 col form-control"
                  />
                </div>
              </div>
              <div className="col-md-2">
                <div className="form-group">
                  <label className="mx-1">Actual End Date</label>
                  <input
                    onChange={(e: any) => {
                      setfilter((prevState: TaskFilter) => {
                        return {
                          ...prevState,
                          actEndDate:
                            e.target.value == "" ? null : e.target.value,
                        };
                      });
                    }}
                    type="date"
                    id="actual-end-date"
                    placeholder="Actual End Date"
                    ref={actEndDateRef}
                    className="m-1 col form-control"
                  />
                </div>
              </div>
              <div className="col-sm-2">
                <div className="form-group">
                  <label className="mx-1">Week Ending Date</label>
                  <input
                    onChange={(e: any) => {
                      setfilter((prevState: TaskFilter) => {
                        return {
                          ...prevState,
                          weekEndingDate:
                            e.target.value == "" ? null : e.target.value,
                        };
                      });
                    }}
                    type="date"
                    id="actual-end-date"
                    placeholder="Actual End Date"
                    ref={weekEndDateRef}
                    className="m-1 col form-control"
                  />
                </div>
              </div>
              <div className="col-md-6">
                <div className="row justify-content-end">
                  <div className="col-auto">
                    <Button
                      variant="contained"
                      endIcon={<SearchIcon />}
                      className="mx-3 mt-4"
                      onClick={() => ApplyFilter()}
                    >
                      Search
                    </Button>
                    <Button
                      variant="contained"
                      endIcon={<RefreshIcon />}
                      className="mx-3 mt-4"
                      onClick={() => reset()}
                    >
                      Reset
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center mb-5">
        <div className="col-12 col-s-3">
          <Grid>
            <Button
              variant="contained"
              className="mb-2 float-md-start"
              onClick={() => setOpen(true)}
              sx={{ ml: "3%" }}
            >
              Add Task
              <AddIcon className="mx-1" />
            </Button>
            <Button
              variant="contained"
              className=" button-down mb-2 float-md-end"
              style={{ marginLeft: "5px" }}
              onClick={() => {
                if (rows.length == 0) {
                  Swal.fire({
                    text: "No data to download!",
                    icon: "warning",
                    confirmButtonColor: "#3085d6",
                  });
                  return;
                }
                DownloadTaskList(rows);
              }}
              sx={{ mr: "3%" }}
            >
              Download
              <DownloadIcon className="mx-1" />
            </Button>
          </Grid>
        </div>
        <div
          className="responsive-div "
          style={{
            marginTop: "4%",
            width: "94vw",
          }}
        >
          <Grid item xs={12} sm={11}>
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                columns={columns}
                fixedHeader={true}
                responsive={true}
                persistTableHead
                progressPending={loading}
                data={rows}
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },
                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
                paginationComponentOptions={{
                  rowsPerPageText: "Rows per page:",
                  rangeSeparatorText: "of",
                  noRowsPerPage: false,
                  selectAllRowsItem: false,
                  selectAllRowsItemText: "All",
                }}
                onChangePage={handleChangePage}
                onChangeRowsPerPage={handleChangeRowsPerPage}
                paginationTotalRows={rows?.length && rows[0]?.totalCount}
                paginationServer
              />
            </Box>
          </Grid>
        </div>
        <AssignTask
          openDialog={taskListView}
          setOpenDialog={setTaskListView}
          selectedRow={selecteRow}
          setReload={setReload}
        />
        <ViewTask
          openDialog={taskListView}
          setOpenDialog={setTaskListView}
          Data={selecteRow}
        />
        <EditTask
          openDialog={taskListView}
          setOpenDialog={setTaskListView}
          setReload={setReload}
          Data={selecteRow}
        />
        {/* <SelectProject
          openDialog={taskListView}
          setOpenDialog={setTaskListView}
        /> */}
        {/* <SelectCategory
          openDialog={taskListView}
          setOpenDialog={setTaskListView}
        /> */}
        <AddDailyTask
          openDialog={taskListView}
          setOpenDialog={setTaskListView}
          Data={selecteRow}
          setReload={setReload}
        />
        <CreateTask open={open} setOpen={setOpen} setReload={setReload} />
      </div>
      <BackDrop open={loading} />
    </>
  );
};
