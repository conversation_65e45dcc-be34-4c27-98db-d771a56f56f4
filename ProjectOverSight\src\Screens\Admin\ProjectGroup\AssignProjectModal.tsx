// import Multiselect from "multiselect-react-dropdown";
import { Dispatch, SetStateAction } from "react";
// import { ProjectGroupMapper } from "../../../Models/Common/AssignmentMapper";
// import { Roles } from "../../../Constants/Common/Roles";
// import Swal from "sweetalert2";
// import { AlertOption } from "../../../Models/Common/AlertOptions";
// import { Post } from "../../../Services/Axios";
import { ProjectGroup } from "../../../Models/Project/ProjectObjective";

type AssignProjectModalProps = {
  showModal: boolean;
  projectId: number;
  setShowModal: (prev: boolean) => void;
  lefts: ProjectGroup[];
  rights: ProjectGroup[];
  setRight: Dispatch<SetStateAction<ProjectGroup[]>>;
  setReload: Dispatch<SetStateAction<boolean>>;
};

export const AssignProjectModal = ({
  showModal,
}: // projectId,
// setShowModal,
// lefts,
// rights,
// setRight,
// setReload,
AssignProjectModalProps) => {
  // const [assignmentMapper, setAssignmentMapper] = useState<ProjectGroupMapper>({
  //   assignedList: [],
  //   unassignedList: lefts,
  // });

  // function toProjectGroup(projectId: number): ProjectGroupMapper {
  //   return {
  //     projectId: projectId,
  //     CreatedBy: Roles.ADMIN,
  //     UpdatedBy: Roles.ADMIN,
  //   };
  // }

  // function handleSelect(
  //   selectedProjects: ProjectGroup[],
  //   selectedProject: ProjectGroup
  // ) {
  //   setAssignmentMapper((prevMapper: any) => {
  //     const unassignedList = prevMapper.unassignedList.filter(
  //       (x: any) => x.projectId !== selectedProject.id
  //     );

  //     const assignedList = [
  //       ...prevMapper.assignedList,
  //       toProjectGroup(selectedProject.id),
  //     ];

  //     return { assignedList, unassignedList };
  //   });
  // }

  // function handleRemove(
  //   selectedProjects: ProjectGroup[],
  //   selectedProject: ProjectGroup
  // ) {
  //   setAssignmentMapper((prevMapper: any) => {
  //     const unassignedList = [
  //       ...prevMapper.unassignedList,
  //       toProjectGroup(selectedProject.id),
  //     ];

  //     const assignedList = prevMapper.assignedList.filter(
  //       (x: any) => x.projectId !== selectedProject.id
  //     );

  //     return { assignedList, unassignedList };
  //   });
  // }

  // debugger;
  // async function Save() {
  //   const { error }: any = await Post(
  //     "app/Project/AssignProjectToProjectGroup",
  //     assignmentMapper
  //   );

  //   var option: AlertOption;
  //   if (error) {
  //     option = {
  //       title: "Error",
  //       text: "Error Occured While Assigning!",
  //       icon: "error",
  //     };
  //   } else {
  //     option = {
  //       title: "Success",
  //       text: "Employee Assigned Successfully!",
  //       icon: "success",
  //     };
  //   }

  //   Swal.fire({
  //     ...option,
  //     showConfirmButton: true,
  //   });
  //   handleCloseModal();
  // }

  // const handleCloseModal = () => {
  //   setAssignmentMapper({ assignedList: [], unassignedList: [] });
  //   setShowModal(false);
  //   setRight([]);
  //   setReload((prev) => !prev);
  // };

  return (
    <div
      className={`modal  fade ${showModal ? "show" : ""}`}
      style={{ display: showModal ? "block" : "none", marginTop: "7%" }}
      tabIndex={-1}
      aria-labelledby="exampleModalLabel"
      aria-hidden={!showModal}
    >
      {/* <div className="modal-dialog">
        <div className="modal-content">
          <div className="modal-header">
            <h1
              className="modal-title"
              style={{ fontSize: "1.25rem", fontFamily: "Monospace" }}
            >
              Edit Assignee
            </h1>
            <button
              type="button"
              className="btn-close"
              onClick={handleCloseModal}
              aria-label="Close"
            ></button>
          </div>
          <div className="modal-body">
            <label className="mb-2 fs-8" style={{ fontWeight: "bolder" }}>
              Assign To:
            </label>
            <Multiselect
              options={lefts}
              selectedValues={rights}
              onSelect={(selectedList, selectedItem) =>
                handleSelect(selectedList, selectedItem)
              }
              onRemove={(selectedList, selectedItem) => {
                handleRemove(selectedList, selectedItem);
              }}
              displayValue="name"
              style={{
                chips: {
                  background: "#6777ef",
                },
                searchBox: {
                  border: "1px solid #e4e7ea!important;",
                  "padding-left": "10px",
                },
              }}
            />
          </div>
          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleCloseModal}
            >
              Close
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={() => Save()}
            >
              Save
            </button>
          </div>
        </div>
      </div> */}
    </div>
  );
};
