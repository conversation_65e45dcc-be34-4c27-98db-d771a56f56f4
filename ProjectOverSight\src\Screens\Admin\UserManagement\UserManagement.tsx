import { TextField } from "@mui/material";
import { useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import DashboardIcon from "@mui/icons-material/Dashboard";
import PersonIcon from "@mui/icons-material/Person";
import AssignmentIcon from "@mui/icons-material/Assignment";
import Diversity3Icon from "@mui/icons-material/Diversity3";

export const UserManagement = () => {
  const [isSidebarClosed, setSidebarClosed] = useState(false);
  // const [isDarkMode, setDarkMode] = useState(false);

  const toggleSidebar = () => {
    setSidebarClosed((prev) => !prev);
  };

  const openSearchBox = () => {
    setSidebarClosed(false);
  };

  // const toggleDarkMode = () => {
  //   setDarkMode((prev) => !prev);
  // };

  return (
    <nav className={`sidebar ${isSidebarClosed ? "close" : ""}`}>
      <header>
        <div className="image-text">
          <span className="image">{<img src="logo.png" alt="" />}</span>
          <div className="text logo-text">
            <span className="name">User Managment</span>
            <span className="profession">Web developer</span>
          </div>
        </div>
        <i className="bx bx-chevron-right toggle" onClick={toggleSidebar}></i>
      </header>

      <div className="menu-bar">
        <div className="menu">
          <li className="search-box " onClick={openSearchBox}>
            <TextField
              placeholder="Search..."
              style={{ border: "1px solid grey" }}
              InputProps={{
                startAdornment: <SearchIcon style={{ color: "grey" }} />,
              }}
            />
          </li>

          <li className="nav-link">
            <a href="#">
              <DashboardIcon sx={{ color: "white" }} />
              <span className="text nav-text">Dashboard</span>
            </a>
          </li>
          <li className="nav-link">
            <a href="#">
              <PersonIcon sx={{ color: "white" }} />
              <span className="text nav-text">Users</span>
            </a>
          </li>
          <li className="nav-link">
            <a href="#">
              <AssignmentIcon sx={{ color: "white" }} />
              <span className="text nav-text">Projects</span>
            </a>
          </li>
          <li className="nav-link">
            <a href="#">
              <Diversity3Icon sx={{ color: "white" }} />
              <span className="text nav-text">Team</span>
            </a>
          </li>
        </div>
      </div>
    </nav>
  );
};

export default UserManagement;
