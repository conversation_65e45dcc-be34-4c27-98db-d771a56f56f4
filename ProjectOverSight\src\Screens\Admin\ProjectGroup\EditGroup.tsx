import { Divider } from "@mui/material";
import { Dispatch, SetStateAction } from "react";
import { Modal, Button, Form } from "react-bootstrap";
import { TProjectGroup } from "../../../Models/Project/ProjectGroup";
import { useForm } from "react-hook-form";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import Swal from "sweetalert2";
import { PostFiles } from "../../../Services/Axios";

type EditGroupProps = {
  show: boolean;
  setShow: Dispatch<SetStateAction<boolean>>;
  projectGroup: TProjectGroup | undefined;
  setReload: Dispatch<SetStateAction<boolean>>;
};

const formField = [
  "name",
  "description",
  "createdBy",
  "updatedBy",
  "file",
  "id",
];

export const EditGroup = ({
  show,
  setShow,
  projectGroup,
  setReload,
}: EditGroupProps) => {
  const { register, handleSubmit, resetField } = useForm();

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  async function save(data: any) {
    data.file = data.file[0];
    const { error }: any = await PostFiles(
      "app/Project/UpdateProjectGroup",
      data
    );
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Group Updated Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev) => !prev);
    });
    handleClose();
  }

  function handleClose() {
    setShow(false);
    reset();
  }

  return (
    <Modal show={show} onHide={() => handleClose()} centered>
      <Form onSubmit={handleSubmit(save)}>
        <Modal.Header closeButton>
          <Modal.Title>Edit</Modal.Title>
        </Modal.Header>
        <Divider />
        <Modal.Body>
          <Form.Group className="mb-3" controlId="exampleForm.ControlInput1">
            <Form.Label>Name*</Form.Label>
            <Form.Control
              type="text"
              placeholder="Name"
              required
              {...register("name")}
              defaultValue={projectGroup?.name}
            />
          </Form.Group>
          <Form.Group className="mb-3" controlId="exampleForm.ControlTextarea1">
            <Form.Label>Description*</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              placeholder="Description"
              required
              {...register("description")}
              defaultValue={projectGroup?.description}
            />
          </Form.Group>
          <Form.Group className="mb-3" controlId="exampleForm.ControlTextarea1">
            <Form.Label>Choose Image</Form.Label>
            <Form.Control type="file" accept="image/*" {...register("file")} />
            <Form.Control {...register("createdBy")} value="user" hidden />
            <Form.Control {...register("updatedBy")} value="user" hidden />
            <Form.Control {...register("id")} value={projectGroup?.id} hidden />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => handleClose()}>
            Close
          </Button>
          <Button variant="primary" type="submit">
            Save
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
