import { useLocation, Link } from "react-router-dom";
import Select from "react-select";
import { <PERSON><PERSON><PERSON>, Breadc<PERSON><PERSON>, Button, Box, Grid } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import { RectangleCard, SquareCard } from "../../../../CommonComponents/Card";
import { COMPLETED, PENDING } from "../../../../Constants/Common/Common";
import DataTable from "react-data-table-component";
import {
  FILTER,
  PROJECTREPORT,
  TASK_LIST,
} from "../../../../Constants/ProjectReport/ProjectReport";
import { useEffect, useRef, useState } from "react";
import {
  ProjectReportVM,
  ReportFilter,
} from "../../../../Models/Project/Project";
import { Get } from "../../../../Services/Axios";
import { UserInterface } from "../../../../Models/Project/UserInterface";
import { UserStory } from "../../../../Models/Project/UserStory";
import { Task } from "../../../../Models/Task/Task";
import BackDrop from "../../../../CommonComponents/BackDrop";
import { ConvertDate } from "../../../../Utilities/Utils";
import { useContextProvider } from "../../../../CommonComponents/Context";
import { Category } from "../../../../Models/Common/CommonMaster";
import "../../../../App.css";

const ProjectReportList = () => {
  const location = useLocation();
  const { startDate, endDate }: any = location.state;
  const [filter, setFilter] = useState<any>(FILTER);
  const [loading, setLoading] = useState<boolean>(false);
  const [projectReport, SetProjectReport] =
    useState<ProjectReportVM>(PROJECTREPORT);
  const [ProjectTask,setProjectTask] = useState<any>([]);
  const [TaskDetails,setTaskDetails] = useState<any>([]);
  const startDateRef = useRef<HTMLInputElement>(null);
  const endDateRef = useRef<HTMLInputElement>(null);
  const userStoryRef = useRef<any>();
  const userInterfaceRef = useRef<any>();
  const categoryRef = useRef<any>();
  const subCategoryRef = useRef<any>();
  const { category, role } = useContextProvider();
  const categorySet = new Set<string>();
  const statuiRef = useRef<any>();
  const [UserStorylist, SetUserStorylist] = useState<any>([]);
  const [UserInterfaceList,setUserInterfaceList] = useState<any>([]);

  category.forEach((category: Category) => {
    categorySet.add(category.categories);
  });

  async function GetData() {
    setLoading(true);
    const response: any = await Get(
      `app/Project/GetProjectReportDetails?projectId=${location.state.projectId}`
    );
    const projectDetails: any = await Get(
      `app/Task/getProjectTaskList?projectId=${location.state.projectId}`
    )

    const UserStoryList: any = await Get(
      `app/Project/GetUserStoryList?projectId=${location.state.projectId}`
    )
   
    const UserInterFaceList: any = await Get(
      `app/Project/GetUserInterfacelist?projectId=${location.state.projectId}`
    )

    const EmpTask: any = await Get(
      `app/EmployeeTask/GetEmpTask`
    )
    if(role == "Customer"){
      setProjectTask(EmpTask?.data || []);
    setTaskDetails(EmpTask?.data || []);
    }else{
      setProjectTask(projectDetails?.data?.projectTaskLists || []);
      setTaskDetails(projectDetails?.data?.projectTaskLists || []);
    }
    
    SetProjectReport(response?.data ?? PROJECTREPORT);
    SetUserStorylist(UserStoryList?.data || []);
    setUserInterfaceList(UserInterFaceList?.data || []);
    setLoading(false);
  }

  useEffect(() => {
    GetData();
  }, []);




  function ApplyFilter() {
    debugger
    var temp: Array<any> = TaskDetails;

    if (filter?.userStory !== null) {
      temp = temp.filter(
        (task: any) =>
          task?.userStoryName
            ?.toLowerCase()
            .search(`${filter.userStory?.toLowerCase()}`) >= 0
      );
      setProjectTask(temp);
    }

    if (filter.userInterface !== null) {
      temp = temp.filter(
        (task: any) =>
          task?.userInterfaceName
            ?.toLowerCase()
            .search(`${filter.userInterface?.toLowerCase()}`) >= 0
      );
      setProjectTask(temp);
    }

    if (filter.category !== null) {
      temp = temp.filter(
        (task: any) =>
          task?.categories
            ?.toLowerCase()
            .search(`${filter.category?.toLowerCase()}`) >= 0
      );
      setProjectTask(temp);
    }

    if (filter.subCategory !== null) {
      temp = temp.filter(
        (task: any) =>
          task?.subCategories
            ?.toLowerCase()
            .search(`${filter.subCategory?.toLowerCase()}`) >= 0
      );
      setProjectTask(temp);
    }

    if (filter.TeamName != null) {
      temp = temp.filter((e: any) => {
        return e.teamName === filter.TeamName;
      });
      setProjectTask(temp);
    }

    if (filter.status != null) {
      temp = temp.filter((e: any) => {
        return e.status.toLowerCase() === filter.status?.toLowerCase();
      });
      setProjectTask(temp);
    }

  }

  function reset() {
    if (startDateRef.current) startDateRef.current.value = "";
    if (endDateRef.current) endDateRef.current.value = "";
    if (categoryRef.current) categoryRef.current.clearValue();
    if (statuiRef.current) statuiRef.current.clearValue();
    if (subCategoryRef.current) subCategoryRef.current.clearValue();
    if (userInterfaceRef.current) userInterfaceRef.current.clearValue();
    if (userStoryRef.current) userStoryRef.current.clearValue();
    setFilter(FILTER);
    setProjectTask(TaskDetails);
  }

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Link color="inherit" to={`/${role}/Project`}>
          <Typography sx={{ fontWeight: "bold" }}>Project</Typography>
        </Link>
        <Link
          color="inherit"
          to={`/${role}/ProjectQuadrant`}
          state={{ ...location.state }}
        >
          <Typography sx={{ fontWeight: "bold" }}>Project Quadrant</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Project Report</Typography>
      </Breadcrumbs>
      <div className="d-flex justify-content-between mx-5  mt-3">
        <span>
          <h5>
            Project Name: <b>{location.state.projectName}</b>
          </h5>
        </span>
        <div>
          <h5>
            Project Start Date: <b>{ConvertDate(startDate)}</b>
          </h5>
          <h5>
            Project End Date: <b>{ConvertDate(endDate)}</b>
          </h5>
        </div>
      </div>
      
      <div
        className="border border-4 mx-5 d-flex p-2 g-3 justify-content-evenly"
        style={{ backgroundColor: "#e1f4f5" }}
      >
        <div>
          <Grid sx={{marginTop: { xs: '11rem',sm:'11rem', md: 'auto' }}}>
          <div style={{ height: "6rem" }} />
          <RectangleCard text={COMPLETED} />
          <RectangleCard text={PENDING} />
          </Grid>
        </div>
          
        <div>
          <Grid container>
            <Grid >
            <Link
              to={`/${role}/UserStory`}
              state={{
                ...location.state,
                projectReportRoute: true,
                status: "",
              }}
            >
              <SquareCard
                text={"User Stories"}
                count={projectReport.userStory.length}
                classNames="squareCard hoverEffect"
              />
            </Link>
            <div>
                <Link
                  to={`/${role}/UserStory`}
                  state={{
                    ...location.state,
                    projectReportRoute: true,
                    status: COMPLETED,
                  }}
                >
                  <RectangleCard
                    count={
                      projectReport.userStory.filter(
                        (x: UserStory) => x.percentage === 100
                      ).length
                    }
                    classNames="rectangleCard"
                  />
                </Link>
                <Link
                  to={`/${role}/UserStory`}
                  state={{
                    ...location.state,
                    projectReportRoute: true,
                    status: PENDING,
                  }}
                >
                  <RectangleCard
                    classNames="rectangleCard"
                    count={
                      projectReport.userStory.filter(
                        (x: UserStory) => parseInt(`${x.percentage}`) < 100
                      ).length
                    }
                  />
                </Link>
              </div>
            </Grid>
            <Grid >
            <Link
              to={`/${role}/UserInterface`}
              state={{
                ...location.state,
                projectReportRoute: true,
                status: "",
              }}
            >
              <SquareCard
                text={"User Interface"}
                count={projectReport.userInterface.length}
                classNames="squareCard hoverEffect"
              />
            </Link>
            <div>
                <Link
                  to={`/${role}/UserInterface`}
                  state={{
                    ...location.state,
                    projectReportRoute: true,
                    status: COMPLETED,
                  }}
                >
                  <RectangleCard
                    classNames="rectangleCard"
                    count={
                      projectReport.userInterface.filter(
                        (x: UserInterface) => x.percentage === 100
                      ).length
                    }
                  />
                </Link>
                <Link
                  to={`/${role}/UserInterface`}
                  state={{
                    ...location.state,
                    projectReportRoute: true,
                    status: PENDING,
                  }}
                >
                  <RectangleCard
                    classNames="rectangleCard"
                    count={
                      projectReport.userInterface.filter(
                        (x: UserInterface) => parseInt(`${x.percentage}`) < 100
                      ).length
                    }
                  />
                </Link>
              </div>
            </Grid>
            <Grid>
              <SquareCard
                text={"Total No.of Tasks"}
                count={TaskDetails?.length || 0}
                classNames="mt-2"
              />
               <div>
                  <RectangleCard
                    count={
                      TaskDetails?.filter(
                        (x: Task) => x.percentage === 100
                      ).length || 0
                    }
                  />
                  <RectangleCard
                    count={
                      TaskDetails?.filter((x: Task) => x.percentage < 100)
                        .length || 0
                    }
                  />
              </div>
               </Grid>
               <Grid >
            <SquareCard
              text={`Project Completion %`}
              classNames="squareCard"
              count={parseFloat(
                `${projectReport.completedPercentage ?? 0}`
              ).toFixed(2)}
            /> 
             <div>
                <RectangleCard
                  text={`${parseFloat(
                    `${projectReport.completedPercentage ?? 0} `
                  ).toFixed(2)}%`}
                />
                <RectangleCard
                  text={`${parseFloat(
                    `${projectReport?.pendingPercentage ?? 0}`
                  ).toFixed(2)}%`}
                />
              </div>
            </Grid>
            <Grid >
            <SquareCard
              text={"Total No.of Resources"}
              count={projectReport.totalResource}
              classNames="squareCard"
            />
             </Grid>
            <Grid >
            <SquareCard
              text={"Total No.of Resources Hours"}
              count={projectReport.totalResourceHours}
              classNames="squareCard"
            />
            </Grid>
          </Grid>
        </div>
      </div>
      <div className="well mx-auto mt-4">
        <div className="row">
          {/* <div className="col-sm-2">
            <div className="form-group">
              <label className="mx-1">Start Date</label>
              <input
                onChange={(e: any) => {
                  setFilter((prevState: ReportFilter) => {
                    return {
                      ...prevState,
                      startDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="end-date"
                placeholder="End Date"
                ref={startDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label className="mx-1">End Date</label>
              <input
                onChange={(e: any) => {
                  setFilter((prevState: ReportFilter) => {
                    return {
                      ...prevState,
                      endDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="end-date"
                placeholder="End Date"
                ref={endDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div> */}
          <div className="col-sm-2">
            <div className="form-group">
            <label>Status</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="status"
                ref={statuiRef}
                className="select-dropdowns mt-1 col"
                onChange={(selectedOption: any) => {
                  setFilter((prevState:any) => ({
                    ...prevState,
                    status: selectedOption ? selectedOption.value : null,
                  }));
                }}
                options={[
                  {
                    label: "Completed",
                    value: "Completed",
                  },
                  {
                    label: "In Progress",
                    value: "In Progress",
                  },
                  {
                    label: "Pending",
                    value: "Pending",
                  },
                  {
                    label: "Ready-For-UAT",
                    value: "Ready-For-UAT",
                  },
                ]}
                placeholder="Status"
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>User Story</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="UserStory"
                ref={userStoryRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setFilter((prevState: ReportFilter) => {
                    return {
                      ...prevState,
                      userStory: code ? code.value : null,
                    };
                  });
                }}
                options={UserStorylist?.map((opt: any) => ({
                  label: opt?.name,
                  value: opt?.name,
                }))}
                placeholder="User Story"
                isSearchable={true}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    zIndex: 999, 
                  }),
                  menu: (provided) => ({
                    ...provided,
                    maxHeight: 300, 
                    zIndex: 999, // Set your desired max height here
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>User Interface</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="UserInterface"
                ref={userInterfaceRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setFilter((prevState: ReportFilter) => {
                    return {
                      ...prevState,
                      userInterface: code ? code.value : null,
                    };
                  });
                }}
                options={UserInterfaceList?.map((opt: any) => ({
                  label: opt?.name,
                  value: opt?.name,
                }))}
                placeholder="User Interface"
                isSearchable={true}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    zIndex: 999, 
                  }),
                  menu: (provided) => ({
                    ...provided,
                    maxHeight: 300, 
                    zIndex: 999, // Set your desired max height here
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Category</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="Category"
                ref={categoryRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setFilter((prevState: ReportFilter) => {
                    return {
                      ...prevState,
                      category: code ? code.value : null,
                    };
                  });
                }}
                options={[...categorySet].map((category: string) => ({
                  label: category,
                  value: category,
                }))}
                placeholder="Category"
                isSearchable={true}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    zIndex: 999, 
                  }),
                  menu: (provided) => ({
                    ...provided,
                    maxHeight: 300, 
                    zIndex: 999, 
                  }),
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Sub Category</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="SubCategory"
                ref={subCategoryRef}
                className="select-dropdowns mt-1 col"
                onChange={(code: any) => {
                  setFilter((prevState: ReportFilter) => {
                    return {
                      ...prevState,
                      subCategory: code ? code.value : null,
                    };
                  });
                }}
                options={category
                  .filter((x) => x.categories === filter.category)
                  .map((category: Category) => ({
                    label: category.subCategory,
                    value: category.subCategory,
                  }))}
                placeholder="Sub Category"
                isSearchable={true}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    zIndex: 999, 
                  }),
                  menu: (provided) => ({
                    ...provided,
                    maxHeight: 300, 
                    zIndex: 999, 
                  }),
                }}
              />
            </div>
          </div>
          <div className="container">
            <div className="row justify-content-end">
              <div className="col-auto">
                <Button
                  variant="contained"
                  endIcon={<SearchIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => ApplyFilter()}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  endIcon={<RefreshIcon />}
                  className="mx-3 mt-3 "
                  onClick={() => reset()}
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    
      <div className="d-flex flex-column justify-content-center align-items-center mb-5">
        <Grid item xs={12} sm={11}>
          <div className="col-3 col-s-3">
          <div
        className="mx-5 mt-5 p-3 text-light d-flex align-items-center"
      >
        <h5 className="m-1 fw-bold" style={{color:"black"}}>List Of Tasks</h5>
      </div>
            <Box style={{ width: "93.5vw", position: "relative",height:"30vw"}}>
              <DataTable
                customStyles={{
                  table: {
                    style: {
                      height: "65vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },   

                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                      opacity:0.6
                    },
                  },
                }}
                fixedHeader={true}
                pagination
                responsive
                paginationPerPage={50}
                paginationRowsPerPageOptions={[50, 100, 200]}
                pointerOnHover={true}
                columns={TASK_LIST}
                data={ProjectTask}
                progressPending={loading}
              />
            </Box>
          </div>
        </Grid>
      </div>
      <BackDrop open={loading} />
    </>
  );
};

export default ProjectReportList;
