import {
  Dialog,
  DialogTitle,
  <PERSON><PERSON>ield,
  Button,
  DialogContent,
  InputLabel,
  DialogActions,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
export const ViewTeam = ({ openDialog, setOpenDialog, Data }: any) => {
  const handleClose = () => {
    setOpenDialog({ view: false });
  };

  return (
    <div>
      <Dialog open={openDialog?.view} onClose={handleClose}>
        <form>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>Team</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
            <div className="row">
              <TextField
                value={Data?.name}
                className="col m-2 read-only-input"
                label="Name"
                type="text"
                disabled={Data?.name? false : true}
                variant="outlined"
              />
              <TextField
                value={Data?.isActive ? "Active" : "In Active"}
                className="col m-2 read-only-input"
                label="Status"
                type="text"
                disabled={Data?.isActive? false : true}
                variant="outlined"
              />
            </div>
            <div className="row">
              <div className="col">
                <InputLabel id="Team-Name">Start Date</InputLabel>
                <TextField
                  required
                  fullWidth
                  className="read-only-input"
                  defaultValue={Data?.startDate?.slice(0, 10)}
                  type="date"
                  disabled={Data?.startDate? false : true}
                  variant="outlined"
                />
              </div>
              <div className="col">
                <InputLabel id="Team-Name">End Date</InputLabel>
                <TextField
                  required
                  fullWidth
                  defaultValue={Data?.endDate?.slice(0, 10)}
                  type="date"
                  className="read-only-input"
                  disabled={Data?.startDate? false : true}
                  variant="outlined"
                />
              </div>
            </div>

            {/* <div className="row col-md-8">
              <InputLabel id="Team-Name">Start Date</InputLabel>
              <TextField
                value={Data?.startDate?.slice(0, 10)}
                className="col m-2"
                type="date"
                disabled
                variant="outlined"
              />
            </div>
            <div className="row col-md-8">
              <InputLabel id="Team-Name">End Date</InputLabel>
              <TextField
                className="col m-2"
                value={Data?.endDate?.slice(0, 10)}
                type="date"
                disabled
                variant="outlined"
              />
            </div> */}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="contained">
              ok
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
