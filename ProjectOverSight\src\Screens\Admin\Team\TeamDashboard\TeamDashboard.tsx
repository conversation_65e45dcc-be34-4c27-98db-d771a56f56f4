import { <PERSON><PERSON><PERSON>rum<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>on, Typography } from "@mui/material";
import { <PERSON> } from "react-router-dom";
import { useContextProvider } from "../../../../CommonComponents/Context";
import { useState, useEffect } from "react";
import "../../../../StyleSheets/TeamDashboard.css";
import { Get } from "../../../../Services/Axios";
import { Regex } from "../../../../Constants/Regex/Regex";
import { ConvertDate } from "../../../../Utilities/Utils";

const colors = [
  "#de839b",
  "#a5a7e6",
  "#a5e6b1",
  "#f29891",
  "#edf0a8",
  "#f5abf1",
];

export const TeamDashboard = () => {
  const { role } = useContextProvider();
  const [dashboardData, setDashboardData] = useState<any>([]);

  async function getData() {
    var dashboardData: any = await Get(
      "app/Team/GetTeamDashboardData?teamId=2"
    );
    setDashboardData(dashboardData?.data);
  }

  useEffect(() => {
    setTimeout(() => {
      getData();
    }, 2000);
  }, []);

  function getRandom() {
    return Math.floor(Math.random() * 5) + 1;
  }
  console.log("Render");
  return (
    <div>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Team Dashboard</Typography>
      </Breadcrumbs>
      <div className="row mx-auto justify-content-center">
        <div className="col-md-3 mx-2 border-1 card mt-4 ">
          <Typography className="fs-4 mt-3 text-center">
            Team Members
          </Typography>
          <hr className="w-75 mx-auto" />
          <div className="member-list">
            {dashboardData?.employees?.map((e: any) => (
              <ListItemButton
                className="d-flex align-items-center justify-content-between rounded border border-1 m-2"
                key={e.id}
              >
                <div className="d-flex align-items-center">
                  <span
                    className="fs-5 logo p-2 text-center"
                    style={{ background: `${colors[getRandom()]}` }}
                  >
                    {e.name.charAt(0)}
                  </span>
                  <p className="fs-6 mx-2 mt-3">
                    {e.name.replace(Regex.CHAR_SPACE, " ")}
                  </p>
                </div>
                <div className="time-box">
                  <p className="time1 m-1 float-end border border-1 p-1">
                    In Time: 10:01
                  </p>
                  <p className="time1 m-1 float-end border border-1 p-1">
                    Out Time: 07:01
                  </p>
                </div>
              </ListItemButton>
            ))}
          </div>
        </div>
        <div className="col-md-5 mx-2 border-1  card mt-4">
          <Typography className="fs-4 mt-3 text-center">Projects</Typography>
          <hr className="w-75 mx-auto" />
          <div className="member-list">
            <Table data={dashboardData?.projects} />
          </div>
        </div>
        <div className="col-md-3 mx-2 border-1 card mt-4 ">
          <Typography className="fs-4 mt-3 text-center">Objectives</Typography>
          <hr className="w-75 mx-auto" />
          <div className="fs-6 member-list">
            {dashboardData?.weeklyObjectives?.map((e: any) => (
              <>
                <small>{e.name}</small>
                <hr />
              </>
            ))}
          </div>
        </div>
        <div className="col-md-6 mx-3 border-1 card mt-4">
          <Typography className="fs-4 mt-3 text-center">
            Today's Tasks
          </Typography>
          <hr className="w-75 mx-auto" />
          <div className="member-list">
            <TaskTable data={dashboardData?.todaysTask} />
          </div>
        </div>
        <div className="col-md-5 mx-3 border-1 card mt-4">
          <Typography className="fs-4 mt-3 text-center">
            High Priority Tasks
          </Typography>
          <hr className="w-75 mx-auto" />
          <div className="member-list">
            <TaskTable data={dashboardData?.priorityTask} />
          </div>
        </div>
      </div>
    </div>
  );
};

const TaskTable = ({ data }: any) => {
  const tableStyle: any = {
    width: "50%",
  };

  const thStyle: any = {
    borderBottom: "2px solid #cfd1d0",
    padding: "8px",
    textAlign: "left",
    fontSize: "12px",
    color: "gray",
  };

  const tdStyle: any = {
    textAlign: "left",
    borderBottom: "2px solid #cfd1d0",
    padding: "8px",
    fontWeight: "bold",
    fontSize: "12px",
  };

  return (
    <table style={tableStyle} className="w-100 m-1">
      <thead>
        <tr>
          <th style={thStyle}>TASK NAME</th>
          <th style={thStyle}>DESCRIPTION</th>
          <th style={thStyle}>STATUS</th>
          <th style={thStyle}>PERCENTAGE</th>
        </tr>
      </thead>

      <tbody>
        {data?.map((task: any) => (
          <tr>
            <td style={tdStyle}>{task.name}</td>
            <td style={tdStyle}>{task.taskDescription}</td>
            <td style={tdStyle}>{task.status}</td>
            <td style={tdStyle}>{task.percentage || 0}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

const Table = ({ data }: any) => {
  const tableStyle: any = {
    borderCollapse: "collapse",
    width: "50%",
  };

  const thStyle: any = {
    borderBottom: "2px solid #cfd1d0",
    padding: "8px",
    textAlign: "left",
    position: "sticky",
    fontSize: "12px",
    color: "gray",
  };

  const tdStyle: any = {
    textAlign: "left",
    padding: "8px",
    fontWeight: "bold",
    fontSize: "12px",
  };

  return (
    <table style={tableStyle} className="w-100 m-1">
      <thead>
        <tr>
          <th style={thStyle}>PROJECT NAME</th>
          <th style={thStyle}>START DATE</th>
          <th style={thStyle}>END DATE</th>
          <th style={thStyle}>STATUS</th>
          <th style={thStyle}>PERCENTAGE</th>
        </tr>
      </thead>

      <tbody>
        {data?.map((project: any) => (
          <tr>
            <td style={tdStyle}>{project.name}</td>
            <td style={tdStyle}>{ConvertDate(project.startDate)}</td>
            <td style={tdStyle}>{ConvertDate(project.endDate)}</td>
            <td style={tdStyle}>{project.status}</td>
            <td style={tdStyle}>{project.percentage || 0}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};
