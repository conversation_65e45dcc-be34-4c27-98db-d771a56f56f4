{"name": "projectoversignt", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@coreui/react": "^4.9.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/icons-material": "^5.14.8", "@mui/joy": "^5.0.0-alpha.89", "@mui/lab": "^5.0.0-alpha.134", "@mui/material": "^5.15.11", "@mui/styles": "^5.15.18", "@mui/x-data-grid": "^6.6.0", "@mui/x-data-grid-pro": "^6.10.0", "@mui/x-date-pickers": "^6.19.9", "@mui/x-license-pro": "^6.10.0", "@mui/x-tree-view": "^6.0.0-alpha.1", "@react-icons/all-files": "^4.1.0", "apexcharts": "^3.41.1", "axios": "^1.6.4", "chart.js": "^4.3.0", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "file-saver": "^2.0.5", "force": "^0.0.3", "lucide-react": "^0.292.0", "material-react-table": "^1.14.0", "multiselect-react-dropdown": "^2.0.25", "npm": "^10.2.4", "primereact": "^10.0.2", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-bootstrap": "^2.9.1", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-data-table-component": "^7.5.4", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "react-google-charts": "^4.0.1", "react-hook-form": "^7.44.3", "react-icons": "^4.10.1", "react-loading-skeleton": "^3.3.1", "react-query": "^3.39.3", "react-router-dom": "^6.22.3", "react-select": "^5.7.3", "react-semicircle-progressbar": "^1.1.0", "react-type-animation": "^3.2.0", "react-virtuoso": "^4.6.2", "reactflow": "^11.7.4", "styled-components": "^6.0.5", "sweetalert2": "^11.7.12", "xlsx": "^0.18.5"}, "devDependencies": {"@types/file-saver": "^2.0.5", "@types/react": "^18.0.37", "@types/react-datepicker": "^4.19.3", "@types/react-dom": "^18.0.11", "@types/rebass": "^4.0.14", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "typescript": "^5.0.2", "vite": "^4.3.9"}}