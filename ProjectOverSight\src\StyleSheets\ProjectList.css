.project-list{
position: absolute;
top: 40%;
left:50%;
transform: translate(-50%, -50%);
width: 60vw;
height: 40vh;
background-color: white;
border: none;
box-shadow: 24;
margin: 8px;
padding: 4px;
border-radius:8px ;
}

#modal-modal-title{
    font-weight: 700;
}


.box:hover {
    transform: translateY(-10px);
  }
  .box {   
    cursor: context-menu;
    transition: all .5s ease-in-out;
  }
  .redTopBorder {
    border-top: 3px solid  #2196f3;
  border-radius: 3px;  
  }
  .text-uppercase {
    text-transform: uppercase!important;
}
.badge {
  font-size: 11px;
  padding: 5px;
}

.badge.badge-primary {
  background-color: #03A9F4;
}
.badge-primary {
  background: #03A9F4;
}
.badge {
  vertical-align: middle;
  padding: 7px 12px;
  font-weight: 600;
  letter-spacing: .3px;
  border-radius: 30px;
  font-size: 12px;
}
.float-right {
  float: right!important;
}
.small, small {
  font-size: 80%;
  font-weight: 400;
}
.underline-on-hover:hover {
  text-decoration: underline;
  cursor: pointer;
}
.assignee__avatar, .assignee_avatar_image {
  border-radius: 50em;
  height: 35px;
  padding: 2px;
}

.project_remaining_user, .tasks_remaining_user {
  background-color: #bbb;
  display: inline-block;
  vertical-align: middle;
  border-radius: 50em;
  font-size: 12px;
  height: 32px;
  padding-top: 6px;
  text-align: center;
  width: 32px;
}

.modal-header {
  border-bottom: none;
  padding-bottom: 5px;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
}

.btn-primary, .btn-primary.disabled {
  box-shadow: 0 2px 6px #acb5f6;
  background-color: #6777ef;
  border-color: #6777ef;
}

.option.highlightOption.highlight {
  background-color: transparent !important;
  color: inherit !important;
}
.option.highlightOption.highlight:hover {
  background-color: #6777ef !important; 
  color: white !important; 
}
.multiSelectContainer li:hover {
  background: #6777ef !important;
  color: #fff;
  cursor: pointer;
}

/* ul.optionContainer  {
  background-color: #6777ef !important;
} */

