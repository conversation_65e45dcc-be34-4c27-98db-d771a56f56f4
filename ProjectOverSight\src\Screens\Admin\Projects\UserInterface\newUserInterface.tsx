import { <PERSON>lt<PERSON>, Button, Stack } from "@mui/material";
import { useLocation } from "react-router-dom";
import { useEffect, useState, useRef } from "react";
import { AddUserInterface } from "./AddUserInterface";
import { EditUserInterface } from "./EditUserInterface";
import { ViewUserInterface } from "./VeiwUserInterface";
import { UserInterface } from "../../../../Models/Project/UserInterface";
import { ConvertDate } from "../../../../Utilities/Utils";
import { Get, Post } from "../../../../Services/Axios";
import EditIcon from "@mui/icons-material/Edit";
import StackedBarChartIcon from "@mui/icons-material/StackedBarChart";
import ChecklistIcon from "@mui/icons-material/Checklist";
import RefreshIcon from "@mui/icons-material/Refresh";
import BackDrop from "../../../../CommonComponents/BackDrop";
import { useContextProvider } from "../../../../CommonComponents/Context";
import { ADMIN } from "../../../../Constants/Common/Roles";
import { COMPLETED, PENDING } from "../../../../Constants/Common/Common";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import DeleteIcon from "@mui/icons-material/Delete";
import Swal from "sweetalert2";
import Card from "@mui/material/Card";
import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import RuleIcon from "@mui/icons-material/Rule";
import PendingActionsIcon from "@mui/icons-material/PendingActions";
import AddIcon from "@mui/icons-material/Add";
import { DownloadUserStoryList } from "../../../../Services/ProjectService";
import DownloadIcon from "@mui/icons-material/Download";
import { styled, alpha } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import { SearchIcon } from "lucide-react";
import Select from "react-select";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import Pagination from "@mui/material/Pagination";

const Search = styled("div")(({ theme }) => ({
  position: "relative",
  borderRadius: theme.shape.borderRadius,
  backgroundColor: alpha(theme.palette.common.white, 0.15),
  "&:hover": {
    backgroundColor: alpha(theme.palette.common.white, 0.25),
  },
  marginRight: theme.spacing(2),
  marginLeft: 0,
  width: "100%",
  [theme.breakpoints.up("sm")]: {
    marginLeft: theme.spacing(3),
    width: "auto",
  },
}));

const SearchIconWrapper = styled("div")(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: "100%",
  position: "absolute",
  pointerEvents: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: "inherit",
  "& .MuiInputBase-input": {
    padding: theme.spacing(1, 1, 1, 0),
    // vertical padding + font size from searchIcon
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create("width"),
    width: "100%",
    [theme.breakpoints.up("md")]: {
      width: "20ch",
    },
  },
}));

export const NewUserInterfacelist = ( { additionalData }: { additionalData: any } ) => {

  const [filterRows, setfilterRows] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [reload, SetReload] = useState<boolean>(true);
  // const [open, setOpen] = useState(false);
  // const handleOpen = () => setOpen(true);
  const [rows, setRows] = useState<any>([]);
  const location = useLocation();
  const [filter, setfilter] = useState<any>({});
  const { projectReportRoute, status } = location.state;
  const [userInterfacedata, setUserInterfacedata] = useState<any>();
  const [data, setData] = useState([]);
  const { role } = useContextProvider();
  const [UserInterfaceView, setUserInterfaceView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });
  // const [projects, setProjects] = useState([]);
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const statusRef = useRef<any>();
  const [searchTerm, setSearchTerm] = useState("");
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [page, setPage] = useState(1);
  const [USname,getUSname] = useState<any>();

  const handleChangePage = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
    return event;
  };

  const options = [
    { value: 6, label: "6" },
    { value: 10, label: "10" },
    { value: 20, label: "20" },
  ];

  const handleChangeRowsPerPage = (
    selectedOption: { value: number; label: string } | null
  ) => {
    if (selectedOption) {
      setRowsPerPage(selectedOption.value);
      setPage(1);
    }
  };

  const handlePendingClick = (statusfilter: string) => {
    const filteredRow = rows.filter(
      (e: any) => e.status.toLowerCase() === statusfilter.toLowerCase()
    );
    setfilterRows(filteredRow);
    setPage(1);
  };

  async function fetchData() {
    setLoading(true);

    const getUserStoryId = Object.keys(additionalData)
    .filter((key) => additionalData[key]?.id) 
    .map((key) => additionalData[key]);

    console.log(getUserStoryId[0]?.userStoryUIs[0]?.uiId);
    
    

    if (getUserStoryId[0]?.userStoryUIs?.length > 0) {
     
      
      let filterUserInterface: any = await Get(
        `app/Project/GetUserStoryUIList?userStoryId=${getUserStoryId[0]?.id}`
      );

      setfilterRows(filterUserInterface?.data);
      setRows(filterUserInterface?.data);
      getUSname(getUserStoryId[0]?.name)
     
    }else if(getUserStoryId[0]?.userStoryUIs?.length == 0){
      let filterUserInterface: any = await Get(
        `app/Project/GetUserStoryUIList?userStoryId=${getUserStoryId[0]?.id}`
      );

      setfilterRows(filterUserInterface?.data);
      setRows(filterUserInterface?.data);
      getUSname(getUserStoryId[0]?.name)

    }else{

    let UserInterface: any = await Get(
      `app/Project/GetUserInterfaceList?projectId=${location?.state?.projectId}`
    );
      setfilterRows(UserInterface?.data);
    setRows(UserInterface?.data);

    if (projectReportRoute) {
      if (status === COMPLETED) {
        setfilterRows(
          UserInterface?.data?.filter(
            (x: UserInterface) => x.percentage === 100
          ) || []
        );
      } else if (status === PENDING) {
        setfilterRows(
          UserInterface?.data?.filter(
            (x: UserInterface) => parseInt(`${x.percentage}`) < 100
          ) || []
        );
      } else {
        setfilterRows(UserInterface?.data || []);
      }
    } else {
      setfilterRows(UserInterface?.data || []);
    }
    getUSname('')
  }


    var objective: any = await Get(
      `app/Project/GetProjectObjective?ProjectId=${location?.state?.projectId}`
    );
    setData(objective?.data?.projectObjectives || []);
    setLoading(false);
  }

  async function resetData(){
    setLoading(true);

    let UserInterface: any = await Get(
      `app/Project/GetUserInterfaceList?projectId=${location?.state?.projectId}`
    );
      setfilterRows(UserInterface?.data);
      setRows(UserInterface?.data);
    

    if (projectReportRoute) {
      if (status === COMPLETED) {
        setfilterRows(
          UserInterface?.data?.filter(
            (x: UserInterface) => x.percentage === 100
          ) || []
        );
      } else if (status === PENDING) {
        setfilterRows(
          UserInterface?.data?.filter(
            (x: UserInterface) => parseInt(`${x.percentage}`) < 100
          ) || []
        );
      } else {
        setfilterRows(UserInterface?.data || []);
      }
    } else {
      setfilterRows(UserInterface?.data);
    }
    getUSname('')
    setLoading(false);

  }

  useEffect(() => {
    fetchData();
  }, [reload,additionalData]);

  function ApplyFilter() {
    let temp: any = [];
    if (filter.actStartDate != null) {
      if (filter.actEndDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].startDate.slice(0, 10) >= filter.actStartDate &&
          rows[i].endDate.slice(0, 10) <= filter.actEndDate
        ) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (searchTerm.trim() !== "") {
      temp = temp.filter((e: any) =>
        e?.name?.toLowerCase()?.includes(searchTerm.toLowerCase())
      );
      setfilterRows(temp);
      setPage(1);
    }

    if (filter.Status != null) {
      temp = temp.filter((e: any) => {
        return e.status.toLowerCase() === filter.Status?.toLowerCase();
      });
      setfilterRows(temp);
      setPage(1);
    }
  }
  function reset() {
    setfilter({});
    if (statusRef.current) statusRef.current.clearValue();
    setSearchTerm("");
    setfilterRows(rows);
    resetData();
  }

  const handleClickOpen = () => {
    setUserInterfaceView({ add: true });
  };

  async function deleteUserInterface(userInterface: UserInterface) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const { error }: any = await Post(
          "app/Project/UpdateUserInterface",
          userInterface
        );
        var option: AlertOption;
        if (error) {
          option = {
            title: "Error",
            text: "Error Occured While Deleting!",
            icon: "error",
          };
        } else {
          option = {
            title: "Success",
            text: "User Interface has been deleted!",
            icon: "success",
          };
        }
        Swal.fire({
          ...option,
          confirmButtonColor: "#3085d6",
        });
        SetReload((prev: boolean) => !prev);
      }
    });
  }

  return (
    <div>
      <div className="row mt-3">
        <div className="col-md-9">
          <div className="row">
            <div className="col-md-2 ">
              {role === ADMIN && (
                <button
                  // variant="contained"
                  type="button"
                  className="btn btn-primary mb-2 float-md-start"
                  onClick={handleClickOpen}
                >
                  Add
                  <AddIcon className="mx-1" />
                </button>
              )}
            </div>
            <div className="col-md-2">
              <div className="form-group" style={{ border: '2px solid #bfbaba', borderRadius: '5px' }}>
                <Select
                  aria-label="Floating label select example"
                  isClearable={true}
                  name="status"
                  ref={statusRef}
                  className="select-dropdowns col"
                  onInputChange={(inputValue: string) => {
                    const alphabeticValue = inputValue.replace(
                      /[^A-Za-z\s]/g,
                      ""
                    );
                    return alphabeticValue;
                  }}
                  onChange={(selectedOption: any) => {
                    if (selectedOption) {
                      setfilter((prevState: any) => ({
                        ...prevState,
                        Status:
                          selectedOption.label.trim() === ""
                            ? null
                            : selectedOption.label,
                      }));
                    }
                  }}
                  options={[
                    {
                      label: "Completed",
                      value: "Completed",
                    },
                    {
                      label: "In Progress",
                      value: "In Progress",
                    },
                    {
                      label: "Pending",
                      value: "Pending",
                    },
                  ]}
                  placeholder="Status"
                  isSearchable={true}
                  formatOptionLabel={(option: any) => option.label}
                />
              </div>
            </div>
            <div className="col-md-4 mb-2">
              <Search>
                <SearchIconWrapper>
                  <SearchIcon />
                </SearchIconWrapper>
                <StyledInputBase
                  inputProps={{ "aria-label": "search" }}
                  placeholder="Search User Story name"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  sx={{
                    border: "2px solid #bfbaba",
                    borderRadius: "10px",
                    width: "108%",
                  }}
                />
              </Search>
            </div>
            <div className="col-2">
              <div className="mt-1 row">
                <div className="col-6">
                  <Button
                    variant="contained"
                    size="small"
                    onClick={ApplyFilter}
                  >
                    <Tooltip title={"Search"}>
                      <SearchIcon />
                    </Tooltip>
                  </Button>
                </div>
                <div className="col-6">
                  <Button
                    variant="contained"
                    size="small"
                    onClick={() => reset()}
                  >
                    <Tooltip title={"Reset"}>
                      <RefreshIcon />
                    </Tooltip>
                  </Button>
                </div>
              </div>
            </div>
            <div className="col-2">
              <button
                className="btn btn-primary mb-2 float-md-end"
                onClick={() => {
                  DownloadUserStoryList(filterRows);
                }}
              >
                Download
                <DownloadIcon className="mx-1" />
              </button>
            </div>
          </div>
          <div className="container mt-2">
            <div
              className="mx-3"
              style={{
                display: "flex",
                justifyContent: "end",
                alignItems: "center",
              }}
            >
              <div
                style={{
                  paddingTop: "5px",
                  marginRight: "15px",
                  width: "60%",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <div>
                  <p className="text-muted mt-2 ">
                    Total User Interface : {filterRows?.length? filterRows?.length : 0}
                  </p>
                </div>
                <div style={{ marginLeft: "35%" }}>
                  <p className="text-muted mt-2 ">Rows per page :</p>
                </div>
                <Select
                  value={{ value: rowsPerPage, label: rowsPerPage.toString() }}
                  options={options}
                  onChange={handleChangeRowsPerPage}
                  aria-label="Rows per page"
                />
              </div>
              <Stack spacing={2} style={{ width: "40%" }}>
                <Pagination
                  count={Math.ceil(filterRows?.length / rowsPerPage)}
                  page={page}
                  onChange={handleChangePage}
                  color="primary"
                />
              </Stack>
            </div>
            <div
                            style={{
                              fontSize: "18px",
                              lineHeight: "28px",
                              color: "#9c4b22",
                              paddingRight: "10px",
                              fontWeight: "900",
                              width: "100%",
                            }}
                          >
                            <p style={{ textAlign: "center",display:USname? "block" : "none"}}>
                             User Story Name - <span style={{color:'blueviolet'}}>{USname}</span>
                            </p>
                          </div>
          </div>
          <div className="row mt-1 overflow-scroll" style={{ height: "98vh" }}>
            {filterRows?.length > 0 ? (
              filterRows
                .slice((page - 1) * rowsPerPage, page * rowsPerPage)
                .map((row: any) => (
                  <div
                    className="col-lg-6 col-md-12 col-sm-12 box"
                    key={row.id}
                  >
                    <div
                      className="shadow m-2 mt-3 pt-2 greenTopBorder"
                      style={{ minHeight: 215, maxHeight: 250 }}
                    >
                      <div className="progress mx-4">
                        <div
                          className="progress-bar"
                          role="progressbar"
                          style={{
                            width: `${row.percentage}%`,
                            backgroundColor: `${
                              row.percentage <= 25
                                ? "red"
                                : row.percentage >= 50
                                ? "green"
                                : "orange"
                            }`,
                          }}
                          aria-valuenow={row.percentage}
                          aria-valuemin={0}
                          aria-valuemax={100}
                        >
                          {row.percentage}%
                        </div>
                      </div>

                      <div className="mx-4 ">
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <div
                            style={{
                              fontSize: "18px",
                              lineHeight: "28px",
                              color: "#9c4b22",
                              paddingRight: "10px",
                              fontWeight: "900",
                              marginTop: "3%",
                              width: "100%",
                            }}
                          >
                            <p style={{ textAlign: "center" }}>
                              {row.name.replace(/[^a-zA-Z ]/g, "")}
                            </p>
                          </div>
                        </div>
                        <div className="row" style={{ height: "25%" }}>
                          {row.description.length > 80 ? (
                            <Tooltip title={row.description}>
                              <p>{` ${row.description.slice(0, 80)}...`}</p>
                            </Tooltip>
                          ) : (
                            <p>{`${row.description}`}</p>
                          )}
                        </div>
                        <div className="row" style={{ borderRadius: "5px" }}>
                          <div className="col-4">
                            <Card
                              variant="outlined"
                              sx={{
                                textAlign: "center",
                                backgroundColor: "blue",
                                color: "white",
                              }}
                            >
                              {row?.status}
                            </Card>
                          </div>
                          <div className="col-8">
                            {role === ADMIN && (
                              <>
                                <Tooltip
                                  title="Edit"
                                  className="mx-3"
                                  onClick={() => {
                                    setUserInterfaceView({ edit: true });
                                    setUserInterfacedata(row);
                                  }}
                                >
                                  <EditIcon className="fs-4 text-warning" />
                                </Tooltip>
                                <Tooltip title="Delete" className="mx-3">
                                  <DeleteIcon
                                    className="fs-4 text-danger"
                                    onClick={() => {
                                      row.isActive = false;
                                      row.projectObjectiveIds = [];
                                      deleteUserInterface(row);
                                    }}
                                  />
                                </Tooltip>
                              </>
                            )}

                            <Tooltip title="Stages" className="mx-3">
                              <StackedBarChartIcon
                                // onClick={handleOpen}
                                className="fs-4 text-primary"
                              />
                            </Tooltip>
                          </div>
                        </div>
                        <div className="row">
                          <p
                            style={{
                              marginTop: "4%",
                              width: "50%",
                              color: "blue",
                            }}
                          >
                            Start Date:{" "}
                            <span style={{ color: "black" }}>
                              {ConvertDate(row?.startDate)}
                            </span>
                          </p>
                          <p
                            style={{
                              marginTop: "4%",
                              width: "50%",
                              color: "red",
                            }}
                          >
                            End Date:
                            <span style={{ color: "black" }}>
                              {ConvertDate(row?.endDate)}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
            ) : (
              <h6
                className="d-flex justify-content-center"
                style={{ marginTop: "20%" }}
              >
                <ErrorOutlineIcon className="mx-1" />
                No Data
              </h6>
            )}
          </div>
        </div>
        <div className="col-md-3">
          <div className="row">
            <div className="col-xs-2 col-lg-12 col-md-12 col-sm-6">
              <div
                className="shadow m-2 mt-3 pt-2"
                onClick={() => reset()}
                style={{ height: "auto", minHeight: 135 }}
              >
                <div className="container">
                  <div className="row">
                    <div
                      style={{
                        fontSize: "18px",
                        lineHeight: "28px",
                        color: "#1f59c4",
                        paddingLeft: "8px",
                        fontWeight: "900",
                        marginTop: "3%",
                        textAlign: "center",
                      }}
                      className="col-md-12"
                    >
                      Total
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-md-4 col-sm-4 col-xs-6 col-5 pt-2">
                      <FormatListBulletedIcon
                        color="primary"
                        sx={{ fontSize: "255%" }}
                      />
                    </div>
                    <div
                      className="col-md-8 col-sm-8 col-xs-6 col-7 px-3"
                      style={{
                        color: "#072f70",
                        fontSize: "250%",
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      {rows?.length || 0}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-xs-4 col-lg-12 col-md-12 col-sm-6">
              <div
                className="shadow  m-2 mt-3 pt-2 "
                onClick={() => handlePendingClick("Completed")}
                style={{ height: 125 }}
              >
                <div className="container">
                  <div className="row">
                    <div
                      style={{
                        fontSize: "18px",
                        lineHeight: "28px",
                        color: "#48e022",
                        paddingLeft: "15px",
                        fontWeight: "900",
                        marginTop: "3%",
                        textAlign: "center",
                      }}
                      className="col-md-12"
                    >
                      Completed
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-md-4 col-sm-4 col-xs-6 col-5 pt-2">
                      <ChecklistIcon
                        color="success"
                        sx={{ fontSize: "225%" }}
                      />
                    </div>
                    <div
                      className="col-md-8 col-sm-8 col-xs-6 col-7 px-4"
                      style={{
                        color: "#70d40d",
                        fontSize: "255%",
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      {rows?.filter((e: any) => e?.status === "Completed")
                        ?.length || "0"}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-xs-4 col-lg-12 col-md-12 col-sm-6">
              <div
                className="shadow  m-2 mt-3 pt-2 "
                onClick={() => handlePendingClick("In progress")}
                style={{ height: 125 }}
              >
                <div className="container">
                  <div className="row">
                    <div
                      style={{
                        fontSize: "18px",
                        lineHeight: "28px",
                        color: "#d16e1d",
                        paddingLeft: "15px",
                        fontWeight: "900",
                        marginTop: "3%",
                        textAlign: "center",
                      }}
                      className="col-md-12"
                    >
                      In-Progress
                    </div>
                    <div className="row">
                      <div className="col-md-4 col-sm-4 col-xs-6 col-5 pt-2">
                        <RuleIcon color="warning" sx={{ fontSize: "225%" }} />
                      </div>
                      <div
                        className="col-md-8 col-sm-8 col-xs-6 col-7 px-4"
                        style={{
                          color: "#824908",
                          fontSize: "250%",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        {rows?.filter((e: any) => e?.status === "In Progress")
                          ?.length || "0"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-xs-4 col-lg-12 col-md-12 col-sm-6">
              <div
                className="shadow  m-2 mt-3 pt-2 "
                onClick={() => handlePendingClick("pending")}
                style={{ height: 125 }}
              >
                <div className="container">
                  <div className="row">
                    <div
                      style={{
                        fontSize: "18px",
                        lineHeight: "28px",
                        color: "#1f59c4",
                        paddingLeft: "8px",
                        fontWeight: "900",
                        marginTop: "3%",
                        textAlign: "center",
                      }}
                      className="col-md-12"
                    >
                      Pending
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-md-4 col-sm-4 col-xs-6 col-5 pt-2">
                      <PendingActionsIcon
                        color="error"
                        sx={{ fontSize: "225%" }}
                      />
                    </div>
                    <div
                      className="col-md-8 col-sm-8 col-xs-6 col-7 px-3"
                      style={{
                        color: "#7d0d09",
                        fontSize: "250%",
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      {rows?.filter((e: any) => e?.status === "Pending")
                        ?.length || "0"}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <AddUserInterface
        openDialog={UserInterfaceView}
        data1={data?.length > 0 ? data : []}
        setOpenDialog={setUserInterfaceView}
        SetReload={SetReload}
        projectId={location?.state?.projectId}
      />
      <EditUserInterface
        openDialog={UserInterfaceView}
        setOpenDialog={setUserInterfaceView}
        Data={userInterfacedata}
        data1={data?.length > 0 ? data : []}
        projectId={location.state.projectId}
        SetReload={SetReload}
      />
      <ViewUserInterface
        openDialog={UserInterfaceView}
        setOpenDialog={setUserInterfaceView}
        Data={userInterfacedata}
      />
      <BackDrop open={loading} />
    </div>
  );
};
