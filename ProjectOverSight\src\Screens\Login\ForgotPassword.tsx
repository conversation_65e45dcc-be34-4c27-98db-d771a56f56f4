import {
  <PERSON><PERSON>,
  <PERSON>alogA<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>T<PERSON><PERSON>,
  Button,
  TextField,
} from "@mui/material";
import { BASE_URL } from "../../Constants/Common/Urls";
import { Post } from "../../Services/Axios";
import { useState } from "react";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { PasswordUpdateDto } from "../../Models/Employee/User";
import Swal from "sweetalert2";
import { StatusCodes } from "../../Constants/Common/StatusCodes";
import Alert from "@mui/material/Alert";
import { PasswordSet } from "./PasswordSet";
import { validatePassword } from "../../Utilities/Helpers";
import { AlertOption } from "../../Models/Common/AlertOptions";
import { Regex } from "../../Constants/Regex/Regex";

type ModelError = {
  email?: { error: boolean; message: string };
  otp?: { error: boolean; message: string };
  password?: { error: boolean; message: string };
  confirmPassword?: { error: boolean; message: string };
};

type viewComponent = {
  sendOtpBtn: boolean;
  resendOtpBtn: boolean;
  passwordFields: boolean;
  otpSuccess: boolean;
};

export const ForgotPassword = ({ open, setOpen }: any) => {
  const [loader, setLoader] = useState<boolean>(false);
  const [errors, setError] = useState<ModelError>();
  const [view, setView] = useState<viewComponent>({
    sendOtpBtn: true,
    resendOtpBtn: false,
    passwordFields: false,
    otpSuccess: false,
  });
  const [passwordDto, setpasswordDto] = useState<PasswordUpdateDto>({
    email: "",
    password: "",
    confirmPassword: "",
  });

  const onSubmitHandler = async () => {
    if (!validatePassword(passwordDto)) return;

    setLoader(true);
    var response: any = await Post(`${BASE_URL}Auth/ChangePW`, passwordDto);

    if (response?.response) {
      response = response?.response?.response ?? response?.response;
    }

    const { status, data } = response;

    var options: AlertOption = {
      title: `${status === StatusCodes.SUCCESS ? "Success" : "Error"}`,
      text: data,
      icon: `${status === StatusCodes.SUCCESS ? "success" : "error"}`,
    };

    Swal.fire(options);

    if (status === StatusCodes.SUCCESS) {
      handleClose();
    } else {
      setLoader(false);
    }
  };

  async function sendOtp() {
    setError({});
    setView({
      ...view,
      otpSuccess: false,
    });
    if (passwordDto.email.length === 0) {
      setError({ ...errors, email: { error: true, message: "" } });
      return;
    }

    if (!Regex.EMAIL.test(passwordDto.email)) {
      setError({
        ...errors,
        email: { error: true, message: "Invalid email address!" },
      });
      return;
    }

    setLoader(true);
    const { data }: any = await Post(`${BASE_URL}Auth/SendEmail`, passwordDto);

    if (!data) {
      setError({
        ...errors,
        email: { error: true, message: "Invalid email address!" },
      });
    }

    if (data) {
      setView({
        ...view,
        sendOtpBtn: false,
        resendOtpBtn: true,
        otpSuccess: true,
      });
    }
    setLoader(false);
  }

  async function verifyOtp() {
    setError({});
    if (!passwordDto.verifyOtp || passwordDto.verifyOtp?.length === 0) {
      setError({ ...errors, otp: { error: true, message: "" } });
      return;
    }
    setLoader(true);
    const { data }: any = await Post(`${BASE_URL}Auth/VerifyOTP`, passwordDto);
    setLoader(false);

    if (data !== "OTP is valid") {
      setError({ ...errors, otp: { error: true, message: data } });
      return;
    } else {
      setView({
        ...view,
        sendOtpBtn: false,
        resendOtpBtn: false,
        otpSuccess: false,
        passwordFields: true,
      });
    }
  }

  const handleClose = () => {
    setpasswordDto({
      email: "",
      password: "",
      confirmPassword: "",
    });
    setView({
      sendOtpBtn: true,
      resendOtpBtn: false,
      passwordFields: false,
      otpSuccess: false,
    });
    setError({});
    setOpen(false);
    setLoader(false);
  };

  return (
    <>
      <Dialog
        open={open}
        sx={{
          "& .MuiDialog-container": {
            "& .MuiPaper-root": {
              height: `${view.sendOtpBtn ? "40vh" : "55vh"}`,
              width: "80%",
              maxWidth: "32vw",
            },
          },
        }}
      >
        <div
          style={{
            backgroundColor: "#f0f0f0",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <DialogTitle>Create New Password</DialogTitle>
          <CancelOutlinedIcon
            onClick={handleClose}
            className="mx-2"
            sx={{
              color: "red",
              fontSize: "30px",
              cursor: "pointer",
            }}
          />
        </div>
        <DialogContent>
          <div className="d-flex flex-column">
            {view.otpSuccess && (
              <Alert severity="success" className="col text-sm w-100">
                OTP has been sent to {passwordDto.email}
              </Alert>
            )}
            <TextField
              margin="normal"
              required
              className="col mx-auto"
              fullWidth
              autoComplete="new-email"
              disabled={!view.sendOtpBtn}
              label="Email Address"
              type="email"
              helperText={errors?.email?.message}
              error={errors?.email?.error}
              onChange={(e) => {
                setError({ ...errors, email: { error: false, message: "" } });
                setpasswordDto({
                  ...passwordDto,
                  email: e.target.value,
                  otp: `${Math.floor(1000 + Math.random() * 9000)}`,
                });
              }}
            />

            {view.resendOtpBtn && (
              <TextField
                label="OTP"
                required
                helperText={errors?.otp?.message}
                error={errors?.otp?.error}
                inputProps={{ maxLength: 6 }}
                onChange={(e) => {
                  setView({
                    ...view,
                    otpSuccess: false,
                  });
                  e.target.value = e.target.value.replace(Regex.NUMBER, "");
                  setError({ ...errors, otp: { error: false, message: "" } });
                  setpasswordDto({ ...passwordDto, verifyOtp: e.target.value });
                }}
              />
            )}
            {view.sendOtpBtn && (
              <Button
                variant="contained"
                className="w-25 mx-auto mt-4"
                disabled={loader}
                onClick={() => sendOtp()}
              >
                Send otp
              </Button>
            )}

            {view.resendOtpBtn && (
              <div className="mx-auto">
                <Button
                  variant="contained"
                  className="mt-4 mx-2"
                  disabled={loader}
                  onClick={() => sendOtp()}
                >
                  Resent Otp
                </Button>
                <Button
                  variant="contained"
                  className="mt-4 mx-2"
                  disabled={loader}
                  onClick={() => verifyOtp()}
                >
                  Verify
                </Button>
              </div>
            )}
          </div>
          {view.passwordFields && (
            <>
              <PasswordSet
                setpasswordDto={setpasswordDto}
                passwordDto={passwordDto}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                onChange={(e) =>
                  setpasswordDto({
                    ...passwordDto,
                    confirmPassword: e.target.value,
                  })
                }
                autoComplete="off"
                label="Confirm Password"
                type="password"
              />
            </>
          )}
        </DialogContent>
        {view.passwordFields && (
          <DialogActions>
            <Button
              onClick={handleClose}
              variant="contained"
              color="error"
              disabled={loader}
            >
              Cancel
            </Button>
            <Button
              onClick={onSubmitHandler}
              variant="contained"
              color="success"
              type="submit"
              disabled={loader}
            >
              {loader ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        )}
      </Dialog>
    </>
  );
};
