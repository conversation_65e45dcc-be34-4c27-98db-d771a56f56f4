import { Button, Box } from '@mui/material'
import Modal from '@mui/material/Modal';
import { EditEmployee } from '../Employee/EmployeeEdit';
import { Regex } from '../../../Constants/Regex/Regex';
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import EditIcon from "@mui/icons-material/Edit";
import { useEffect, useState } from 'react';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import { Get } from "../../../Services/Axios";
import * as React from 'react';
import CircularProgress from '@mui/material/CircularProgress';

const style = {
  position: 'absolute' as 'absolute',
  top: '40%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 1200,
  height: 600,
  bgcolor: 'background.paper',
  boxShadow: 25,
  p: 4,
};


interface Props {
  employees: any[];
  onClose: () => void;
  setReload: React.Dispatch<React.SetStateAction<boolean>>;
}


const StyledTableCells = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));


export const EmployeeModal: React.FC<Props> = ({ employees, onClose, setReload }) => {
  const [refetch, setRefetch] = useState<boolean>(false);
  const [rows, setRows] = useState<any>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);;
  const [loading, setLoading] = useState<boolean>(true);
  const [empView, setEmpView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });


  useEffect(() => {
    let employeeList = Get("app/Employee/GetEmployeeList");
    employeeList.then((response: any) => {
      const filteredRows = response.data.filter((row: any) => employees?.some(employee => employee?.userId === row?.userId));
      setRows(filteredRows);
    });
    setLoading(false);
    setReload(false);
  }, [employees, refetch]);


  let email: string[] = [];
  rows?.forEach((row: any) => {
    if (row.user?.email) {
      email.push(row.user?.email);
    }
  });

  return (
    <>
      <React.Fragment>
        <Modal
          open={employees !== null}
          onClose={() => {
            onClose();
            setRows([]);
            setRefetch(true)
            setSelectedEmployee(null); 
            setLoading(true)
          }}
          sx={{ mt: 15, backgroundColor: 'transparent' }}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
        >
          <Box sx={style}>
            <Button variant='contained' color='error' sx={{ float: 'right', mb: '15px' }} onClick={onClose}>Close</Button>
            <TableContainer component={Paper}>
              <Table sx={{ minWidth: 700 }} aria-label="simple table">
                <TableHead sx={{ position: 'sticky', top: 0, zIndex: 99 }}>
                  <TableRow sx={{ backgroundColor: "Highlight" }}>
                    <StyledTableCells align="center">Action</StyledTableCells>
                    <StyledTableCells align="center">EmployeeId </StyledTableCells>
                    <StyledTableCells align="center">
                      Employee Name
                    </StyledTableCells>
                    <StyledTableCells align="center">
                      PhoneNumber
                    </StyledTableCells>
                    <StyledTableCells align="center">Department</StyledTableCells>
                    <StyledTableCells align="center">In-Time</StyledTableCells>
                    <StyledTableCells align="center">In-OutTime</StyledTableCells>
                    <StyledTableCells align="center">IsActive</StyledTableCells>
                  </TableRow>
                </TableHead>
                <TableBody>
                {!loading ? (
                  rows && rows.length > 0 ? (
                  !loading && rows && rows.map((row: any, index: number) => (
                    <StyledTableRow key={index}>
                      <StyledTableCell align="center">
                        <EditIcon
                          className="fs-4 text-warning mx-2"
                          onClick={() => {
                            const selectedEmp = rows.find((rows: any) => rows.id === row.id);
                            if (selectedEmp) {
                              setEmpView({ edit: true });
                              setSelectedEmployee(selectedEmp);
                            }
                          }}
                        />
                      </StyledTableCell>
                      <StyledTableCell align="center">
                        {row.id}
                      </StyledTableCell>
                      <StyledTableCell align="center">
                        {row?.name ? row.name.replace(Regex.CHAR_SPACE, "")
                          .charAt(0)
                          .toUpperCase() +
                          row?.name?.replace(Regex.CHAR_SPACE, "")
                            .slice(1)
                            .toLowerCase() : ""}
                      </StyledTableCell>
                      <StyledTableCell align="center">
                        {row.phoneNumber}
                      </StyledTableCell>
                      <StyledTableCell align="center">
                        {row.department}
                      </StyledTableCell>
                      <StyledTableCell align="center">
                        {row.officialIntime ? row.officialIntime : "-"}
                      </StyledTableCell>
                      <StyledTableCell align="center">
                        {row.officialOuttime ? row.officialOuttime : '-'}
                      </StyledTableCell>
                      <StyledTableCell align="center">
                        {row.isActive ? "Active" : "Inactive"}
                      </StyledTableCell>
                    </StyledTableRow>
                  ))): (
                     <StyledTableRow>
                     <StyledTableCell colSpan={8} rowSpan={8}  align="center" className="loading-cell">
                       No Employees
                     </StyledTableCell>
                   </StyledTableRow>
                  )
                ) : (
                  <StyledTableRow>
                  <StyledTableCell colSpan={8} rowSpan={8}  align="center" className="loading-cell">
                    <CircularProgress color="success" />
                  </StyledTableCell>
                </StyledTableRow>
                )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </Modal>
      </React.Fragment>
      {selectedEmployee && (
        <EditEmployee
          editEmployee={empView}
          setEditEmployee={setEmpView}
          setRefetch={setRefetch}
          emailList={email.filter((e) => e !== selectedEmployee?.user?.email)}
          data={selectedEmployee}
        />
      )}
    </>
  );
};