import PsychologyIcon from "@mui/icons-material/Psychology";
import NotesIcon from "@mui/icons-material/Notes";
import BadgeIcon from "@mui/icons-material/Badge";
import PersonIcon from "@mui/icons-material/Person";
import GroupsIcon from "@mui/icons-material/Groups";
import HourglassTopIcon from "@mui/icons-material/HourglassTop";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import ViewKanbanIcon from "@mui/icons-material/ViewKanban";
import AttributionIcon from "@mui/icons-material/Attribution";
import { ScrumIconBlack } from "../CommonComponents/BackDrop";
import { Component } from "lucide-react";
import BusinessIcon from "@mui/icons-material/Business";
import EditNoteIcon from '@mui/icons-material/EditNote';
import CategoryIcon from '@mui/icons-material/Category';
// import { Component } from "lucide-react";
import { GanttChartSquare } from "lucide-react";
// import ManageAccountsIcon from '@mui/icons-material/ManageAccounts'; 
// import DescriptionIcon from "@mui/icons-material/Description";
// import { Dashboard } from '@mui/icons-material';

// import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
export const AdminPages = [
  { name: "Task", icon: TaskAltIcon },
    { name: "Weekly Plan", icon:GanttChartSquare},
    { name: "Employee", icon: BadgeIcon },
    { name: "Project", icon: ViewKanbanIcon },
    { name: "Team", icon: GroupsIcon },
    { name: "Attendance", icon: PersonIcon },
    { name: "PMO Scrum", icon: ScrumIconBlack },
    // { name: "Project Group", icon: GroupsIcon}
    //{ name: "Check List",icon: PersonIcon}
];
export const Admindrawer = [
  { name: "Task", icon: TaskAltIcon },
  { name: "Weekly Plan", icon:GanttChartSquare},
  { name: "Employee", icon: BadgeIcon },
  { name: "Project", icon: ViewKanbanIcon },
  { name: "Team", icon: GroupsIcon },
  { name: "Attendance", icon: PersonIcon },
  { name: "PMO Scrum", icon: ScrumIconBlack },
  { name: "Task Progress", icon: HourglassTopIcon },
  {name : "Requirement Matrix", icon:EditNoteIcon},
  { name: "Project Group", icon: Component},
  {name: "Department", icon: BusinessIcon }, 
  // { name: "Project Group", icon: Component},
  { name: "Skill", icon: PsychologyIcon },
  // { name: "Comment", icon: MessageIcon },
  { name: "Release Notes", icon: NotesIcon },
  { name: "Leave", icon: CalendarMonthIcon },
  { name: "Common Master", icon:AttributionIcon},
  { name: "Category Management", icon:CategoryIcon}
  // { name: "User Management" , icon:ManageAccountsIcon}
  // { name: "User Management" , icon:ManageAccountsIcon}
];
