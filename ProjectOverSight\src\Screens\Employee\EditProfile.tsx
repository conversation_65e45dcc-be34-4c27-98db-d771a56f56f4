import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
} from "@mui/material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { Form } from "react-bootstrap";
import { useForm } from "react-hook-form";
import { useContextProvider } from "../../CommonComponents/Context";
import Swal from "sweetalert2";
import { Get, Post } from "../../Services/Axios";
import { AlertOption } from "../../Models/Common/AlertOptions";
import { useEffect, useState } from "react";
import { CommonMaster } from "../../Models/Common/CommonMaster";

type EditProfileProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

const formFields = [
  "userName",
  "phoneNumber",
  "email",
  "location",
  "id",
  "employeeCode",
  "manager",
];

export const EditProfile = ({ open, setOpen }: EditProfileProps) => {
  const { handleSubmit, register, resetField } = useForm();
  const [save, setSave] = useState(false);
  const [employee, setEmployee] = useState<any>(null);
  const { user, commonMaster } = useContextProvider();

  async function fetchData() {
    var employee: any = await Get(
      `app/Employee/GetEmployeeById?employeeId=${user?.employeeId}`
    );
    setEmployee(employee?.data);
  }

  useEffect(() => {
    fetchData();
    return () => {};
  }, [open]);

  const onSubmitHandler = async (data: any) => {
    if (data.phoneNumber.length < 10) {
      Swal.fire({
        title: "",
        text: "Please enter valid phone number.",
        icon: "warning",
      });
      return;
    }

    setSave(true);
    data.isActive = true;
    const { error }: any = await Post("app/Employee/UpdateProfile", data);
    var option: AlertOption;

    if (error) {
      option = {
        title: "",
        text: "error occured",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Profile Updated!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      handleClose();
    });
  };

  const handleClose = () => {
    formFields.map((field: any) => {
      resetField(field);
    });
    setSave(false);
    setOpen(false);
  };

  return (
    <Dialog open={open}>
      <DialogTitle className="pop-title-bg">
        Edit Profile{" "}
        <CancelOutlinedIcon
          className="float-end m-1 text-danger pointer"
          onClick={handleClose}
        />
      </DialogTitle>
      <Form onSubmit={handleSubmit(onSubmitHandler)}>
        <DialogContent className="row popup d-flex justify-content-center mt-3">
          <div className="row">
            <TextField
              label="Name"
              className="col m-2"
              required
              defaultValue={employee?.user?.name}
              {...register("userName")}
            />
            <TextField
              label="Phone Number"
              className="col m-2"
              required
              defaultValue={employee?.phoneNumber}
              inputProps={{ minLength: 10, maxLength: 10 }}
              {...register("phoneNumber", {
                onChange: (e: any) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, "");
                },
              })}
            />
          </div>
          <div className="row">
            <TextField
              label="Email"
              className="col m-2"
              required
              type="email"
              defaultValue={employee?.user?.email}
              {...register("email")}
            />
            <FormControl fullWidth className="col m-2">
              <InputLabel required id="location">
                Work Location
              </InputLabel>
              <Select
                labelId="location"
                required
                label="Work Location"
                defaultValue={employee?.location}
                {...register("location")}
              >
                {commonMaster
                  .filter((x: CommonMaster) => x.codeType === "Location")
                  .sort(
                    (a: CommonMaster, b: CommonMaster) =>
                      a.displaySequence - b.displaySequence
                  )
                  .map((option: any) => (
                    <MenuItem key={option.codeValue} value={option.codeValue}>
                      {option.codeValue}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          </div>
          <div className="row">
            <TextField
              label="Employee Code"
              className="col m-2"
              defaultValue={employee?.employeeCode}
              required
              {...register("employeeCode")}
            />
            <TextField
              className="col m-2"
              defaultValue={employee?.manager}
              label="Manager"
              {...register("manager")}
            />
          </div>
          <input type="hidden" {...register("id")} value={user?.employeeId} />
          <input type="hidden" {...register("role")} value={user?.userRoles} />
        </DialogContent>
        <DialogActions>
          <Button className="bg-danger text-light shadow" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            variant="contained"
            type="submit"
            disabled={save}
            color="success"
          >
            {save ? "Saving..." : "Save"}
          </Button>
        </DialogActions>
      </Form>
    </Dialog>
  );
};
