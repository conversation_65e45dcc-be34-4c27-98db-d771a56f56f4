.voice-recorder-container {
  max-width: 700px;
  margin: 30px auto;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  font-family: "Segoe UI", sans-serif;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.record-btn {
  background-color: #007bff;
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.record-btn.recording {
  background-color: #dc3545;
}

.record-btn:hover {
  background-color: #0056b3;
}

.reset-btn {
  background-color: #6c757d;
  color: #ffffff;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.reset-btn:hover {
  background-color: #5a6268;
}

.output-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.transcript-box {
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  resize: none;
  background-color: #f9f9f9;
}
