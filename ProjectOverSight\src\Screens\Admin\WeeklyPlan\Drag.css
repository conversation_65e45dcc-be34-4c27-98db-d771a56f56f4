.Drags {
    font-family: sans-serif;
    text-align: center;
    /* border: 1px solid grey; */
    display: inline-flex;
    width: 100%;
    /* height: 100%; */
    justify-content: space-between;
  }

  .plan {
    border-radius: 16px;
    box-shadow: 0 30px 30px -25px rgba(0, 38, 255, 0.205);
    padding: 10px;
    background-color: #fff;
    color: #697e91;
    max-width: 300px;
    
  }

  

  .drag_container{
    border-radius: 16px;
    /* box-shadow: 0 30px 30px -25px rgba(0, 38, 255, 0.205); */
    padding: 10px;
    /* background-color: #fff; */
    /* color: #697e91; */
    width: '100';
  }
  
  .plan strong {
    font-weight: 600;
    /* color: #425275; */
  }
  
  .plan .innerCard {
    align-items: center;
    padding: 10px;
    padding-top: 10px;
    background-color: #f4f5f8;
    border-radius: 12px;
    position: relative;
    outline-offset: 2px solid #425275;
  }

  .plan .title {
    font-weight: 600;
    font-size: 1.25rem;
    color: #425675;
  }
  
  .plan .title + * {
    margin-top: 0.75rem;
  }
  
  .plan .info + * {
    margin-top: 1rem;
  }
  
  .plan .features {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 12px;
    height: 80px;
    border: 1px solid blueviolet;
    
  }
  
  .plan .features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .plan .features li + * {
    margin-top: 0.75rem;
  }

  .dragged {
    transition: transform 0.3s ease-in-out;
    transform: scale(1.1); /* You can customize the scaling or add other transformations */
  }
  
  .dragged-over {
    border: 2px dashed #007bff; /* You can customize the border style when dragged over */
  }
  
  /* .plan .features .icon {
    background-color: #1FCAC5;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
  } */
  
  /* .plan .features .icon svg {
    width: 14px;
    height: 14px;
  } */
  .statuss{
    /* background-color: #7d9ec0; */
    font-weight: bolder;
  }
  
  .plan .features + * {
    margin-top: 1.25rem;
    
  }

  .cardlist{
    display: flex;
    justify-content: space-between;
  }
  
  .plan .action {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: end;
  }
  
  /* .plan .button {
    background-color: #6558d3;
    border-radius: 6px;
    color: #fff;
    font-weight: 500;
    font-size: 1.125rem;
    text-align: center;
    border: 0;
    outline: 0;
    width: 100%;
    padding: 0.625em 0.75em;
    text-decoration: none;
  }

  .button {
    position: relative;
    width: 100%;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    border: 1px solid #34974d;
    background-color: #3aa856;
  } */
  
  /* .button, .button__icon, .button__text {
    transition: all 0.3s;
  }
  
  .button .button__text {
    transform: translateX(30px);
    color: #fff;
    font-weight: 600;
  }
  
  .button .button__icon {
    position: absolute;
    transform: translateX(109px);
    height: 100%;
    width: 39px;
    background-color: #34974d;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .button .svg {
    width: 30px;
    stroke: #fff;
  } */
  
  /* .button:hover {
    background: #34974d;
  }
  
  .button:hover .button__text {
    color: transparent;
  }
  
  .button:hover .button__icon {
    width: 148px;
    transform: translateX(0);
  }
  
  .button:active .button__icon {
    background-color: #2e8644;
  }
  
  .button:active {
    border: 1px solid #2e8644;
  } */
  
  
 