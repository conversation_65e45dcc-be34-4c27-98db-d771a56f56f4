
.login-container {
    animation: fadeIn 1s ease-in-out;
  }
  .title-card{
    animation: fadeIn 1s ease-in-out ;

  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .button {
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  }
  
  .button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  .LinearProgress {
    animation: progressAnimation 1s ease-in-out infinite;
  }
  
  @keyframes progressAnimation {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 100% 0;
    }
  }
  
  .MuiAlert-root {
    animation: alertAnimation 0.5s ease-in-out forwards;
  }
  
  @keyframes alertAnimation {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  

  .login-container {
    transition: margin-top 0.5s ease-in-out;
  }
  
  @media (max-width: 768px) {
    .login-container-transition {
      margin-top: 200px;
  }
  }

  @media screen and (max-width: 768px) {
    .Login-main {
      flex-direction: column;
      
    }
    .Title-div{
       display: flex;
       text-align: center;
       position: relative;
       right: 20px;
      }
      .title-card{
        width:100%;
      }
      
    .login-container {
      max-width: 100%;
    }
  
  }


  body, html { height: 90%; }
  .outer {
    width: 1px;
    height: 50vh;
    margin: 15px auto;
    position: relative;
    display: flex;
    left: 50%;
    bottom: 40%;
    overflow: hidden;
  }
  .inner {
    position: absolute;
    height: 100%;
    top:30%;
    background:rgb(196, 17, 17);
    box-shadow: 0px 0px 30px 20px lightgrey;
  }


  @media screen and (max-width: 600px) {
    .Login-main {
      flex-direction: column;
      
    }
    .Title-div{
       display: flex;
       text-align: center;
       position: relative;
       right: 20px;
       
      
       /* margin-bottom: 15%; */
  
      }
      .title-card{
        width:100%;
      }
      
    .login-container {
      max-width: 100%;
    }
  
  }


.bg-design{
  z-index: -10;
  background: #F6F8F9;
  background: radial-gradient(circle farthest-corner at center center, #F6F8F9 0%, #E5EBEE 30%, #D7DEE3 60%, #F5F7F9 100%);
  height: 100vh;
  position:absolute;
  width: 100%;
}

 



