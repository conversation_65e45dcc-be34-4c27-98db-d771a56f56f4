import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>rum<PERSON>, FormControl, Tooltip, Typography } from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import { Link, useLocation } from "react-router-dom";
import { Get } from "../../../Services/Axios";
import { WeekEndingDate } from "../../../Utilities/Utils";
import { TypeAnimation } from "react-type-animation";
import BackDrop from "../../../CommonComponents/BackDrop";
import { useContextProvider } from "../../../CommonComponents/Context";

const WeeklyPlanDashboard = () => {
  const [weeklyPlanDashboard, setWeeklyPlanDashboard] = useState([]);
  const reload = false;
  const { state } = useLocation();
  const [selectedOption, setSelectedOption] = useState("ThisWeek");
  const [loading, setLoading] = useState<boolean>(true);
  const [isHoveredTotal, setIsHoveredTotal] = useState(false);
  const [isHoveredCompleted, setIsHoveredCompleted] = useState(false);
  const [isHoveredPending, setIsHoveredPending] = useState(false);
  const [isHoveredTotalCurrent, setIsHoveredTotalCurrent] = useState(false);
  const {role} = useContextProvider();
  const [isHoveredCompletedCurrent, setIsHoveredCompletedCurrent] =
    useState(false);
  const [isHoveredPendingCurrent, setIsHoveredPendingCurrent] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    const teamId = state.data.id;
    const response: any = await Get(
      `app/WeeklyPlan/GetWeeklyPlanDashboard?teamId=${teamId}`
    );
    setWeeklyPlanDashboard(response?.data);
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [reload, state.teamId]);

  const getPreviousWeekEndingDate = () => {
    const previousWeekEndingDate = WeekEndingDate();
    previousWeekEndingDate.setDate(previousWeekEndingDate.getDate() - 7);
    return previousWeekEndingDate.toISOString().slice(0, 10);
  };

  const previousWeekEndingDate = getPreviousWeekEndingDate();

  const getNextWeekEndingDate = () => {
    const nextWeekEndingDate = WeekEndingDate();
    nextWeekEndingDate.setDate(nextWeekEndingDate.getDate() + 7);
    return nextWeekEndingDate.toISOString().slice(0, 10);
  };

  const nextWeekEndingDate = getNextWeekEndingDate();

  const handleSelectChange = (event: any) => {
    setSelectedOption(event.target.value);
    setLoading(false);
  };

  const getCurrentWeekData = () => {
    return weeklyPlanDashboard?.filter(
      (row: any) =>
        row.weekEndingDate.slice(0, 10) ===
        WeekEndingDate().toISOString().slice(0, 10)
    );
  };

  const getLastWeekData = () => {
    return weeklyPlanDashboard?.filter(
      (row: any) => row.weekEndingDate.slice(0, 10) === previousWeekEndingDate
    );
  };

  const getNextWeekData = () => {
    const nextWeekEndingDate = getNextWeekEndingDate();
    return weeklyPlanDashboard?.filter(
      (row: any) => row.weekEndingDate.slice(0, 10) === nextWeekEndingDate
    );
  };

  const renderTableRows = () => {
    switch (selectedOption) {
      case "LastWeek":
        return getLastWeekData();
      case "NextWeek":
        return getNextWeekData();
      default:
        return getCurrentWeekData();
    }
  };

  const currentWeekEndingDate = WeekEndingDate().toISOString().slice(0, 10);

  const LastCompletedWeek = weeklyPlanDashboard?.filter(
    (x: any) =>
      x?.weekEndingDate.slice(0, 10) === previousWeekEndingDate &&
      x?.status === "completed"
  ).length;

  return (
    <>
      <div className="d-flex flex-column">
        <Breadcrumbs className="mt-3 mx-3" separator=">">
          <Link to={`/${role}`}>
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Link to={`/${role}/WeeklyPlan`}>
            <Typography sx={{ fontWeight: "bold" }}>WeeklyPlan</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold" }}>Team Weekly Plan</Typography>
        </Breadcrumbs>

        <label className="p-3 rounded-3 shadow border-3 fs-3 text-primary fw-bold mx-auto">
          <TypeAnimation
            sequence={[state.data?.name]}
            wrapper="span"
            speed={50}
            cursor={false}
            repeat={1}
          />
        </label>

        <div
          className="last-weekend-div"
          style={{
            display: "flex",
            margin: "0 auto",
            marginBottom: "2%",
            backgroundColor: "white",
            width: "80%",
            border: "1px solid #90add4",
            boxShadow: "0px 0px 5px grey",
            borderRadius: "5px",
            height: "20%",
            alignItems: "center",
          }}
        >
          <div>
            <div
              style={{
                textAlign: "center",
                height: "80%",
                margin: "20%",
                color: "rgb(3, 108, 219)",
              }}
            >
              <h5>Last Week Planned items</h5>
            </div>
          </div>
          <div
            className="d-flex"
            style={{
              justifyContent: "space-around",
              width: "70%",
              marginLeft: "50px",
              alignItems: "center",
              padding: "10px",
              height: "100%",
            }}
          >
            <div
              style={{
                flex: 1,
                marginLeft: "20px",
                textAlign: "center",
                height: "80%",
                backgroundColor: "#dedede",
                color: "#4b4c4d",
                borderRadius: "5px",
                cursor: "pointer",
                boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.2)",
                transition: "transform 0.3s",
                transform: isHoveredTotal ? "scale(1.1)" : "scale(1.0)",
              }}
              onMouseEnter={() => setIsHoveredTotal(true)}
              onMouseLeave={() => setIsHoveredTotal(false)}
            >
              <Link
                to={`/${role}/WeeklyPlan`}
                state={{
                  teamName: state.data?.name,
                  WeekEndingDateCurr: previousWeekEndingDate,
                }}
                style={{ textDecoration: "none", color: "#4b4c4d" }}
              >
                <div className="m-3" style={{ height: "40%" }}>
                  Total Planned Items
                </div>
                <p style={{ marginTop: "-15px" }}>
                  {
                    weeklyPlanDashboard?.filter(
                      (x: any) =>
                        x.weekEndingDate.slice(0, 10) === previousWeekEndingDate
                    ).length || 0
                  }
                </p>
              </Link>
            </div>
            <div
              style={{
                flex: 1,
                marginLeft: "20px",
                textAlign: "center",
                height: "80%",
                backgroundColor: "#05b5f5",
                color: "#014259",
                borderRadius: "5px",
                cursor: "pointer",
                boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.2)",
                transition: "transform 0.3s",
                transform: isHoveredCompleted ? "scale(1.1)" : "scale(1.0)",
              }}
              onMouseEnter={() => setIsHoveredCompleted(true)}
              onMouseLeave={() => setIsHoveredCompleted(false)}
            >
              <Link
                to={`/${role}/WeeklyPlan`}
                state={{
                  teamName: state.data?.name,
                  status: "Completed",
                  WeekEndingDateCurr: previousWeekEndingDate,
                }}
                style={{ textDecoration: "none", color: "#4b4c4d" }}
              >
                <div className="m-3" style={{ height: "40%" }}>
                  Completed Planned Items
                </div>
                <p style={{ marginTop: "-15px" }}>{LastCompletedWeek || 0}</p>
              </Link>
            </div>
            <div
              style={{
                flex: 1,
                marginLeft: "20px",
                textAlign: "center",
                height: "80%",
                backgroundColor: "#ffa200",
                color: "#613e01",
                borderRadius: "5px",
                boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.2)",
                transition: "transform 0.3s",
                transform: isHoveredPending ? "scale(1.1)" : "scale(1.0)",
              }}
              onMouseEnter={() => setIsHoveredPending(true)}
              onMouseLeave={() => setIsHoveredPending(false)}
            >
              <Link
                style={{ color: "#613e01" }}
                to={`/${role}/WeeklyPlan`}
                state={{
                  teamName: state.data?.name,
                  status: "Moved",
                  WeekEndingDateCurr: previousWeekEndingDate,
                }}
              >
                <div className="m-3" style={{ height: "40%" }}>
                  Pending Planned Items
                </div>
                <p style={{ marginTop: "-15px" }}>
                  {
                    weeklyPlanDashboard?.filter(
                      (x: any) =>
                        x.weekEndingDate.slice(0, 10) ===
                          previousWeekEndingDate && x.status === "Moved"
                    ).length || 0
                  }
                </p>
              </Link>
            </div>
          </div>
        </div>

        <div
          className="current-weekend-div"
          style={{
            display: "flex",
            margin: "0 auto",
            backgroundColor: "white",
            width: "80%",
            border: "1px solid #90add4",
            boxShadow: "0px 0px 5px grey",
            borderRadius: "5px",
            height: "20%",
            alignItems: "center",
          }}
        >
          <div style={{ width: "13.5rem" }}>
            <div
              style={{
                textAlign: "center",
                height: "80%",
                margin: "20%",
                color: "rgb(3, 108, 219)",
              }}
            >
              <h5>Current Week Planned items</h5>
            </div>
          </div>
          <div
            className="d-flex"
            style={{
              justifyContent: "space-around",
              marginLeft: "50px",
              padding: "10px",
              alignItems: "center",
              width: "70%",
              height: "100%",
            }}
          >
            <div
              style={{
                flex: 1,
                marginLeft: "18px",
                textAlign: "center",
                backgroundColor: "#dedede",
                color: "#4b4c4d",
                height: "80%",
                borderRadius: "5px",
                boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.2)",
                transition: "transform 0.3s",
                transform: isHoveredTotalCurrent ? "scale(1.1)" : "scale(1.0)",
              }}
              onMouseEnter={() => setIsHoveredTotalCurrent(true)}
              onMouseLeave={() => setIsHoveredTotalCurrent(false)}
            >
              <Link
                style={{ color: "#4b4c4d" }}
                to={`/${role}/WeeklyPlan`}
                state={{
                  teamName: state.data?.name,
                }}
              >
                <div className="m-3" style={{ height: "40%" }}>
                  Total Planned Items
                </div>
                <p style={{ marginTop: "-15px" }}>
                  {weeklyPlanDashboard?.length || 0 }
                </p>
              </Link>
            </div>

            <div
              style={{
                flex: 1,
                marginLeft: "20px",
                textAlign: "center",
                backgroundColor: "#05b5f5",
                color: "#014259",
                height: "80%",
                borderRadius: "5px",
                boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.2)",
                transition: "transform 0.3s",
                transform: isHoveredCompletedCurrent
                  ? "scale(1.1)"
                  : "scale(1.0)",
              }}
              onMouseEnter={() => setIsHoveredCompletedCurrent(true)}
              onMouseLeave={() => setIsHoveredCompletedCurrent(false)}
            >
              <Link
                style={{ color: "#014259" }}
                to={`/${role}/WeeklyPlan`}
                state={{
                  teamName: state.data?.name,
                  status: "Completed",
                  WeekEndingDateCurr: currentWeekEndingDate,
                }}
              >
                <div className="m-3" style={{ height: "40%" }}>
                  Completed Planned Items
                </div>
                <p style={{ marginTop: "-15px" }}>
                  {
                    weeklyPlanDashboard?.filter(
                      (x: any) =>
                        x.weekEndingDate.slice(0, 10) ===
                          WeekEndingDate().toISOString().slice(0, 10) &&
                        x.status === "completed"
                    ).length || 0
                  }
                </p>
              </Link>
            </div>
            <div
              style={{
                flex: 1,
                marginLeft: "20px",
                textAlign: "center",
                backgroundColor: "#ffa200",
                color: "#613e01",
                borderRadius: "5px",
                boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.2)",
                transition: "transform 0.3s",
                transform: isHoveredPendingCurrent
                  ? "scale(1.1)"
                  : "scale(1.0)",
              }}
              onMouseEnter={() => setIsHoveredPendingCurrent(true)}
              onMouseLeave={() => setIsHoveredPendingCurrent(false)}
            >
              <Link
                style={{ color: "#613e01" }}
                to={`/${role}/WeeklyPlan`}
                state={{
                  teamName: state.data?.name,
                  status: "In Progress",
                  WeekEndingDateCurr: currentWeekEndingDate,
                }}
              >
                <div className="m-2" style={{ height: "40%" }}>
                  Pending Planned Items
                </div>
                <p style={{ marginTop: "-1px" }}>
                  {
                    weeklyPlanDashboard?.filter(
                      (x: any) =>
                        x.weekEndingDate.slice(0, 10) ===
                          WeekEndingDate().toISOString().slice(0, 10) &&
                        x.status === "In Progress"
                    ).length || 0
                  }
                </p>
              </Link>
            </div>
          </div>
        </div>

        <div
          className="time-div"
          style={{
            display: "flex",
            margin: "0 auto",
            marginTop: "2%",
            backgroundColor: "white",
            width: "80%",
            border: "1px solid lightgrey",
            boxShadow: "0px 0px 1px grey",
            borderRadius: "5px",
            alignItems: "center",
            padding: "2.4rem",
          }}
        >
          <div
            className="d-flex mx-5 h-100 w-50"
            style={{
              alignItems: "center",
              width: "100%",
              justifyContent: "flex-start",
            }}
          >
            <div
              className="mx-2"
              style={{
                flex: "1",
                padding: "2%",
                height: "100%",
                borderRadius: "5px",
                backgroundColor: "#1f72de",
                color: "white",
                boxShadow: "0px 0px 10px grey",
              }}
            >
              <p>Estimated Hours</p>
              <p className="text-center" style={{ marginTop: "-15px" }}>
                {weeklyPlanDashboard?.reduce(
                  (a: number, b: any) => a + b.estimatedHours,
                  0 
                ) || 0}
              </p>
            </div>
            <div
              style={{
                padding: "2%",
                height: "100%",
                borderRadius: "5px",
                backgroundColor: "#1f72de",
                color: "white",
                flex: "1",
                boxShadow: "0px 0px 10px grey",
              }}
            >
              <p>Actual Hours</p>
              <p className="text-center" style={{ marginTop: "-15px" }}>
                40
              </p>
            </div>
          </div>
          <FormControl className="d-flex mx-5 ">
            <select
              className="form-select mx-5 "
              placeholder="days"
              value={selectedOption}
              onChange={handleSelectChange}
            >
              <option value="" disabled>
                Select Days
              </option>
              <option value="Week">This Weekend</option>
              <option value="LastWeek">Last Weekend</option>
              <option value="NextWeek">Upcoming Weekend</option>
            </select>
          </FormControl>
        </div>

        <TableContainer className="container mt-4 border-3 w-100">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="center" className="bg-dark text-light">
                  <b>Sl.No</b>
                </TableCell>
                <TableCell align="center" className="bg-dark text-light">
                  <b>Planned Items</b>
                </TableCell>
                <TableCell align="center" className="bg-dark text-light">
                  <b>
                    Total no of<br></br>Task
                  </b>
                </TableCell>
                <TableCell align="center" className="bg-dark text-light">
                  <b>
                    Total no of<br></br>Task Assigned
                  </b>
                </TableCell>
                <TableCell align="center" className="bg-dark text-light">
                  <b>
                    Total no of <br></br>Task Un - assigned
                  </b>
                </TableCell>
                <TableCell align="center" className="bg-dark text-light">
                  <b>
                    Total no of <br></br>Task completed
                  </b>
                </TableCell>
                <TableCell align="center" className="bg-dark text-light">
                  <b>
                    Total no of <br></br>Task Pending
                  </b>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {selectedOption === "LastWeek"
                ? renderTableRows()?.map((row: any, index: number) => (
                    <TableRow key={row.slNo}>
                      <TableCell align="center" component="th" scope="row">
                        {index + 1}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title={row.notes}>
                          <span>{row.notes}</span>
                        </Tooltip>
                      </TableCell>
                      <TableCell align="center">
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            taskId: row.listOfTasks.map((task: any) => task.id),
                            WeekEndingDateCurr: previousWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>{row.listOfTasks.length}</span>
                        </Link>
                      </TableCell>
                      <TableCell align="center">
                        { role == 'Customer'? (
                           row.listOfTasks.filter(
                            (task: any) => task.status === "Assigned"
                          ).length && (
                            <span>
                              {
                                row.listOfTasks.filter(
                                  (task: any) => task.status === "Assigned"
                                ).length
                              }
                            </span>
                          )
                        ):(
                        <Link
                          to={"/Admin/Task"}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter((task: any) => task.status === "Assigned")
                              .map((task: any) => task.id),
                            status: "Assigned",
                            WeekEndingDateCurr: previousWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          {row.listOfTasks.filter(
                            (task: any) => task.status === "Assigned"
                          ).length && (
                            <span>
                              {
                                row.listOfTasks.filter(
                                  (task: any) => task.status === "Assigned"
                                ).length
                              }
                            </span>
                          )}
                        </Link>
                        )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        row.listOfTasks.filter(
                          (task: any) => task.status === "Unassigned"
                        ).length && (
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "Unassigned"
                              ).length
                            }
                          </span>
                        )
                      ):(
                        <Link
                          to={"/Admin/Task"}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "Unassigned"
                              )
                              .map((task: any) => task.id),
                            status: "Unassigned",
                            WeekEndingDateCurr: previousWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          {row.listOfTasks.filter(
                            (task: any) => task.status === "Unassigned"
                          ).length && (
                            <span>
                              {
                                row.listOfTasks.filter(
                                  (task: any) => task.status === "Unassigned"
                                ).length
                              }
                            </span>
                          )}
                        </Link>
                        )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                         <span>
                         {
                           row.listOfTasks.filter(
                             (task: any) => task.status === "Completed"
                           ).length
                         }
                       </span>
                      ):(
                        <Link
                          to={"/Admin/Task"}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "Completed"
                              )
                              .map((task: any) => task.id),
                            status: "Completed",
                            WeekEndingDateCurr: previousWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "Completed"
                              ).length
                            }
                          </span>
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        <span>
                        {
                          row.listOfTasks.filter(
                            (task: any) => task.status === "In Progress"
                          ).length
                        }
                      </span>
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "In Progress"
                              )
                              .map((task: any) => task.id),
                            status: "In Progress",
                            WeekEndingDateCurr: previousWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "In Progress"
                              ).length
                            }
                          </span>
                        </Link>
                      )}
                      </TableCell>
                    </TableRow>
                  ))
                : selectedOption === "NextWeek"
                ? renderTableRows()?.map((row: any, index: number) => (
                    <TableRow key={row.slNo}>
                      <TableCell align="center" component="th" scope="row">
                        {index + 1}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title={row.notes}>
                          <span>{row.notes}</span>
                        </Tooltip>
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        <span>{row.listOfTasks.length}</span>
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks.map((task: any) => task.id),
                            WeekEndingDateCurr: nextWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>{row.listOfTasks.length}</span>
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        row.listOfTasks.filter(
                          (task: any) => task.status === "Assigned"
                        ).length && (
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "Assigned"
                              ).length
                            }
                          </span>
                        )
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter((task: any) => task.status === "Assigned")
                              .map((task: any) => task.id),
                            status: "Assigned",
                            WeekEndingDateCurr: nextWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          {row.listOfTasks.filter(
                            (task: any) => task.status === "Assigned"
                          ).length && (
                            <span>
                              {
                                row.listOfTasks.filter(
                                  (task: any) => task.status === "Assigned"
                                ).length
                              }
                            </span>
                          )}
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        row.listOfTasks.filter(
                          (task: any) => task.status === "Unassigned"
                        ).length && (
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "Unassigned"
                              ).length
                            }
                          </span>
                        )
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "Unassigned"
                              )
                              .map((task: any) => task.id),
                            status: "Unassigned",
                            WeekEndingDateCurr: nextWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          {row.listOfTasks.filter(
                            (task: any) => task.status === "Unassigned"
                          ).length && (
                            <span>
                              {
                                row.listOfTasks.filter(
                                  (task: any) => task.status === "Unassigned"
                                ).length
                              }
                            </span>
                          )}
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        <span>
                        {
                          row.listOfTasks.filter(
                            (task: any) => task.status === "Completed"
                          ).length
                        }
                      </span>
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "Completed"
                              )
                              .map((task: any) => task.id),
                            status: "Completed",
                            WeekEndingDateCurr: nextWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "Completed"
                              ).length
                            }
                          </span>
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                         <span>
                         {
                           row.listOfTasks.filter(
                             (task: any) => task.status === "In Progress"
                           ).length
                         }
                       </span>
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "In Progress"
                              )
                              .map((task: any) => task.id),
                            status: "In Progress",
                            WeekEndingDateCurr: nextWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "In Progress"
                              ).length
                            }
                          </span>
                        </Link>
                      )}
                      </TableCell>
                    </TableRow>
                  ))
                : renderTableRows()?.map((row: any, index: number) => (
                    <TableRow key={row.slNo}>
                      <TableCell align="center" component="th" scope="row">
                        {index + 1}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title={row.notes}>
                          <span>{row.notes}</span>
                        </Tooltip>
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                         <span>{row.listOfTasks.length}</span>
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks.map((task: any) => task.id),
                            WeekEndingDateCurr: currentWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>{row.listOfTasks.length}</span>
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        row.listOfTasks.filter(
                          (task: any) => task.status === "Assigned"
                        ).length && (
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "Assigned"
                              ).length
                            }
                          </span>
                        )
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter((task: any) => task.status === "Assigned")
                              .map((task: any) => task.id),
                            status: "Assigned",
                            WeekEndingDateCurr: currentWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          {row.listOfTasks.filter(
                            (task: any) => task.status === "Assigned"
                          ).length && (
                            <span>
                              {
                                row.listOfTasks.filter(
                                  (task: any) => task.status === "Assigned"
                                ).length
                              }
                            </span>
                          )}
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        row.listOfTasks.filter(
                          (task: any) => task.status === "Unassigned"
                        ).length && (
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "Unassigned"
                              ).length
                            }
                          </span>
                        )
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "Unassigned"
                              )
                              .map((task: any) => task.id),
                            status: "Unassigned",
                            WeekEndingDateCurr: currentWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          {row.listOfTasks.filter(
                            (task: any) => task.status === "Unassigned"
                          ).length && (
                            <span>
                              {
                                row.listOfTasks.filter(
                                  (task: any) => task.status === "Unassigned"
                                ).length
                              }
                            </span>
                          )}
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        <span>
                        {
                          row.listOfTasks.filter(
                            (task: any) => task.status === "Completed"
                          ).length
                        }
                      </span>
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "Completed"
                              )
                              .map((task: any) => task.id),
                            status: "Completed",
                            WeekEndingDateCurr: currentWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "Completed"
                              ).length
                            }
                          </span>
                        </Link>
                      )}
                      </TableCell>
                      <TableCell align="center">
                      { role == 'Customer'? (
                        <span>
                        {
                          row.listOfTasks.filter(
                            (task: any) => task.status === "In Progress"
                          ).length
                        }
                      </span>
                      ):(
                        <Link
                          to={`/Admin/Task`}
                          state={{
                            teamName: state.data?.name,
                            taskId: row.listOfTasks
                              .filter(
                                (task: any) => task.status === "In Progress"
                              )
                              .map((task: any) => task.id),
                            status: "In Progress",
                            WeekEndingDateCurr: currentWeekEndingDate,
                          }}
                          style={{ textDecoration: "none", color: "inherit" }}
                        >
                          <span>
                            {
                              row.listOfTasks.filter(
                                (task: any) => task.status === "In Progress"
                              ).length
                            }
                          </span>
                        </Link>
                      )}
                      </TableCell>
                    </TableRow>
                  ))}
            </TableBody>
          </Table>
        </TableContainer>
        <BackDrop open={loading} />
      </div>
    </>
  );
};

export default WeeklyPlanDashboard;
