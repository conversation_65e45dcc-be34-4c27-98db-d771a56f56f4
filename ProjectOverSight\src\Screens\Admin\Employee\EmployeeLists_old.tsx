import {
  <PERSON>readcrumbs,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Ava<PERSON>,
  Box,
  Zoom,
  Badge,
  Stack,
  Pagination,
  Tooltip,
  Button,
} from "@mui/material";
import { Link, useNavigate } from "react-router-dom";
import { useContextProvider } from "../../../CommonComponents/Context";
import CardContent from "@mui/material/CardContent";
import SearchIcon from "@mui/icons-material/Search";
import { styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import { useEffect, useState } from "react";
import { Get } from "../../../Services/Axios";
import OpenInFullIcon from "@mui/icons-material/OpenInFull";
import BorderColorIcon from "@mui/icons-material/BorderColor";
import BackDrop from "../../../CommonComponents/BackDrop";
import { EditEmployee } from "./EmployeeEdit";
import PsychologyIcon from "@mui/icons-material/Psychology";
import DashboardIcon from "@mui/icons-material/Dashboard";
import { ViewEmp } from "./ViewEmployee";
import { Regex } from "../../../Constants/Regex/Regex";
import Select from "react-select";
import { departmentColor } from "../../../Constants/Colors";
import Refresh from "@mui/icons-material/Refresh";

const actions = [
  { icon: <BorderColorIcon sx={{ color: "orange" }} />, name: "Edit" },
  { icon: <PsychologyIcon sx={{ color: "green" }} />, name: "Assign Skill" },
  {
    icon: <DashboardIcon sx={{ color: "#ba000d" }} />,
    name: "Employee Report",
  },
  { icon: <OpenInFullIcon sx={{ color: "#0b6bb0" }} />, name: "View" },
];

const StyledBadge = styled(Badge)(({ theme }) => ({
  "& .MuiBadge-badge": {
    backgroundColor: "#44b700",
    color: "#44b700",
    boxShadow: `0 0 0 2px ${theme.palette.background.paper}`,
    "&::after": {
      position: "absolute",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      borderRadius: "50%",
      animation: "ripple 1.2s infinite ease-in-out",
      border: "1px solid currentColor",
      content: '""',
    },
  },
  "@keyframes ripple": {
    "0%": {
      transform: "scale(.8)",
      opacity: 1,
    },
    "100%": {
      transform: "scale(2.4)",
      opacity: 0,
    },
  },
}));

const Search = styled("div")(({ theme }) => ({
  position: "relative",
  borderRadius: theme.shape.borderRadius,
  width: "100%",
  background: "#13a4ba",
  [theme.breakpoints.up("sm")]: {
    marginLeft: theme.spacing(1),
    width: "auto",
  },
}));

const SearchIconWrapper = styled("div")(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: "100%",
  position: "absolute",
  pointerEvents: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: "inherit",
  "& .MuiInputBase-input": {
    padding: theme.spacing(1, 1, 1, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create("width"),
    width: "100%",
    [theme.breakpoints.up("sm")]: {
      width: "100%",
      "&:focus": {
        width: "20ch",
      },
    },
  },
}));

const EmployeeList = () => {
  const navigate = useNavigate();
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [rows, setRows] = useState<any>([]);
  const [employees, setEmployees] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [reload, setReload] = useState<boolean>(true);
  const [empView, setEmpView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });
  const { role } = useContextProvider();
  const [checked, setChecked] = useState(false);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  const [page, setPage] = useState(1);

  useEffect(() => {
    let employeeList = Get("app/Employee/GetEmployeeList");
    employeeList.then((response: any) => {
      setRows(response?.data || []);
      setEmployees(response?.data || []);
      setLoading(false);
      setChecked(true);
    });
  }, [reload]);

  let email: string[] = [];
  rows?.forEach((row: any) => {
    if (row.user?.email) {
      email.push(row.user?.email);
    }
  });

  const search = (reset = false) => {
    var filteredEmployees = reset
      ? rows
      : rows.filter((employee: any) => {
          const isMatch =
            employee.name.toLowerCase().includes(searchTerm) ||
            employee.department.toLowerCase().includes(searchTerm) ||
            employee.phoneNumber.includes(searchTerm) ||
            employee?.user?.email.toLowerCase().includes(searchTerm);
          if (isMatch) {
            return employee;
          }
        });
    setEmployees(filteredEmployees);
  };

  const handleChangePage = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
    return event;
  };

  const handleChangeRowsPerPage = (
    selectedOption: { value: number; label: string } | null
  ) => {
    if (selectedOption) {
      setRowsPerPage(selectedOption.value);
      setPage(1);
    }
  };

  const options = [
    { value: 8, label: "8" },
    { value: 10, label: "10" },
    { value: 20, label: "20" },
  ];

  const handleEditClick = (name: any, employee: any) => {
    switch (name) {
      case "Edit":
        setEmpView({ edit: true });
        setSelectedEmployee(employee);
        break;
      case "Assign Skill":
        navigate("/Admin/AssignSkill", {
          state: { data: employee.user },
        });
        break;
      case "Employee Report":
        navigate(`/${role}/EmployeeReport`, {
          state: {
            data: employee?.userId,
            Employeename: employee?.user.name,
            employeeId: employee?.id,
            Department: employee?.department,
          },
        });

        break;
      case "View":
        setEmpView({ view: true });
        setSelectedEmployee(employee);
        break;
      default:
        break;
    }
  };

  return (
    <>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link color="inherit" to={`/${role}`}>
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>Employee</Typography>
      </Breadcrumbs>
      <Grid
        container
        sx={{
          width: "95%",
          marginTop: "2%",
          marginLeft: "3%",
        }}
        item
      >
        <Grid
          item
          md={6}
          className="w-100 mx-auto mb-3 d-flex align-items-center"
        >
          <Search
            className="mx-auto w-75 border border-3 rounded-start-pill"
            sx={{
              backgroundColor: "#F6FBFF",
            }}
          >
            <SearchIconWrapper>
              <SearchIcon sx={{ color: "darkgrey" }} />
            </SearchIconWrapper>
            <StyledInputBase
              placeholder="Search for Employees"
              value={searchTerm}
              onChange={(e) =>
                setSearchTerm(e.target.value.trim().toLowerCase())
              }
            />
          </Search>
          <Button variant="contained" onClick={() => search()} className="mx-1">
            <Tooltip title="Search">
              <SearchIcon />
            </Tooltip>
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              setSearchTerm("");
              search(true);
            }}
          >
            <Tooltip title="Reset">
              <Refresh />
            </Tooltip>
          </Button>
        </Grid>
      </Grid>
      <div className="d-flex container mx-auto justify-content-between align-items-center">
        <p className="float-start">
          Total Employees: <b>{employees.length}</b>
        </p>
        <div
          style={{
            display: "flex",
            justifyContent: "end",
            alignItems: "center",
          }}
        >
          <p className="text-muted mt-2 mx-2">Rows per page : </p>
          <Select
            value={{ value: rowsPerPage, label: rowsPerPage.toString() }}
            options={options}
            onChange={handleChangeRowsPerPage}
            aria-label="Rows per page"
          />

          <Stack spacing={2} className="">
            <Pagination
              count={Math.ceil(employees?.length / rowsPerPage)}
              page={page}
              onChange={handleChangePage}
              className="float-end"
              color="primary"
            />
          </Stack>
        </div>
      </div>
      <Grid
        container
        item
        className="col-s-7"
        sx={{
          ml: {
            xs: 8,
            sm: 7,
            md: 4,
          },
          pl: {
            sm: 10,
            md: 1,
          },
          overflowY: "scroll",
          height: {
            xs: "60%",
            sm: "60%",
            md: "70%",
          },
          width: {
            xs: "70%",
            sm: "90%",
            md: "95%",
          },
        }}
      >
        {!loading &&
          employees
            .slice((page - 1) * rowsPerPage, page * rowsPerPage)
            .map((employee: any, index: any) => {
              return (
                <Grid
                  container
                  item
                  md={3}
                  sm={5}
                  xs={12}
                  key={employee.name}
                  sx={{ transition: "max-width 0.3s ease" }}
                >
                  <Box
                    sx={{
                      display: "inline-flex",
                      m: { xs: "4%", sm: "4%", md: "4%" },
                      width: { xs: "80%", sm: "90%", md: "100%" },
                      height: { xs: "60%", sm: "80%", md: "60%" },
                      ml: { xs: "18%" },
                    }}
                  >
                    <Zoom
                      in={checked}
                      style={{ transformOrigin: "0 0 0" }}
                      {...(checked ? { timeout: 200 } : {})}
                      key={index}
                    >
                      <Grid container item>
                        <div
                          style={{
                            borderRadius: "10px",
                            width: "90%",
                            margin: "auto",
                          }}
                          className="bg-light border border-1"
                        >
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              flexGrow: 1,
                            }}
                          >
                            <div style={{}}>
                              <Grid container item>
                                <StyledBadge
                                  sx={{
                                    height: ["70%", "100%"],
                                    width: "13%",
                                    ml: "44%",
                                    mt: "5%",
                                  }}
                                  overlap="circular"
                                  anchorOrigin={{
                                    vertical: "bottom",
                                    horizontal: "right",
                                  }}
                                  variant="dot"
                                >
                                  <Avatar
                                    alt="Remy Sharp"
                                    src="/src/assets/man.png"
                                  />
                                </StyledBadge>
                                <Grid xs={12} item>
                                  <CardContent sx={{ textAlign: "center" }}>
                                    <Typography
                                      gutterBottom
                                      variant="h1"
                                      component="div"
                                      sx={{
                                        fontSize: ["110%", "125%"],
                                        fontWeight: "bold",
                                      }}
                                    >
                                      <Link
                                        to={`/${role}/EmployeeOverView`}
                                        className="tableStyle"
                                        state={{
                                          employeeId: employee.id,
                                          employeeName: employee.user.name,
                                          route: "employee",
                                        }}
                                      >
                                        <span
                                          style={{
                                            color: "#0c0d0f",
                                            fontSize: "120%",
                                            fontWeight: "bold",
                                          }}
                                        >
                                          {employee?.name
                                            ? employee.name.replace(
                                                Regex.CHAR_SPACE,
                                                " "
                                              )
                                            : ""}
                                        </span>
                                      </Link>
                                    </Typography>
                                    <Typography component="div">
                                      <span
                                        style={{
                                          color: `${
                                            departmentColor[
                                              employee?.department?.toLowerCase()
                                            ]?.color
                                          }`,
                                          backgroundColor: `${
                                            departmentColor[
                                              employee?.department?.toLowerCase()
                                            ]?.background
                                          }`,
                                          borderRadius: "5px",
                                          fontWeight: "lighter",
                                          display: "inline-block",
                                          marginTop: "10px",
                                        }}
                                      >
                                        {employee?.department}
                                      </span>
                                    </Typography>

                                    <Typography
                                      className="mt-3"
                                      component="div"
                                    >
                                      {employee?.phoneNumber}
                                    </Typography>

                                    <Typography
                                      className="row-email mt-2"
                                      color="inherit"
                                      component="div"
                                      sx={{
                                        fontSize: ["0.8em", "0.9em", "0.9em"],
                                      }}
                                    >
                                      <span style={{ color: "#b87209" }}>
                                        {employee?.user?.email}
                                      </span>
                                    </Typography>
                                    <Typography
                                      className="mt-2"
                                      component="div"
                                      sx={{
                                        m: [1, 0.5],
                                        fontSize: ["85%", "95%"],
                                      }}
                                    >
                                      <span style={{ color: "GrayText" }}>
                                        {employee.userId}
                                      </span>
                                    </Typography>
                                  </CardContent>
                                  <Box className="w-100 d-flex justify-content-evenly">
                                    {actions.map((action) => (
                                      <span
                                        key={action.name}
                                        className="m-2 rounded-circle p-2"
                                        style={{
                                          backgroundColor: "#d4e2fa",
                                        }}
                                        onClick={() =>
                                          handleEditClick(action.name, employee)
                                        }
                                      >
                                        <Tooltip title={action.name}>
                                          {action.icon}
                                        </Tooltip>
                                      </span>
                                    ))}
                                  </Box>
                                </Grid>
                              </Grid>
                            </div>
                          </Box>
                        </div>
                      </Grid>
                    </Zoom>
                  </Box>
                </Grid>
              );

              return null;
            })}
      </Grid>
      <BackDrop open={loading} />
      {selectedEmployee && (
        <EditEmployee
          editEmployee={empView}
          setEditEmployee={setEmpView}
          setReload={setReload}
          emailList={email.filter((e) => e !== selectedEmployee?.user?.email)}
          data={selectedEmployee}
        />
      )}
      {empView.view && (
        <ViewEmp
          viewEmployee={empView}
          setviewEmployee={setEmpView}
          data={selectedEmployee}
        />
      )}
    </>
  );
};
export default EmployeeList;
