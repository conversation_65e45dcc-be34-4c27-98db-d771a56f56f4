import {
  Alert,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextareaAutosize,
  Autocomplete,
  Grid,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import { Get, Post, PostFiles } from "../../../../Services/Axios";
import { useQuery } from "react-query";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import { Document } from "../../../../Models/Project/UserStory";
import { EditFileUpload } from "../../../../CommonComponents/EditFileUpload";
import { Regex } from "../../../../Constants/Regex/Regex";
import { AddProjectObjective } from "../ProjectObjective/AddProjectObjective";
import AddIcon from "@mui/icons-material/Add";

const formField = [
  "Name",
  "Description",
  "StartDate",
  "EndDate",
  "Status",
  "Percentage",
  "Complexity",
  "CreatedBy",
  "UpdatedBy",
  "ProjectId",
  "id",
  "UICategory",
];

export const EditUserInterface = ({
  openDialog,
  setOpenDialog,
  projectId,
  Data,
  data1,
  SetReload,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [CategoryUI, setCategoryUI] = useState([]);
  const [save, setSave] = useState<boolean>(false);
  const [open, setOpen] = useState<any>({ add: false });
  const [uploadedFiles, setUploadedFiles] = useState<any>([]);
  const [selectedFiles, setSelectedFiles] = useState<any>([]);
  const [deleteFileIds, setDeleteFileIds] = useState<Array<number>>([]);
  const [projectobjective, setprojectobjective] = useState<any>([]);
  const [Eachprojectobjectives, setEachprojectobjectives] = useState<any>([]);
  const [IDs, setId] = useState<any>([]);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });

  async function fetchCommonMaster() {
    const commonMaster: any = await Get("app/CommonMaster/GetCodeTableList");
    return commonMaster.data;
  }

    var objective: any[] = [];
  data1?.data?.projectObjectiveMappings?.forEach((e: any) => {
      objective.push({ label: e.description, id: e.projectObjectiveId });
  });


  const { data } = useQuery("master_UI1", fetchCommonMaster);

  const onSubmitHandler = async (data: any) => {
    debugger
    setSave(true);
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }
    data.status = Data.status;
    // data.ProjectObjectiveId = data;
    console.log(IDs);
    data.userInterface = data.Name;
    const { error }: any = await Post("app/Project/UpdateUserInterface", data);

    if (!error && selectedFiles.length > 0) {
      selectedFiles.forEach(async (file: any) => {
        var document: Document = {
          TableName: "UserInterface",
          AttributeId: data.id,
          ProjectId: Data?.projectId,
          DocType: data.DocType,
          FileName: file.name,
          FileType: file.type,
          File: file,
          IsActive: true,
          CreatedBy: "user",
          UpdatedBy: "user",
        };
        await PostFiles("app/Project/UploadFiles", document);
      });
    }

    if (deleteFileIds.length > 0) {
      deleteFileIds.forEach((_id: number) => {
        Post(`app/Project/DeleteFile?id=${_id}`, "");
      });
    }

    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "User Interface Updated Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      SetReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  useEffect(() => {
    setUploadedFiles(Data?.documents ?? []);
    getobjectives();
  }, [projectId,Data,open, openDialog?.edit]);

  async function getobjectives() {
   
    try {
      const objectiveResponse: any = await Get(
        `app/Project/GetProjectObjective?ProjectId=${Data?.projectId}`
      );

      const objectiveData = objectiveResponse?.data?.projectObjectives || [];
      setprojectobjective(objectiveData);
    
      let ObjId: any[] = [];
      if (Data?.projectObjectiveId) {
        if (Array.isArray(Data.projectObjectiveId)) {
          ObjId = Data.projectObjectiveId; 
        } else if (typeof Data?.projectObjectiveId === 'string') {
          ObjId = Data.projectObjectiveId.split("|"); 
        } else if (typeof Data?.projectObjectiveId === 'number') {
          ObjId = [Data.projectObjectiveId]; 
        }
      }
    
      const eachObjectiveResults = objectiveData.filter((objective: any) => 
        ObjId.includes(objective?.id)
      ) || [];
    
      setEachprojectobjectives(eachObjectiveResults);
    } catch (error) {
      console.error("Error fetching project objectives:", error);
    }
  }
  
  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setOpenDialog({ edit: false });
    setSave(false);
    setSelectedFiles([]);
    setDeleteFileIds([]);
  };

  return (
    <div>
      <Dialog open={openDialog?.edit}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Edit User Interface
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
             <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                defaultValue={Data?.name}
                {...register("Name")}
                onChange={(e: any) => {
                  e.target.value = e.target.value.replace(Regex.CHAR_SPACE, "");
                }}
                label="User Interface Name"
                type="text"
                variant="outlined"
              />
               </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <TextField
                  id="outlined-read-only-input"
                  label="Status"
                  disabled
                  defaultValue={Data?.status}
                  InputProps={{
                    readOnly: true,
                  }}
                  {...register("Status")}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} >
            <InputLabel id="Description">Description</InputLabel>
            <FormControl fullWidth>
                <TextareaAutosize
                  required
                  defaultValue={Data?.description}
                  placeholder="Description"
                  {...register("Description")}
                  onChange={(e: any) => {
                    e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                  }}
                  style={{ height: 100 }}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <InputLabel id="start-date">Start date</InputLabel>
            <FormControl fullWidth>
                <TextField
                  required
                  id="start-date"
                  defaultValue={Data?.startDate?.slice(0, 10)}
                  margin="dense"
                  {...register("StartDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <InputLabel id="end-date">End date</InputLabel>
            <FormControl fullWidth>
                <TextField
                  required
                  id="end-date"
                  defaultValue={Data?.endDate?.slice(0, 10)}
                  margin="dense"
                  {...register("EndDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
            <InputLabel id="UI-Category">UI Category</InputLabel>
                <Select
                  labelId="UI-Category"
                  id="uiCategory"
                  required
                  defaultValue={Data?.uiCategory?.trim()}
                  label="UI Category"
                  {...register("UICategory", {
                    onChange: (e: any) => {
                      setCategoryUI(e.target.value);
                    },
                  })}
                >
                  {data?.map((e: any) => {
                    if (e.codeType == "UICategory")
                      return (
                        <MenuItem value={e.codeName.trim()} key={e.codeValue}>
                          {e.codeName}
                        </MenuItem>
                      );
                  })}
                </Select>
                </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <InputLabel id="Complexity">Complexity</InputLabel>
                <Select
                  required
                  labelId="Complexity"
                  defaultValue={Data?.complexity}
                  margin="dense"
                  id="Complexity"
                  label="Complexity"
                  {...register("Complexity")}
                >
                  <MenuItem value="Low">Low</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="High">High</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <EditFileUpload
                  uploadedFiles={uploadedFiles}
                  setUploadedFiles={setUploadedFiles}
                  selectedFiles={selectedFiles}
                  setSelectedFiles={setSelectedFiles}
                  setDeleteFileIds={setDeleteFileIds}
                  deleteFileIds={deleteFileIds}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                <InputLabel id="Status">Document Type</InputLabel>
                <Select
                  labelId="Document Type"
                  id="DocumentType"
                  label="Document Type"
                  defaultValue={Data?.documents[0]?.docType}
                  required={uploadedFiles.length > 0}
                  {...register("DocType")}
                >
                  <MenuItem value="Input">Input</MenuItem>
                  <MenuItem value="Process">Process</MenuItem>
                  <MenuItem value="Output">Output</MenuItem>
                  <MenuItem value="Sample Code">Sample Code</MenuItem>
                </Select>
                </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            {Eachprojectobjectives?.length > 0 ? 
            (<InputLabel id="ProjectObjectiv">Project Objectives</InputLabel>)
            : (<></>)}
            <FormControl fullWidth>
                <Autocomplete
                  multiple
                  defaultValue={Eachprojectobjectives?.map((result:any) => result?.description)}
                  options={projectobjective?.map((objective: any) => ({
                    label: objective.description,
                    id: objective.id,
                  }))}
                  sx={{ maxHeight: '150px', overflowY: 'auto' }}
                  {...register("ProjectObjectiveIds")}
                  onChange={(e: any, value: any) => {
                    if (value) var ids = [];
                    value?.map((e: any) => {
                      ids?.push(e.id);
                    });
                    setId(ids);
                    return e;
                  }}
                  renderInput={(params: any) => (
                    <TextField
                      {...params}
                      label={Eachprojectobjectives?.length > 0 ? '': 'Select Objectives'}
                      variant="outlined"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <Button
                variant="contained"
                onClick={() => setOpen({ add: true })}
              >
                Add Project Objective
                <AddIcon className="mx-1" />
              </Button>
              {/* <FormControl className="col m-2">
                <InputLabel id="Status">Project Objective</InputLabel>
                <Select
                  labelId="Project-Objective"
                  multiple
                  required
                  defaultValue={objective}
                  id="Status"
                  {...register("ProjectObjectiveIds")}
                  label="Project Objective"
                >
                  {data?.data?.projectObjectives?.map(
                    (e: any, index: number) => {
                      return (
                        <MenuItem value={e.id} key={index}>
                          {e.description}
                        </MenuItem>
                      );
                    }
                  )}
                </Select>
              </FormControl> */}
           </FormControl>
            </Grid>
          </Grid>
            <div className="row">
              <input {...register("CreatedBy")} value="user" hidden />
              <input {...register("UpdatedBy")} value="user" hidden />
              <input {...register("ProjectId")} value={projectId} hidden />
              <input {...register("id")} value={Data?.id} hidden />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
              type="submit"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              color="success"
              disabled={save}
              type="submit"
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <AddProjectObjective
        openDialog={open}
        setOpenDialog={setOpen}
        ProjectId={projectId}
        SetReload={SetReload}
      />
      <input type="text" value={CategoryUI} hidden />
    </div>
  );
};
