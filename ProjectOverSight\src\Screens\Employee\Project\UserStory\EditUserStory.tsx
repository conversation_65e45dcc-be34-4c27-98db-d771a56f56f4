import {
  <PERSON>ert,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Grid,
  Autocomplete,
  TextareaAutosize,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import { Post, PostFiles, Get } from "../../../../Services/Axios";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import { EditFileUpload } from "../../../../CommonComponents/EditFileUpload";
import { Document } from "../../../../Models/Project/UserStory";
import { Regex } from "../../../../Constants/Regex/Regex";
import AddIcon from "@mui/icons-material/Add";
import { AddProjectObjective } from "../../../Admin/Projects/ProjectObjective/AddProjectObjective";

const formField = [
  "Name",
  "Description",
  "StartDate",
  "EndDate",
  "Status",
  "Percentage",
  "CreatedBy",
  "UpdatedBy",
  "ProjectId",
  "id",
];

export const EditUserStory = ({
  openDialog,
  setOpenDialog,
  projectId,
  setReload,
  Data,
  data,
}: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [save, setSave] = useState<boolean>(false);
  const [open, setOpen] = useState<any>({ add: false });
  const [uploadedFiles, setUploadedFiles] = useState<any>([]);
  const [selectedFiles, setSelectedFiles] = useState<any>([]);
  const [projectobjective, setprojectobjective] = useState<any>([]);
  const [IDs, setId] = useState<any>([]);
  const [deleteFileIds, setDeleteFileIds] = useState<Array<number>>([]);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });

  useEffect(() => {
    setUploadedFiles(Data?.documents ?? []);
  }, [openDialog?.edit]);

  useEffect(() => {
    async function getobjectives() {
      debugger
      let objective: any = await Get(
        `app/Project/GetProjectObjective?ProjectId=${projectId}`
      );
      setprojectobjective(objective?.data?.projectObjectives || []);
    }
    getobjectives();
  }, [projectId, open, openDialog?.edit]);

  var objective: any[] = [];
 
  data?.data?.projectObjectiveMappings?.forEach((e: any) => {
    debugger
    if (e.userStoryId === Data?.id) objective.push(e.description);
  });

  console.log( objective)

  const onSubmitHandler = async (data: any) => {
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }
    data.status = Data.status;
    data.ProjectObjectiveIds = IDs;
    setSave(true);
    if (typeof data.ProjectObjectiveIds === "string") {
      data.ProjectObjectiveIds = data.ProjectObjectiveIds.split(",");
    }
    const { error }: any = await Post("app/Project/UpdateUserStory", data);
    if (!error && selectedFiles.length > 0) {
      selectedFiles.forEach(async (file: any) => {
        var document: Document = {
          TableName: "UserStory",
          AttributeId: data.id,
          ProjectId: data.ProjectId,
          DocType: data.DocType,
          FileName: file.name,
          FileType: file.type,
          File: file,
          IsActive: true,
          CreatedBy: "user",
          UpdatedBy: "user",
        };
        await PostFiles("app/Project/UploadFiles", document);
      });
    }

    if (deleteFileIds.length > 0) {
      deleteFileIds.forEach((_id: number) => {
        Post(`app/Project/DeleteFile?id=${_id}`, "");
      });
    }

    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Updating!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "User Story Created Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ edit: false });
    setSelectedFiles([]);
    setDeleteFileIds([]);
  };

  return (
    <div>
      <Dialog open={openDialog?.edit}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Edit User Story
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    required
                    defaultValue={Data?.name}
                    {...register("Name")}
                    onChange={(e: any) => {
                      e.target.value = e.target.value.replace(Regex.EMAIL_1, "");
                    }}
                    label="User Story Name"
                    type="text"
                    variant="outlined"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <TextField
                    id="outlined-read-only-input"
                    label="Status"
                    disabled
                    defaultValue={Data?.status}
                    InputProps={{
                      readOnly: true,
                    }}
                    {...register("Status")}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <InputLabel id="Description">Description</InputLabel>
                <FormControl fullWidth>
                  <TextareaAutosize
                    required
                    defaultValue={Data?.description}
                    placeholder="Description"
                    {...register("Description")}
                    onChange={(e: any) => {
                      e.target.value = e.target.value.replace(Regex.CHAR_NUM, "");
                    }}
                    style={{ height: 100 }}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Start date</InputLabel>
                <FormControl fullWidth>
                  <TextField
                    required
                    id="start-date"
                    defaultValue={Data?.startDate?.slice(0, 10)}
                    margin="dense"
                    {...register("StartDate")}
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">End date</InputLabel>
                <FormControl fullWidth>
                  <TextField
                    required
                    id="end-date"
                    defaultValue={Data?.endDate?.slice(0, 10)}
                    margin="dense"
                    {...register("EndDate")}
                    label=""
                    type="date"
                    fullWidth
                    variant="outlined"
                  />
                  <input {...register("CreatedBy")} value="user" hidden />
                  <input {...register("UpdatedBy")} value="user" hidden />
                  <input {...register("Percentage")} value="0" hidden />
                  <input {...register("ProjectId")} value={projectId} hidden />
                  <input {...register("id")} value={Data?.id} hidden />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <EditFileUpload
                    uploadedFiles={uploadedFiles}
                    setUploadedFiles={setUploadedFiles}
                    selectedFiles={selectedFiles}
                    setSelectedFiles={setSelectedFiles}
                    setDeleteFileIds={setDeleteFileIds}
                    deleteFileIds={deleteFileIds}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="Status">Document Type</InputLabel>
                  <Select
                    labelId="Document Type"
                    id="DocumentType"
                    label="Document Type"
                    defaultValue={Data?.documents[0]?.docType}
                    required={uploadedFiles?.length > 0}
                    {...register("DocType")}
                  >
                    <MenuItem value="Input">Input</MenuItem>
                    <MenuItem value="Process">Process</MenuItem>
                    <MenuItem value="Output">Output</MenuItem>
                    <MenuItem value="Sample Code">Sample Code</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                {objective?.map((e: any) => e?.label) && <InputLabel className="mx-3" id="projectobjective">Project Objectives</InputLabel>}
                <div
                  className="mt-2"
                  style={{ maxHeight: "100px", overflowY: "auto" }}
                >
                  <Autocomplete
                    multiple
                    defaultValue={objective?.length ? objective.map((e: any) => {
                            if (e) {
                              return e;
                            } else {
                              return "";
                            }
                          })
                        : []
                    }
                    options={
                      Array.isArray(projectobjective)
                        ? projectobjective.map((objective: any) => ({
                          label: objective?.description || 'No objectives',  
                          id: objective?.id,
                        }))
                        : []
                    }
                    {...register("ProjectObjectiveIds")}
                    onChange={(e: any, value: any) => {
                      if (value) {
                        const ids = value.map((e: any) => e?.id).filter((id: any) => id !== undefined);
                        setId(ids);
                      }
                      return e;
                    }}
                    renderInput={(params: any) => (
                      <TextField
                        {...params}
                        label={objective?.length > 0 ? "" : "Select Objectives"}
                        variant="outlined"
                        style={{ height: "50%" }}
                      />
                    )}
                  />

                </div>
              </Grid>
              <Grid item xs={12} md={6}>
                <Button
                  variant="contained"
                  className="mt-2 m-2 col"
                  onClick={() => setOpen({ add: true })}
                >
                  Add Project Objective
                  <AddIcon className="mx-1" />
                </Button>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              color="success"
              type="submit"
              disabled={save}
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <AddProjectObjective
        openDialog={open}
        setOpenDialog={setOpen}
        ProjectId={projectId}
        SetReload={setReload}
      />
    </div>
  );
};
