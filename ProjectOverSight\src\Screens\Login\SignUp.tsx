import {
  Button,
  FormControl,
  InputLabel,
  TextField,
  OutlinedInput,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Post } from "../../Services/Axios";
import { VERSION } from "../../Constants/Common/Versions";
import { BASE_URL } from "../../Constants/Common/Urls";
import {
  CONFLICT,
  NOTFOUND,
  SUCCESS,
} from "../../Constants/Common/StatusCodes";
import { ADMIN, CUSTOMER, EMPLOYEE } from "../../Constants/Common/Roles";
import { ForgotPassword } from "./ForgotPassword";
import { UserLoginDto } from "../../Models/Employee/User";
import { SessionUser } from "../../Models/Employee/Employee";
import { useContextProvider } from "../../CommonComponents/Context";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { Regex } from "../../Constants/Regex/Regex";

export const SignUp = ({
  setLoader,
  setemailError,
  setpasswordError,
  seterrorMsg,
  emailError,
  passwordError,
  setError,
  loader,
}: any) => {
  const [open, setopen] = useState(false);
  const [user, setuser] = useState<UserLoginDto>({
    email: "",
    password: "",
    versionCode: VERSION,
  });
  const json: any = sessionStorage.getItem("user") || null;
  const sessionUser: SessionUser = JSON.parse(json);
  const navigate = useNavigate();
  const emailRef = useRef<HTMLInputElement>();
  const passwordRef = useRef<HTMLInputElement>();
  const { setContext } = useContextProvider();
  const [showPassword, setShowPassword] = useState(false);

  const handleSignInClick = async () => {
    if (user.email == "" || user.password == "") {
      user.email == ""
        ? emailRef.current?.focus()
        : user.password == ""
        ? passwordRef.current?.focus()
        : undefined;
      return;
    }
    setLoader(true);
    setError(false);
    debugger
    let response: any = await Post(`${BASE_URL}Auth/Login`, user);
    if (response.response) response = response.response;

    switch (response.response?.status || response.status) {
      case SUCCESS:
        sessionStorage.setItem("user", JSON.stringify(response.data));
        setContext(response.data);
        Redirect(response.data.userRoles);
        break;
      case NOTFOUND:
        setemailError(true);
        setLoader(false);
        seterrorMsg(response.response.data);
        break;
      case CONFLICT:
        setpasswordError(true);
        setLoader(false);
        seterrorMsg(response.response.data);
        break;
      default:
        setLoader(false);
        setError(true);
        seterrorMsg(response?.message);
    }
  };

  async function Redirect(role: string) {
    switch (role) {
      case EMPLOYEE:
        navigate("/Employee");
        break;
      case ADMIN:
        navigate("/Admin");
        break;
      case CUSTOMER:
        navigate("/Customer");
        break;
      default:
        navigate("/Login");
    }
    // sessionStorage.setItem("isLoggedIn", "Logout");
  }

  useEffect(() => {
    if (sessionUser == null) {
      // sessionStorage.setItem("isLoggedIn", "Login");
    } else {
      // sessionStorage.setItem("isLoggedIn", "Logout");
      Redirect(sessionUser.userRoles);
    }
  }, []);

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    event.preventDefault();
  };

  return (
    <>
      <TextField
        margin="normal"
        required
        fullWidth
        inputRef={emailRef}
        id="email"
        label="Email"
        name="email"
        error={emailError}
        autoComplete="email"
        onChange={(e) => {
          e.target.value = e.target.value.replace(Regex.EMAIL_1, "");
          setemailError(false);
          setuser({ ...user, email: e.target.value });
        }}
        autoFocus
      />
      <FormControl sx={{ width: "25rem" }} variant="outlined">
        <InputLabel htmlFor="outlined-adornment-password">Password*</InputLabel>
        <OutlinedInput
          id="outlined-adornment-password"
          autoComplete="new-password"
          error={passwordError}
          fullWidth
          inputRef={passwordRef}
          required
          type={showPassword ? "text" : "password"}
          onChange={(e) => {
            setuser({ ...user, password: e.target.value });
            setpasswordError(false);
          }}
          endAdornment={
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle password visibility"
                onClick={handleClickShowPassword}
                onMouseDown={handleMouseDownPassword}
                edge="end"
              >
                {!showPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </InputAdornment>
          }
          label="Password"
        />
      </FormControl>
      <Button
        variant="contained"
        fullWidth
        color="primary"
        disabled={loader}
        sx={{ mt: 3, mb: 2 }}
        onClick={() => handleSignInClick()}
      >
        {loader ? "Signing In..." : "Sign In"}
      </Button>
      <Button
        variant="text"
        fullWidth
        disabled={loader}
        color="primary"
        sx={{ mt: 1 }}
        onClick={() => setopen(true)}
      >
        Forgot Password?
      </Button>
      <ForgotPassword open={open} setOpen={setopen} />
    </>
  );
};
