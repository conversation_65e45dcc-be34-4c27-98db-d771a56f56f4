import * as React from "react";
import dayjs, { Dayjs } from "dayjs";
import isBetweenPlugin from "dayjs/plugin/isBetween";
import { styled } from "@mui/material/styles";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import { PickersDay, PickersDayProps } from "@mui/x-date-pickers/PickersDay";
import { Typography, Breadcrumbs, Grid, Button } from "@mui/material";
import { Link } from "react-router-dom";
import { useContextProvider } from "../../../CommonComponents/Context";
// import { useRef } from "react";
import "./Drag.css";

import { useState, useEffect } from "react";

interface Task {
  id: number;
  name: string;
  image?: any; // Make it optional by adding '?'
  status: string;
  description: string;
  time: string;
  days: string;
}

const tasks: Task[] = [
  {
    id: 1,
    name: "Project 1",
    status: "Assigned",
    // image: IconBox, // Add the image property with IconBox
    description: "Description",
    time: "8 hrs",
    days: "5 days left",
  },
  {
    id: 2,
    name: "Project 2",
    status: "In Progress",
    description: "Description",
    time: "6 hrs",
    days: "6 days left",
  },
  {
    id: 3,
    name: "Project 1",
    status: "Completed",
    description: "Description",
    time: "13 hrs",
    days: "4 days left",
  },
  {
    id: 4,
    name: "Project 2",
    status: "Assigned",
    description: "Description",
    time: "22 hrs",
    days: "2 days left",
  },
  {
    id: 5,
    name: "Project 5",
    status: "In Progress",
    description: "Description",
    time: "2 hrs",
    days: "1 day left",
  },
  {
    id: 6,
    name: "Project 6",
    status: "Completed",
    description: "Description",
    time: "20 hrs",
    days: "11 days left",
  },
  {
    id: 7,
    name: "Project 7",
    status: "Delivered",
    description: "Description",
    time: "2 hrs",
    days: "12-Jan",
  },
];

dayjs.extend(isBetweenPlugin);

interface CustomPickerDayProps extends PickersDayProps<Dayjs> {
  isSelected: boolean;
  isHovered: boolean;
}

const CustomPickersDay = styled(PickersDay, {
  shouldForwardProp: (prop) => prop !== "isSelected" && prop !== "isHovered",
})<CustomPickerDayProps>(({ theme, isSelected, isHovered, day }) => ({
  borderRadius: 0,
  ...(isSelected &&
    day.day() >= 1 &&
    day.day() <= 5 && {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
      "&:hover, &:focus": {
        backgroundColor: theme.palette.primary.main,
      },
    }),
  // ...(isHovered && {
  //   backgroundColor: theme.palette.primary[theme.palette.mode],
  //   '&:hover, &:focus': {
  //     backgroundColor: theme.palette.primary[theme.palette.mode],
  //   },
  // }),
  ...(day.day() >= 1 &&
    day.day() <= 5 &&
    isHovered && {
      // Apply specific styles for days 1 to 5
      backgroundColor: theme.palette.primary[theme.palette.mode], // Set your desired background color
      "&:hover, &:focus": {
        backgroundColor: theme.palette.primary[theme.palette.mode], // Set your desired hover background color
      },
    }),
  ...(day.day() === 1 && {
    borderTopLeftRadius: "50%",
    borderBottomLeftRadius: "50%",
  }),
  ...(day.day() === 5 && {
    borderTopRightRadius: "50%",
    borderBottomRightRadius: "50%",
  }),
})) as React.ComponentType<CustomPickerDayProps>;

const isInSameWeek = (dayA: Dayjs, dayB: Dayjs | null | undefined) => {
  if (dayB == null) {
    return false;
  }

  return dayA.isSame(dayB, "week");
};

function Day(
  props: PickersDayProps<Dayjs> & {
    selectedDay?: Dayjs | null;
    hoveredDay?: Dayjs | null;
  }
) {
  const { day, selectedDay, hoveredDay, ...other } = props;

  return (
    <CustomPickersDay
      {...other}
      day={day}
      sx={{ px: 2.5 }}
      disableMargin
      selected={false}
      isSelected={isInSameWeek(day, selectedDay)}
      isHovered={isInSameWeek(day, hoveredDay)}
    />
  );
}

export const Weeklyplan = () => {
  // const constraintsRef = useRef(null);
  const { role } = useContextProvider();
  const [hoveredDay, setHoveredDay] = React.useState<Dayjs | null>(null);
  const [value, setValue] = React.useState<Dayjs | null>(dayjs());
  const [taskList, setTaskList] = useState<Task[]>(tasks);

  useEffect(() => {
    setTaskList(tasks);
  }, [tasks]);

  const onDragStart = (evt: React.DragEvent<HTMLDivElement>) => {
    let element = evt.currentTarget as HTMLDivElement;
    element.classList.add("dragged");
    evt.dataTransfer.setData("text/plain", evt.currentTarget.id);
    evt.dataTransfer.effectAllowed = "move";
  };

  const onDragEnd = (evt: React.DragEvent<HTMLDivElement>) => {
    let element = evt.currentTarget as HTMLDivElement;
    element.classList.remove("dragged");
    element.classList.remove("dragged-over"); // Ensure the dragged-over class is also removed
  };

  const onDragEnter = (evt: React.DragEvent<HTMLDivElement>) => {
    evt.preventDefault();
    let element = evt.currentTarget;
    element.classList.add("dragged-over");
    evt.dataTransfer.dropEffect = "move";
  };

  const onDragLeave = (evt: React.DragEvent<HTMLDivElement>) => {
    let currentTarget = evt.currentTarget;
    let newTarget = evt.relatedTarget as Node;
    if (newTarget.parentNode === currentTarget || newTarget === currentTarget)
      return;
    evt.preventDefault();
    let element = evt.currentTarget;
    element.classList.remove("dragged-over");
  };

  const onDragOver = (evt: React.DragEvent<HTMLDivElement>) => {
    evt.preventDefault();
    evt.dataTransfer.dropEffect = "move";
  };

  const onDrop = (
    evt: React.DragEvent<HTMLDivElement>,
    value: boolean,
    status: string
  ) => {
    evt.preventDefault();
    evt.currentTarget.classList.remove("dragged-over");
    let data = evt.dataTransfer.getData("text/plain");
    let updated = taskList.map((task) => {
      if (task.id.toString() === data.toString()) {
        task.status = status;
      }
      return task;
    });
    setTaskList(updated);
    return value;
  };

  const renderTaskCards = (taskList: Task[], status: string) => {
    return taskList
      .filter((data) => data.status === status)
      .map((task) => (
        <div
          className="plan mt-2"
          style={{
            cursor: "pointer",
            border: "2px solid #cfdae3",
            marginLeft: "13px",
          }}
          key={task.id.toString()}
          id={task.id.toString()}
          draggable
          onDragStart={(e) => onDragStart(e)}
          onDragEnd={(e) => onDragEnd(e)}
        >
          <div>
            <div className="innerCard">
              <div>
                <p className="title">{task.name}</p>
              </div>
              <ul className="features">
                <div>{task.description}</div>
              </ul>
              <div className="cardlist">
                <div className="statuss">{task.status}</div>
                <div>{task.time}</div>
                <div>{task.days}</div>
              </div>
            </div>
          </div>
        </div>
      ));
  };

  return (
    <>
      <Grid container sx={{ display: "inline-flex" }}>
        <Grid xs={3}>
          <Breadcrumbs className="mt-3 mx-3 " separator=">">
            <Link color="inherit" to={`/${role}`}>
              <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
            </Link>
            <Typography sx={{ fontWeight: "bold" }}>Weeklyplan</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid xs={7}>
          <Typography
            className="mt-3 text-center"
            sx={{ fontWeight: "bold", color: "GrayText" }}
          >
            Weekly Plan
          </Typography>
        </Grid>
      </Grid>
      <div
        // className="mx-4 shadow rounded-5 p-3"
        style={{
          maxHeight: "275vh",
          borderTopLeftRadius: "10px",
          borderTopRightRadius: "10px",
          marginTop: "0.5%",
        }}
      >
        <Grid container>
          <Grid xs={3} sx={{ float: "left" }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateCalendar
                value={value}
                onChange={(newValue) => setValue(newValue)}
                showDaysOutsideCurrentMonth
                displayWeekNumber
                slots={{ day: Day }}
                sx={{
                  backgroundColor: "#cfdae3",
                  borderRadius: "15px",
                  width: "300px", // Set your desired width
                  height: "280px", // Set your desired height
                }}
                slotProps={{
                  day: (ownerState) =>
                    ({
                      selectedDay: value,
                      hoveredDay,
                      onPointerEnter: () => setHoveredDay(ownerState.day),
                      onPointerLeave: () => setHoveredDay(null),
                    } as any),
                }}
              />
            </LocalizationProvider>

            <div
              style={{ width: "90%", height: "380px", overflowY: "auto" }}
              className="shadow m-3 ml-3"
            >
              <h4>
                <p className="text-muted " style={{ textAlign: "center" }}>
                  Team Memeber
                </p>
              </h4>
            </div>
          </Grid>
          <Grid xs={9}>
            <Grid container>
              <div className="Drags">
                {/* New Order */}
                <div
                  style={{ width: "100%", height: "580px", overflowY: "auto" }}
                  className="shadow m-2"
                  onDragLeave={(e) => onDragLeave(e)}
                  onDragEnter={(e) => onDragEnter(e)}
                  onDragEnd={(e) => onDragEnd(e)}
                  onDragOver={(e) => onDragOver(e)}
                  onDrop={(e) => onDrop(e, false, "Assigned")}
                >
                  <section className="drag_container">
                    <div className="container">
                      <div className="drag_column">
                        <div className="drag_row">
                          <h4>
                            <p className="text-muted ">Upcoming</p>
                          </h4>
                          <Button
                            variant="contained"
                            sx={{ width: "100%", position: "relative" }}
                          >
                            ADD +
                          </Button>
                          {renderTaskCards(taskList, "Assigned")}
                        </div>
                      </div>
                    </div>
                  </section>
                </div>

                {/* In Progress */}
                <div
                  style={{ width: "100%", height: "580px", overflowY: "auto" }}
                  className="shadow m-2 "
                  onDragLeave={(e) => onDragLeave(e)}
                  onDragEnter={(e) => onDragEnter(e)}
                  onDragEnd={(e) => onDragEnd(e)}
                  onDragOver={(e) => onDragOver(e)}
                  onDrop={(e) => onDrop(e, false, "In Progress")}
                >
                  <section className="drag_container">
                    <div className="container">
                      <div className="drag_column">
                        <div className="drag_row">
                          <h4>
                            <p className="text-muted ">In Progress</p>
                          </h4>
                          <Button variant="contained" sx={{ width: "100%" }}>
                            ADD +
                          </Button>
                          {renderTaskCards(taskList, "In Progress")}
                        </div>
                      </div>
                    </div>
                  </section>
                </div>
                {/* Completed */}
                <div
                  style={{ width: "100%", height: "580px", overflowY: "auto" }}
                  className="shadow m-2 pt-2 "
                  onDragLeave={(e) => onDragLeave(e)}
                  onDragEnter={(e) => onDragEnter(e)}
                  onDragEnd={(e) => onDragEnd(e)}
                  onDragOver={(e) => onDragOver(e)}
                  onDrop={(e) => onDrop(e, true, "Completed")}
                >
                  <section className="drag_container">
                    <div className="container">
                      <div className="drag_column">
                        <div className="drag_row">
                          <h4>
                            <p className="text-muted ">Completed</p>
                          </h4>
                          <Button variant="contained" sx={{ width: "100%" }}>
                            ADD +
                          </Button>
                          {renderTaskCards(taskList, "Completed")}
                        </div>
                      </div>
                    </div>
                  </section>
                </div>
              </div>
            </Grid>
          </Grid>
        </Grid>
      </div>
    </>
  );
};
