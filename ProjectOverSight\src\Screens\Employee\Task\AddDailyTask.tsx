import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  <PERSON>alogTitle,
  TextField,
} from "@mui/material";
import * as React from "react";
import { useForm } from "react-hook-form";
import { Post } from "../../../Services/Axios";
import Swal from "sweetalert2";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { ConvertToISO } from "../../../Utilities/Utils";
import { useState } from "react";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import { TransitionProps } from "@mui/material/transitions";
import Slide from "@mui/material/Slide";

const formField = ["date", "EstTime", "Description"];

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export const AddDailyTask = ({
  openDialog,
  setOpenDialog,
  Data,
  setReload,
}: any) => {
  const { handleSubmit, register, resetField } = useForm();
  const [save, setSave] = useState<boolean>(false);
  const json: any = sessionStorage.getItem("user") || null;
  const sessionUser = JSON.parse(json);

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  async function onSubmitHandler(data: any) {

    const DayPlan: any = {
      employeeId: sessionUser.employeeId,
      taskId: Data?.id,
      employeeTaskId: Data?.employeeTaskId,
      projectObjectiveId: 1,
      projectId: Data?.projectId,
      name: Data?.name,
      employeeName: "",
      projectName: "",
      comment: data.Comment,
      status: "In-Progress",
      description: data.Description,
      estTime: Number(data?.EstTime),
      weekEndingDate: undefined,
      priority: "high",
      workedOn: data.date,
    };
    setSave(true);
    const { error }: any = await Post(
      "/app/EmployeeDailyTask/AddEmployeeDayPlan",
      DayPlan
    );

    var option: AlertOption;

    if (error) {
      option = {
        title: "Error",
        text: "Error Occured!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Daily task Added!",
        icon: "success",
      };
    }

    handleClose();
    reset();
    setReload((prev: boolean) => !prev);
    Swal.fire({
      ...option,
      showConfirmButton: true,
    });
  }

  function handleClose() {
    setOpenDialog({ daily: false });
    reset();
    setSave(false);
  }

  return (
    <>
      <Dialog open={openDialog?.daily} onClose={handleClose} TransitionComponent={Transition}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle>Add Daily Task</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            <div className="row">
              <TextField
                required
                className="col m-2"
                {...register("date")}
                label="Date"
                fullWidth
                defaultValue={ConvertToISO(new Date())}
                type="date"
                variant="outlined"
              />
            </div>

            <div className="row">
              <TextField
                required
                className="col m-2"
                {...register("EstTime")}
                label="Estimation Time"
                type="text"
                fullWidth
                variant="outlined"
              />
            </div>

            <div className="row">
              <TextField
                required
                className="col m-2"
                margin="dense"
                defaultValue={Data?.description}
                {...register("Description")}
                InputProps={{
                  readOnly: true,
                }}
                label="Task Description"
                fullWidth
                type="text"
                variant="outlined"
              />
            </div>
            <div className="row">
              <TextField                
                className="col m-2"
                margin="dense"              
                {...register("Comment")}               
                label="Comments"
                fullWidth
                type="text"
                variant="outlined"
                inputProps={{
                  maxLength: 100,
                }}
              />
            </div>
            <input
              type="text"
              hidden
              {...register("EmployeeId")}
              value={sessionUser.employeeId}
            />
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="success"
              disabled={save}
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
};
