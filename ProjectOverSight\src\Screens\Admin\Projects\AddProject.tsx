import {
  Alert,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  TextareaAutosize,
  Autocomplete,
  OutlinedInput,
  ListItemText,
  Grid,
} from "@mui/material";
import Select from "@mui/material/Select";
import { useForm } from "react-hook-form";
import { useState } from "react";
import Swal from "sweetalert2";
import { Get, Post } from "../../../Services/Axios";
import { useQuery } from "react-query";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AlertOption } from "../../../Models/Common/AlertOptions";
import Checkbox from "@mui/material/Checkbox";

const formField = [
  "Name",
  "Type",
  "Description",
  "StartDate",
  "EndDate",
  "Status",
  "Percentage",
  "CreatedBy",
  "UpdatedBy",
  "TechStackId",
  "TeamId",
];

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

export const AddProject = ({ openDialog, setOpenDialog, setReload }: any) => {
  const { register, handleSubmit, resetField } = useForm();
  const [techStack, settechStack] = useState([]);
  const [save, setSave] = useState<boolean>(false);
  const [team, setTeam] = useState([]);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });

  async function fetchCommonMaster1() {
    const commonMaster: any = await Get("app/CommonMaster/GetCodeTableList");
    const teamName: any = await Get("app/Team/GetTeamList");
    return { commonMaster, teamName };
  }
  const { data } = useQuery("AddProject", fetchCommonMaster1);

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
    settechStack([]);
    setTeam([]);
  }

  const onSubmitHandler = async (data1: any) => {
    debugger
    setSave(true);
    if (data1.StartDate > data1.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }
    const defaultStatus = "In Progress";
    data1.Status = defaultStatus;

    const { error }: any = await Post("app/Project/AddProject", data1);
    var option: AlertOption;
    if (error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "Project Added Successfully!",
        icon: "success",
      };
    }

    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      setReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ add: false });
  };

  return (
    <div>
      <Dialog open={openDialog?.add}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle style={{ color: "blue", flex: "1" }}>
              Add Project
            </DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup d-flex justify-content-center">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
              <TextField
                required
                // className="col m-2"
                {...register("Name")}
                label="Project Name"
                type="text"
                variant="outlined"
              />
              </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <Autocomplete
                options={
                  data?.commonMaster?.data?.length > 0 &&
                  data?.commonMaster?.data
                    ?.sort((a: any, b: any) =>
                      a.codeValue.localeCompare(b.codeValue)
                    )
                    .filter((x: any) => x.codeType === "ProjectType")
                    .map((e: any) => ({
                      label: e.codeValue,
                      id: e.codeValue,
                    }))
                }
                // className="col-6 mt-2"
                renderInput={(params: any) => (
                  <TextField
                    {...params}
                    label="Project Type"
                    required
                    {...register("Type")}
                  />
                )}
              />
           </FormControl>
            </Grid>
              <Grid item xs={12}>
            <FormControl fullWidth>
              <TextareaAutosize
                required
                // className="col m-2 form-control"
                placeholder="Description"
                {...register("Description")}
                style={{ height: 100 }}
              />
           </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="start-date">Start date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  required
                  id="start-date"
                  margin="dense"
                  {...register("StartDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel id="end-date">End date</InputLabel>
                <FormControl fullWidth>
                <TextField
                  required
                  id="end-date"
                  margin="dense"
                  {...register("EndDate")}
                  label=""
                  type="date"
                  fullWidth
                  variant="outlined"
                />
             </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
               <FormControl fullWidth>
                  <InputLabel id="project-type">Tech Stack</InputLabel>
                  <Select
                    labelId="Tech-Stack"
                    id="Tech-Stack"
                    multiple
                    {...register("TechStackId", {
                      onChange: (e: any) => {
                        settechStack(e.target.value);
                      },
                    })}
                    value={techStack}
                    input={<OutlinedInput label="Tech Stack" />}
                    renderValue={(selected: any) => (
                      <div>
                        {selected.map((value: number) => (
                          <span key={value} style={{ marginRight: 8 }}>
                            {
                              data?.commonMaster?.data?.find(
                                (option: any) => option.id === value
                              )?.codeName
                            }
                            ,
                          </span>
                        ))}
                      </div>
                    )}
                    MenuProps={MenuProps}
                  >
                    {data?.commonMaster?.data?.length > 0 &&
                      data?.commonMaster?.data?.map((e: any) => {
                        if (e.codeType == "ProjectTechStackCatagory")
                          return (
                            <MenuItem value={e.id} key={e.codeValue}>
                              <Checkbox
                                checked={techStack.indexOf(e.id as never) > -1}
                              />
                              <ListItemText primary={e.codeName} />
                            </MenuItem>
                          );
                      })}
                  </Select>
                </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
            <FormControl fullWidth>
                {/* &nbsp; &nbsp;&nbsp; */}
                  <TextField
                    id="outlined-read-only-input"
                    label="Status"
                    disabled
                    defaultValue={"In Progress"}
                    InputProps={{
                      readOnly: true,
                    }}
                    {...register("Status")}
                  />
                </FormControl>
            </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <InputLabel id="team">Team List</InputLabel>
                  <Select
                    labelId="team"
                    id="team"
                    required
                    value={team}
                    label="Team List"
                    {...register("TeamId", {
                      onChange: (e: any) => {
                        setTeam(e.target.value);
                      },
                    })}
                  >
                    {data?.teamName?.data?.length > 0 &&
                      data?.teamName?.data
                        ?.filter((x: any) => x.isActive === true)
                        .map((e: any) => {
                          return (
                            <MenuItem value={e.id} key={e.id}>
                              {e.name}
                            </MenuItem>
                          );
                        })}
                  </Select>
                  </FormControl>
            </Grid>
            
              <input {...register("CreatedBy")} value="user" hidden />
              <input {...register("UpdatedBy")} value="user" hidden />
              <input {...register("Percentage")} value="0" hidden />
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              color="success"
              disabled={save}
              type="submit"
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </div>
  );
};
