import {
  Alert,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  // OutlinedInput,
  // ListItemText,
  // Checkbox,
  TextareaAutosize,
  Autocomplete,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";
import AddIcon from "@mui/icons-material/Add";
import { Get, Post, PostFiles } from "../../../../Services/Axios";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { AddProjectObjective } from "../ProjectObjective/AddProjectObjective";
import { AlertOption } from "../../../../Models/Common/AlertOptions";
import { FileUpload } from "../../../../CommonComponents/FileUpload";
import { Document } from "../../../../Models/Project/UserStory";

const formField = [
  "Name",
  "Description",
  "StartDate",
  "EndDate",
  "Status",
  "Percentage",
  "CreatedBy",
  "UpdatedBy",
  "ProjectId",
  "ProjectObjectiveIds",
  "ResourceId"
];

// const ITEM_HEIGHT = 44;
// const ITEM_PADDING_TOP = 8;
// const MenuProps = {
//   PaperProps: {
//     style: {
//       maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
//       width: 250,
//     },
//   },
// };

export default function AddUserStory({
  openDialog,
  setOpenDialog,
  projectId,
  SetReload,
}: any) {
  const { register, handleSubmit, resetField } = useForm();
  const [save, setSave] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<any>({
    message: "",
    show: false,
  });
  const [open, setOpen] = useState<any>({ add: false });
  const [IDs, setId] = useState<any>([]);
  const [uploadedFiles, setUploadedFiles] = useState<any>([]);
  // const [Project, setProject] = useState<any>({
  //   ProjectId: location.state.projectId,
  //   ProjectName: location.state.projectName,
  // });
  const [projectobjective, setprojectobjective] = useState<any>([]);
  // const [Requirement,setRequirement] = useState<any>([]);
  const [resourceId, setResourceId] = useState<number[]>([]);
 
  const onSubmitHandler = async (data: any) => {
    debugger
    if (data.StartDate > data.EndDate) {
      setErrorMsg({
        message: "Start Date must be before End Date",
        show: true,
      });
      return;
    }
    const defaultStatus = "Pending";
    data.Status = defaultStatus;
    data.ProjectObjectiveIds = IDs;
    data.ResourceId = resourceId.join("|");
    setSave(true);
    const response: any = await Post("app/Project/AddUserStory", data);
    if (!response.error && uploadedFiles.length > 0) {
      uploadedFiles.forEach(async (file: any) => {
        var document: Document = {
          TableName: "UserStory",
          AttributeId: response.data.id,
          ProjectId: data.ProjectId,
          DocType: data.DocType,
          FileName: file.name,
          FileType: file.type,
          File: file,
          IsActive: true,
          CreatedBy: "user",
          UpdatedBy: "user",
        };
        await PostFiles("app/Project/UploadFiles", document);
      });
    }

    var option: AlertOption;
    if (response.error) {
      option = {
        title: "Error",
        text: "Error Occured While Saving!",
        icon: "error",
      };
    } else {
      option = {
        title: "Success",
        text: "User Story Added Successfully!",
        icon: "success",
      };
    }
    Swal.fire({
      ...option,
      confirmButtonColor: "#3085d6",
    }).then(() => {
      SetReload((prev: boolean) => !prev);
    });
    handleClose();
  };

  function reset() {
    formField.map((e: string) => {
      resetField(e);
    });
  }

  const handleClose = () => {
    reset();
    setErrorMsg({
      message: "",
      show: false,
    });
    setSave(false);
    setOpenDialog({ add: false });
    setId([]);
    setUploadedFiles([]);
    setResourceId([]);
  };

  useEffect(() => {
    SetReload((prev: boolean) => !prev);
  }, [open]);

  useEffect(() => {
    async function getobjectives() {
      let objective: any = await Get(
        `app/Project/GetProjectObjective?ProjectId=${projectId}`
      );
      // let requirement : any = await Get(`app/Requirement/GetRequirements`)
      setprojectobjective(objective?.data?.projectObjectives || []);
      // setRequirement(requirement?.data || []);
    }
    getobjectives();
  }, [projectId, open]);
  

  return (
    <div>
      <Dialog open={openDialog?.add}>
        <form onSubmit={handleSubmit(onSubmitHandler)}>
          <div
            style={{
              backgroundColor: "#f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <DialogTitle>Add User Story</DialogTitle>
            <CancelOutlinedIcon
              onClick={handleClose}
              sx={{
                color: "red",
                fontSize: "30px",
                marginRight: "10px",
                cursor: "pointer",
              }}
            />
          </div>
          <DialogContent className="row popup">
            {errorMsg.show && (
              <Alert severity="error" className="mb-3">
                {errorMsg.message}. <strong>check it out!</strong>
              </Alert>
            )}
            <div className="row mx-auto">
              <TextField
                required
                className="col m-2"
                {...register("Name")}
                label="User Story Name"
                type="text"
                variant="outlined"
              />
              <FormControl className="col m-2">
                <TextField
                  id="outlined-read-only-input"
                  label="Status"
                  disabled
                  defaultValue={"Pending"}
                  InputProps={{
                    readOnly: true,
                  }}
                  {...register("Status")}
                />
              </FormControl>
            </div>
            <div className="row mx-auto">
              <TextareaAutosize
                required
                className="col m-2 mb-3 form-control"
                aria-label="empty textarea"
                placeholder="Description"
                {...register("Description")}
                style={{ height: 90 }}
              />
            </div>
            <div className="row mx-auto">
              <FormControl className="col m-2">
                <FileUpload
                  uploadedFiles={uploadedFiles}
                  setUploadedFiles={setUploadedFiles}
                />
              </FormControl>
              <FormControl className="col m-2">
                <InputLabel id="Status">Document Type</InputLabel>
                <Select
                  labelId="Document Type"
                  id="DocumentType"
                  label="Document Type"
                  required={uploadedFiles.length > 0}
                  {...register("DocType")}
                >
                  <MenuItem value="Input">Input</MenuItem>
                  <MenuItem value="Process">Process</MenuItem>
                  <MenuItem value="Output">Output</MenuItem>
                  <MenuItem value="Sample Code">Sample Code</MenuItem>
                </Select>
              </FormControl>
            </div>
            <div className="row mx-auto">
              <div className="col">
                <InputLabel id="start-date">Start date</InputLabel>
                <TextField
                  required
                  id="start-date"
                  margin="dense"
                  {...register("StartDate")}
                  label=""
                  type="date"
                  variant="outlined"
                  fullWidth
                />
              </div>
              <div className="col">
                <InputLabel id="end-date">End date</InputLabel>
                <TextField
                  required
                  id="end-date"
                  margin="dense"
                  {...register("EndDate")}
                  label=""
                  type="date"
                  variant="outlined"
                  fullWidth
                />
              </div>
            </div>
            <input {...register("CreatedBy")} value="user" hidden />
            <input {...register("UpdatedBy")} value="user" hidden />
            <input {...register("Percentage")} value="0" hidden />
            <input {...register("ProjectId")} value={projectId} hidden />
            <div className="row mx-auto">
              <div className="col-6 mt-2">
                <Autocomplete
                  multiple
                  options={projectobjective.map((objective: any) => ({
                    label: objective.description,
                    id: objective.id,
                  }))}
                  {...register("ProjectObjectiveIds")}
                  onChange={(e: any, value: any) => {
                    if (value) var ids = [];
                    value.map((e: any) => {
                      ids.push(e.id);
                    });
                    setId(ids);
                    return e;
                  }}
                  renderInput={(params: any) => (
                    <TextField
                      {...params}
                      label="Select Objectives"
                      variant="outlined"
                    />
                  )}
                />
              </div>
              <Button
                variant="contained"
                className="mt-2 m-2 col"
                onClick={() => setOpen({ add: true })}
              >
                Add Project Objective
                <AddIcon className="mx-1" />
              </Button>
            </div>
            {/* <div className="row mx-auto">
            <div className="col-6 mt-3">
            <FormControl fullWidth>
            <InputLabel id="Requirements">Requirements</InputLabel>
            <Select
  labelId="ResourceId"
  id="DocumentType"
  label="Requirements"
  required
  multiple
  value={resourceId}
  onChange={(e) => {
    const newValue:any = Array.isArray(e.target.value) ? e.target.value : [e.target.value];
    setResourceId(newValue);
  }}
  renderValue={(selected) => (
    <div>
      {selected.map((value: number) => (
        <span key={value} style={{ marginRight: 8 }}>
          {
            Requirement.find(
              (option: any) => option.id === value
            )?.requirementDefinition
          }
          ,
        </span>
      ))}
    </div>
  )}
  input={<OutlinedInput label="Requirement Source"/>}
  MenuProps={MenuProps}
>
  {Requirement?.length > 0 &&
    Requirement
      .filter((x: any) => x.projectId === projectId)
      .map((e: any) => (
        <MenuItem value={e?.id} key={e?.id} sx={{height:'100%'}}>
          <Checkbox checked={resourceId.indexOf(e?.id) > -1} />
          <ListItemText sx={{fontSize:'20px'}} primary={e.requirementDefinition} />
        </MenuItem>
      ))}
</Select>
</FormControl>
</div>
            </div> */}
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              size="medium"
              variant="contained"
              color="error"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              variant="contained"
              disabled={save}
              color="success"
              type="submit"
            >
              {save ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <AddProjectObjective
        openDialog={open}
        setOpenDialog={setOpen}
        ProjectId={projectId}
        SetReload={SetReload}
      />
    </div>
  );
}
