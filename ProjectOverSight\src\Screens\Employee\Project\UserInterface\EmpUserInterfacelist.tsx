import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  FormControl,
  Autocomplete,
  TextField,
} from "@mui/material";
import { Link, useLocation } from "react-router-dom";
import Select from "react-select";
import SearchIcon from "@mui/icons-material/Search";
import AddIcon from "@mui/icons-material/Add";
import DownloadIcon from "@mui/icons-material/Download";
import Box from "@mui/material/Box";
import { useEffect, useRef, useState } from "react";
import { AddUserInterface } from "./AddUserInterface";
import { EditUserInterface } from "./EditUserInterface";
import { ViewUserInterface } from "./VeiwUserInterface";
import RefreshIcon from "@mui/icons-material/Refresh";
import { UserInterface } from "../../../../Models/Project/UserInterface";
import { ConvertDate } from "../../../../Utilities/Utils";
import { Get } from "../../../../Services/Axios";
import { DownloadEmpUserInterfaceList } from "../../../../Services/ProjectService";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { Tooltip } from "@mui/material";
import DataTable from "react-data-table-component";
import { Project } from "../../../../Models/Project/Project";

export const EmpUserInterfacelist = () => {
  const [filterRows, setfilterRows] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [reload, setReload] = useState<boolean>(true);
  const [rows, setRows] = useState<any>([]);
  const location = useLocation();
  const uiNameRef = useRef<HTMLInputElement>(null);
  const statuiRef = useRef<any>();
  const percentageRef = useRef<HTMLInputElement>(null);
  const actStartDateRef = useRef<HTMLInputElement>(null);
  const actEndDateRef = useRef<HTMLInputElement>(null);
  const descriptionRef = useRef<HTMLInputElement>(null);
  const complexityRef = useRef<any>();
  const [filter, setfilter] = useState<UserInterface>({});
  const [userInterfacedata, setUserInterfacedata] = useState<any>();
  const [data, setData] = useState([]);
  const [UserInterfaceView, setUserInterfaceView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });
  const [projects, setProjects] = useState([]);
  const [Project, setProject] = useState<any>({
    ProjectId: location.state.projectId,
    ProjectName: location.state.projectName,
  });

  const columns: any = [
    {
      field: "Action",
      name: "Action",
      type: "Date",
      width: 200,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "center",
      align: "center",
      selector: (params: any) => {
        return (
          <>
            <Tooltip
              className="mx-2"
              title="View"
              onClick={() => {
                setUserInterfaceView({ view: true });
                setUserInterfacedata(params);
              }}
            >
              <VisibilityIcon className="fs-4 text-info" />
            </Tooltip>

            <Tooltip
              className="mx-2"
              title="Edit"
              onClick={() => {
                setUserInterfaceView({ edit: true });
                setUserInterfacedata(params);
              }}
            >
              <EditIcon className="fs-4 text-warning" />
            </Tooltip>

            <Link
              to="/Admin/UserStoryList"
              state={{ projectId: params.id }}
              style={{ textDecoration: "none" }}
            ></Link>
          </>
        );
      },
    },
    {
      field: "name",
      name: "Name",
      width: 280,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => <p className="tableStyle">{row.name}</p>,
    },
    {
      field: "description",
      name: "Description",
      width: 500,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (row: any) => <p className="tableStyle">{row.description}</p>,
    },
    {
      field: "status",
      name: "Status",
      width: 110,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.status}</p>,
    },
    {
      field: "percentage",
      name: "Percentage",
      type: "number",
      width: 100,
      align: "right",
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.percentage}</p>,
    },
    {
      field: "complexity",
      name: "Complexity",
      width: 150,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      editable: false,
      selector: (row: any) => <p className="tableStyle">{row.complexity}</p>,
    },
    {
      field: "startDate",
      name: "Start Date",
      type: "Date",
      width: 150,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (params: any) => {
        const result = ConvertDate(params.startDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
    {
      field: "endDate",
      name: "End Date",
      type: "Date",
      width: 150,
      editable: false,
      headerClassName: "bg-primary text-light",
      headerAlign: "left",
      align: "left",
      selector: (params: any) => {
        const result = ConvertDate(params.endDate);
        return <p className="tableStyle">{result}</p>;
      },
    },
  ];

  async function fetchData() {
    setLoading(true);
    let UserInterface = Get(
      `app/Project/GetUserInterfaceList?projectId=${Project.ProjectId}`
    );
    UserInterface.then((response: any) => {
      setRows(response?.data);
      setfilterRows(response?.data || []);
    });

    const response: any = await Get("app/Project/GetEmployeeProjectlist");
    setProjects(response.data || []);

    var objective: any = await Get(
      `app/Project/GetProjectObjective?ProjectId=${Project.ProjectId}`
    );
    setData(objective?.data?.projectObjectives || []);
    setLoading(false);
  }

  useEffect(() => {
    fetchData();
  }, [reload]);

  const handleClickOpen = () => {
    setUserInterfaceView({ add: true });
  };

  function ApplyFilter() {
    let temp: any = [];

    if (filter.startDate != null) {
      if (filter.endDate == null) {
        actEndDateRef.current?.focus();
        return;
      }
      temp = [];
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].startDate.slice(0, 10) >= filter.startDate &&
          rows[i].endDate.slice(0, 10) <= filter.endDate
        ) {
          temp.push(rows[i]);
        }
      }
      setfilterRows(temp);
    } else {
      temp = rows;
    }

    if (filter.name != null) {
      temp = temp.filter((e: any) => {
        return e.name.toLowerCase().search(filter.name?.toLowerCase()) >= 0;
      });
      setfilterRows(temp);
    }

    if (filter.description != null) {
      temp = temp.filter((e: any) => {
        return (
          e.description.toLowerCase() === filter.description?.toLowerCase()
        );
      });
      setfilterRows(temp);
    }

    if (filter.status != null) {
      temp = temp.filter((e: any) => {
        return e.status.toLowerCase() === filter.status?.toLowerCase();
      });
      setfilterRows(temp);
    }

    if (filter.percentage != null) {
      temp = temp.filter((e: any) => {
        return e.percentage === Number(filter.percentage);
      });
      setfilterRows(temp);
    }

    if (filter.complexity != null) {
      temp = temp.filter((e: any) => {
        return e.complexity.toLowerCase() === filter.complexity?.toLowerCase();
      });
      setfilterRows(temp);
    }
  }

  function reset() {
    setfilter({});
    if (uiNameRef.current) uiNameRef.current.value = "";
    if (statuiRef.current) statuiRef.current.clearValue();
    if (percentageRef.current) percentageRef.current.value = "";
    if (actStartDateRef.current) actStartDateRef.current.value = "";
    if (actEndDateRef.current) actEndDateRef.current.value = "";
    if (descriptionRef.current) descriptionRef.current.value = "";
    if (complexityRef.current) complexityRef.current.clearValue();

    setfilterRows(rows);
  }

  return (
    <div>
      <Breadcrumbs className="mt-3 mx-3" separator=">">
        <Link to="/Employee">
          <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
        </Link>
        <Link to="/Employee/Project">
          <Typography sx={{ fontWeight: "bold" }}>Project</Typography>
        </Link>
        <Typography sx={{ fontWeight: "bold" }}>User Interface</Typography>
      </Breadcrumbs>
      <Grid container sx={{ mt: "2%" }}>
        <Grid xs={2}>
          <FormControl fullWidth sx={{ ml: "20%" }}>
            <Autocomplete
              options={projects
                .sort((a: Project, b: Project) =>
                  a.name!.localeCompare(b.name!)
                )
                .map((project: Project) => ({
                  label: project.name,
                  id: project.id,
                }))}
              onChange={(e: any, value: any) => {
                if (value)
                  setProject({
                    ProjectId: value.id,
                    ProjectName: value.label,
                  });
                setReload((prev) => !prev);
                return e;
              }}
              renderInput={(params: any) => (
                <TextField
                  {...params}
                  label="Select Project"
                  variant="outlined"
                  required
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid xs={10}>
          <Typography align="center" className="fw-bolder fs-3">
            Project Name: {Project.ProjectName}
          </Typography>
        </Grid>
      </Grid>
      {/* <div className="row col-md-6 m-5">
        <FormControl className="col-md-4">
         
        </FormControl>
      </div> */}

      <div className="well mx-auto mt-4">
        <div className="row">
          <div className="col-sm-2">
            <div className="form-group">
              <label>User Interface Name</label>
              <input
                id="User-Interface-Name"
                placeholder="User Interface Name"
                ref={uiNameRef}
                className="m-1 form-control col"
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const alphabeticValue = inputValue.replace(/[^A-Za-z]/g, ""); // Remove non-alphabetic characters
                  setfilter((prevState) => ({
                    ...prevState,
                    name: alphabeticValue === "" ? undefined : alphabeticValue,
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Description</label>
              <input
                id="Description"
                placeholder="Description"
                ref={descriptionRef}
                className="m-1 form-control col"
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const alphabeticValue = inputValue.replace(/[^A-Za-z]/g, ""); // Remove non-alphabetic characters
                  setfilter((prevState) => ({
                    ...prevState,
                    description:
                      alphabeticValue === "" ? undefined : alphabeticValue,
                  }));
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Status</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="status"
                ref={statuiRef}
                className="select-dropdowns mt-1 col"
                onInputChange={(inputValue: string) => {
                  const alphabeticValue = inputValue.replace(
                    /[^A-Za-z\s]/g,
                    ""
                  ); // Remove non-alphabetic characters
                  return alphabeticValue;
                }}
                onChange={(selectedOption: any) => {
                  if (selectedOption) {
                    setfilter((prevState) => ({
                      ...prevState,
                      status:
                        selectedOption.label.trim() === ""
                          ? null
                          : selectedOption.label,
                    }));
                  }
                }}
                options={[
                  {
                    label: "In Progress",
                    value: "In Progress",
                  },
                  {
                    label: "Completed",
                    value: "Completed",
                  },
                  {
                    label: "Pending",
                    value: "Pending",
                  },
                  {
                    label: "Active",
                    value: "Active",
                  },
                ]}
                placeholder="Status"
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Percentage</label>
              <input
                id="percentage"
                placeholder="Percentage"
                className="m-1 form-control col"
                ref={percentageRef}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const numericValue = parseFloat(inputValue);

                  if (!isNaN(numericValue) || inputValue === "") {
                    setfilter((prevState) => ({
                      ...prevState,
                      percentage: isNaN(numericValue)
                        ? undefined
                        : numericValue,
                    }));
                  }
                }}
              />
            </div>
          </div>
          <div className="col-sm-2">
            <div className="form-group">
              <label>Complexity</label>
              <Select
                aria-label="Floating label select example"
                isClearable={true}
                name="complexity"
                ref={complexityRef}
                className="select-dropdowns mt-1 col"
                onInputChange={(inputValue: string) => {
                  const alphabeticValue = inputValue.replace(
                    /[^A-Za-z\s]/g,
                    ""
                  ); // Remove non-alphabetic characters
                  return alphabeticValue;
                }}
                onChange={(selectedOption: any) => {
                  if (selectedOption) {
                    setfilter((prevState) => ({
                      ...prevState,
                      complexity:
                        selectedOption.label.trim() === ""
                          ? null
                          : selectedOption.label,
                    }));
                  }
                }}
                options={[
                  {
                    label: "Low",
                    value: "Low",
                  },
                  {
                    label: "Medium",
                    value: "Medium",
                  },
                  {
                    label: "High ",
                    value: "High ",
                  },
                ]}
                placeholder="Complexity"
                isSearchable={true}
                formatOptionLabel={(option: any) => option.label}
              />
            </div>
          </div>

          <div className="col-md-2">
            <div className="form-group">
              <label className="mx-1">Start Date</label>
              <input
                onChange={(e: any) => {
                  setfilter((prevState: any) => {
                    return {
                      ...prevState,
                      startDate: e.target.value == "" ? null : e.target.value,
                    };
                  });
                }}
                type="date"
                id="start-date"
                placeholder="Start Date"
                ref={actStartDateRef}
                className="m-1 col form-control"
              />
            </div>
          </div>
          <div className="container">
            <div className="row">
              <div className="col-md-2">
                <div className="form-group">
                  <label className="mx-1">End Date</label>
                  <input
                    onChange={(e: any) => {
                      setfilter((prevState: any) => {
                        return {
                          ...prevState,
                          endDate: e.target.value == "" ? null : e.target.value,
                        };
                      });
                    }}
                    type="date"
                    id="end-date"
                    placeholder="End Date"
                    ref={actEndDateRef}
                    className="m-1 col form-control"
                  />
                </div>
              </div>
              <div className="col-md-10">
                <div className="row justify-content-end">
                  <div className="col-auto">
                    <Button
                      variant="contained"
                      endIcon={<SearchIcon />}
                      className="mx-1 mt-4"
                      onClick={() => ApplyFilter()}
                    >
                      search
                    </Button>
                    <Button
                      variant="contained"
                      endIcon={<RefreshIcon />}
                      className="mx-3 mt-4"
                      onClick={() => reset()}
                    >
                      Reset
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex flex-column justify-content-center align-items-center">
        <div className="col-11 col-s-4">
          <Grid>
            <Button
              variant="contained"
              className="mb-3 float-md-start"
              onClick={handleClickOpen}
              sx={{ ml: "3%" }}
            >
              Add User Interface
              <AddIcon className="mx-1" />
            </Button>
            <Button
              variant="contained"
              className="mb-3 float-md-end"
              onClick={() => {
                DownloadEmpUserInterfaceList(filterRows);
              }}
              sx={{ mr: "3%" }}
            >
              Download
              <DownloadIcon className="mx-1" />
            </Button>
          </Grid>
        </div>
        <Grid item xs={12} sm={11} sx={{ mt: "4%" }}>
          <div className="responsive-div col-3 col-s-3">
            <Box style={{ width: "94vw", position: "relative" }}>
              <DataTable
                data={filterRows || []}
                columns={columns}
                progressPending={loading}
                fixedHeader={true}
                responsive
                persistTableHead
                customStyles={{
                  table: {
                    style: {
                      height: "80vh",
                      border: "1px solid rgba(0,0,0,0.1)",
                      overflowY: "scroll",
                    },
                  },
                  headRow: {
                    style: {
                      background: "#1e97e8",
                      fontSize: "16px",
                      color: "white",
                      fontFamily: "inherit",
                    },
                  },
                  pagination: {
                    style: {
                      position: "absolute",
                      width: "94vw",
                      background: "#daeef0",
                      color: "black",
                      textAlign: "right",
                      top: -55,
                      borderRadius: "5px 5px 0 0",
                    },
                  },
                }}
                pagination
                paginationPerPage={50}
                paginationRowsPerPageOptions={[10, 20, 30, 40, 50]}
                pointerOnHover={true}
              />
            </Box>
          </div>
        </Grid>
      </div>
      <AddUserInterface
        openDialog={UserInterfaceView}
        setOpenDialog={setUserInterfaceView}
        setReload={setReload}
        projectId={Project.ProjectId}
        data1={data?.length > 0 ? data : []}
      />
      <EditUserInterface
        openDialog={UserInterfaceView}
        setOpenDialog={setUserInterfaceView}
        Data={userInterfacedata}
        setReload={setReload}
        projectId={location.state.projectId}
      />
      <ViewUserInterface
        openDialog={UserInterfaceView}
        setOpenDialog={setUserInterfaceView}
        Data={userInterfacedata}
      />
    </div>
  );
};
