import { Mic } from "lucide-react";
import { useState, useRef, useEffect } from "react";

const VoiceRecorder = ({ question, setQuestions }: any) => {
  const [transcript, setTranscript] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const mediaRecorderRef = useRef<any>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recognitionRef = useRef<any>(null);

  // Create audio URL when question.audio changes
  useEffect(() => {
    if (question.audio) {
      // Clean up previous URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }

      // Create new URL if audio is a File object
      if (!(question.audio instanceof File)) {
        const url = URL.createObjectURL(question.audio);
        setAudioUrl(url);
      } else if (typeof question.audio === "string") {
        // If it's already a URL string, use it directly
        setAudioUrl(question.audio);
      }
    } else {
      // Clean up URL if no audio
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
        setAudioUrl(null);
      }
    }

    // Cleanup function
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [question.audio]);

  const startRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // Setup MediaRecorder
    const mediaRecorder = new MediaRecorder(stream);
    mediaRecorderRef.current = mediaRecorder;
    audioChunksRef.current = [];

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunksRef.current.push(event.data);
        console.log("Audio chunk received, size:", event.data.size); // Debug log
      }
    };

    // Start recording with timeslice to ensure data is collected
    mediaRecorder.start(1000); // Collect data every 1000ms

    // Setup SpeechRecognition
    const SpeechRecognition: any =
      window?.SpeechRecognition || window?.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert("SpeechRecognition not supported in this browser.");
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.lang = "en-US";
    recognition.continuous = true;
    recognition.interimResults = true;

    recognition.onresult = (event: any) => {
      let finalTranscript = "";
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        const result = event.results[i];
        finalTranscript += result[0].transcript;
      }
      setTranscript(finalTranscript);
    };

    recognition.onerror = (e: any) => {
      console.error("SpeechRecognition Error", e);
    };

    recognitionRef.current = recognition;
    recognition.start();
    setIsRecording(true);
  };

  const stopRecording = async () => {
    setIsRecording(false);

    // Stop speech recognition first
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }

    // Stop media recorder and handle the data when it's ready
    if (!mediaRecorderRef.current) return;

    // Set up the onstop handler to process the audio data
    mediaRecorderRef.current.onstop = async () => {
      // Create audio blob from collected chunks
      const audioBlob = new Blob(audioChunksRef.current, {
        type: "audio/webm",
      });

      console.log("Audio blob size:", audioBlob.size); // Debug log

      if (audioBlob.size === 0) {
        console.warn("Audio blob is empty - no audio data was recorded");
        return;
      }

      const audioFile = new File([audioBlob], "voice.webm", {
        type: "audio/webm",
      });

      // Update the questions state with the audio file
      setQuestions((prev: any[]) =>
        prev.map((q: any) =>
          q.id === question.id
            ? {
                ...q,
                question: question.question,
                answer: transcript,
                audio: audioBlob,
              }
            : q
        )
      );

      // Prepare form data
      const formData = new FormData();
      formData.append("audio", audioFile);
      formData.append("transcript", transcript);

      // Send to server (placeholder)
      try {
        await fetch("/api/save-voice", {
          method: "POST",
          body: formData,
        });
      } catch (error) {
        console.error("Error saving audio:", error);
      }

      // Clear transcript for next recording
      setTranscript("");
    };

    // Stop the recorder - this will trigger the onstop event
    mediaRecorderRef.current.stop();

    // Stop all tracks
    mediaRecorderRef.current.stream
      .getTracks()
      .forEach((track: any) => track.stop());
  };

  const resetRecording = () => {
    // Show confirmation dialog if there's existing audio or transcript
    const hasContent = audioUrl || transcript || question.answer;
    if (hasContent) {
      const confirmReset = window.confirm(
        "Are you sure you want to reset? This will delete the current recording and transcript."
      );
      if (!confirmReset) {
        return;
      }
    }

    // Stop any ongoing recording first
    if (isRecording) {
      stopRecording();
    }

    // Clear the transcript
    setTranscript("");

    // Clean up the current audio URL
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
    }

    // Update the question state to remove audio and answer
    setQuestions((prev: any[]) =>
      prev.map((q: any) =>
        q.id === question.id
          ? {
              ...q,
              question: question.question,
              answer: "",
              audio: null,
            }
          : q
      )
    );

    // Reset audio chunks
    audioChunksRef.current = [];

    console.log("Recording reset for question:", question.id);
  };

  return (
    <div className="voice-recorder-container">
      <div className="controls">
        <button
          onClick={isRecording ? stopRecording : startRecording}
          className={`record-btn ${isRecording ? "bg-danger" : ""}`}
        >
          <Mic /> {isRecording ? "Stop Recording" : "Start Recording"}
        </button>
        <button
          className="reset-btn"
          onClick={resetRecording}
          disabled={!audioUrl && !transcript && !question.answer}
          title={
            !audioUrl && !transcript && !question.answer
              ? "Nothing to reset"
              : "Reset recording and transcript"
          }
        >
          Reset
        </button>
      </div>

      <div className="output-section">
        {audioUrl && (
          <div className="audio-player w-100 d-flex justify-content-center">
            <audio controls src={audioUrl}></audio>
          </div>
        )}
        <textarea
          className="transcript-box"
          cols={90}
          rows={4}
          value={transcript ?? question.answer}
          readOnly
        />
      </div>
    </div>
  );
};

export default VoiceRecorder;
