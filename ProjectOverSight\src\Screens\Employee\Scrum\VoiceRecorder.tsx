import { Mic } from "lucide-react";
import { useState, useRef } from "react";

const VoiceRecorder = ({ question, setQuestions }: any) => {
  const [transcript, setTranscript] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const mediaRecorderRef = useRef<any>(null);
  const audioChunksRef = useRef([]);
  const recognitionRef = useRef<any>(null);

  const startRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // Setup MediaRecorder
    const mediaRecorder = new MediaRecorder(stream);
    mediaRecorderRef.current = mediaRecorder;
    audioChunksRef.current = [];

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunksRef.current.push(event.data);
      }
    };

    mediaRecorder.start();

    // Setup SpeechRecognition
    const SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert("SpeechRecognition not supported in this browser.");
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.lang = "en-US";
    recognition.continuous = true;
    recognition.interimResults = true;

    recognition.onresult = (event) => {
      let finalTranscript = "";
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        const result = event.results[i];
        finalTranscript += result[0].transcript;
      }
      setTranscript(finalTranscript);
    };

    recognition.onerror = (e) => {
      console.error("SpeechRecognition Error", e);
    };

    recognitionRef.current = recognition;
    recognition.start();
    setIsRecording(true);
  };

  const stopRecording = async () => {
    setIsRecording(false);

    // Stop media recorder
    if (!mediaRecorderRef.current) return;
    mediaRecorderRef.current.stop();
    mediaRecorderRef.current.stream
      .getTracks()
      .forEach((track: any) => track.stop());

    // Stop speech
    if (!recognitionRef.current) return;
    recognitionRef.current.stop();

    // Create audio blob
    const audioBlob = new Blob(audioChunksRef.current, { type: "audio/webm" });
    const audioFile = new File([audioBlob], "voice.webm", {
      type: "audio/webm",
    });

    setQuestions((prev: any) => {
      return [
        ...prev.filter((x: any) => x.id !== question.id),
        {
          id: question.id,
          question: question.question,
          answer: transcript,
          audio: audioFile,
        },
      ];
    });

    // Prepare form data
    const formData = new FormData();
    formData.append("audio", audioFile);
    formData.append("transcript", transcript);

    // Send to server (placeholder)
    await fetch("/api/save-voice", {
      method: "POST",
      body: formData,
    });

    // alert("Recording and transcript saved!");
    setTranscript("");
  };

  return (
    <div className="voice-recorder-container">
      <div className="controls">
        <button
          onClick={isRecording ? stopRecording : startRecording}
          className={`record-btn ${isRecording ? "bg-danger" : ""}`}
        >
          <Mic /> {isRecording ? "Stop Recording" : "Start Recording"}
        </button>
        <button className="reset-btn">Reset</button>
      </div>

      <div className="output-section">
        {question.audio && (
          <div className="audio-player w-100 d-flex justify-content-center">
            <audio controls src={question.audio}></audio>
          </div>
        )}
        <textarea
          className="transcript-box"
          cols={90}
          rows={4}
          value={transcript ?? question.answer}
          readOnly
        />
      </div>
    </div>
  );
};

export default VoiceRecorder;
