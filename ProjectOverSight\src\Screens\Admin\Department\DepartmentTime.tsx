import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  <PERSON>,
  MenuItem,
  Grid,
} from "@mui/material";
import { Link } from "react-router-dom";
import Paper from "@mui/material/Paper";
import { styled } from "@mui/material/styles";
import { useEffect, useRef, useState } from "react";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import TextField from "@mui/material/TextField";
import { useForm } from "react-hook-form";
import { Get, Post } from "../../../Services/Axios";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import EditIcon from "@mui/icons-material/Edit";
import Swal from "sweetalert2";
import { EmployeeModal } from "./DepartmentEmployee";
import { EditDepartmentTime } from "./EditDepartmentTime";
import BackDrop from "../../../CommonComponents/BackDrop";
import { ToolTip } from "../../../CommonComponents/ToolTip";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === "dark" ? "#1A2027" : "#fff",
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  color: theme.palette.text.secondary,
}));

const Items = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === "dark" ? "#1A2027" : "#fff",
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  color: theme.palette.text.secondary,
  borderTop: "8px solid #00bfff",
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

type DepartmentTimeDetails = {
  Id?: number;
  CommonMasterId?: number;
  StartTime?: string | null;
  EndTime?: string | null;
};

interface Employee {
  name: string;
}

export const DepartmentTime = () => {
  const startTimeRef = useRef<HTMLInputElement | null>(null);
  const endTimeRef = useRef<HTMLInputElement | null>(null);
  const subCategoryRef = useRef<any>();
  const [startTimeValue, setStartTimeValue] = useState<Date | null>(null);
  const [endTimeValue, setEndTimeValue] = useState<Date | null>(null);
  const [categories, setcategories] = useState<any>([]);
  const [subCategories, setSubCategories] = useState<any>([]);
  const [tDepartmentTime, setDepartmentTime] = useState<any>([]);
  const { register, handleSubmit } = useForm();
  const [refetch, setRefetch] = useState<boolean>(false);
  const [load, setload] = useState<boolean>(false);
  const [categoryValue, setCategoryValue] = useState<string>("");
  const [selectedValue, setSelectedValue] = useState<string>("");
  const [DepartmentView, setDepartmentView] = useState<any>({
    view: false,
    edit: false,
    add: false,
  });
  const [DepartmentDetails, setDepartmentDetails] = useState<any>(null);
  const [modalEmployees, setModalEmployees] = useState<any>(null);
  const [loading, setLoading] = useState<any>(true);

  const handleModalOpen = (employees: Employee[] | null) => {
    setModalEmployees(employees);
  };

  const handleModalClose = () => {
    setModalEmployees(null);
  };

  const onSubmitHandler = async (data: any) => {
    setLoading(true);
    const startTimeValue = startTimeRef.current?.value;
    const endTimeValue = endTimeRef.current?.value;

    const DepartmentTimeDetail: DepartmentTimeDetails = {
      Id: data.id,
      CommonMasterId: data?.subCategory,
      StartTime: startTimeValue,
      EndTime: endTimeValue,
    };

    const findDepartment = tDepartmentTime.filter(
      (e: any) => e.categories[0]?.id === DepartmentTimeDetail.CommonMasterId
    );

    if (findDepartment.length > 0) {
      const departmentToUpdate = findDepartment[0];
      const updatedDepartmentTimeDetail: DepartmentTimeDetails = {
        Id: departmentToUpdate.id,
        CommonMasterId: data?.subCategory,
        StartTime: startTimeValue,
        EndTime: endTimeValue,
      };
      const confirmResult = await Swal.fire({
        title: "Confirmation",
        text: `Are you sure want to update Department - ${
          departmentToUpdate?.categories[0]?.codeValue
        } and Start Time - ${convertTo12HourFormat(
          departmentToUpdate?.startTime.split("T")[1]?.substring(0, 5)
        )} and End Time - ${convertTo12HourFormat(
          departmentToUpdate?.endTime.split("T")[1]?.substring(0, 5)
        )}?`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const { error }: any = await Post(
          "app/DepartmentTime/UpdateDepartmentTime",
          updatedDepartmentTimeDetail
        );

        if (!error) {
          const updatedDepartmentTime = tDepartmentTime.map(
            (department: any) => {
              if (department.id === departmentToUpdate.id) {
                return { ...department, ...updatedDepartmentTimeDetail };
              }
              return department;
            }
          );
          setDepartmentTime(updatedDepartmentTime);
          const updatedDepartmentTimeList: any = await Get(
            "app/DepartmentTime/GetDepartmentTimes"
          );
          setDepartmentTime(updatedDepartmentTimeList.data || []);

          Swal.fire({
            title: "Success",
            text: "Employee Updated Successfully!",
            icon: "success",
            confirmButtonColor: "#3085d6",
          });
        } else {
          Swal.fire({
            title: "Error",
            text: "Data Not Updated!",
            icon: "error",
            confirmButtonColor: "#3085d6",
          });
        }

        setStartTimeValue(null);
        setEndTimeValue(null);
        setCategoryValue("");
        setSelectedValue("");
      }
    } else {
      const newDepartmentTimeDetail: DepartmentTimeDetails = {
        CommonMasterId: data?.subCategory,
        StartTime: startTimeValue,
        EndTime: endTimeValue,
      };

      const { error }: any = await Post(
        "app/DepartmentTime/AddDepartmentTime",
        newDepartmentTimeDetail
      );

      if (!error) {
        const updatedDepartmentTimeList: any = await Get(
          "app/DepartmentTime/GetDepartmentTimes"
        );
        setDepartmentTime(updatedDepartmentTimeList.data || []);

        Swal.fire({
          title: "Success",
          text: "Details Added successfully!",
          icon: "success",
          confirmButtonColor: "#3085d6",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: "Error Occurred While Saving!",
          icon: "error",
          confirmButtonColor: "#3085d6",
        });
      }
      setStartTimeValue(null);
      setEndTimeValue(null);
      setCategoryValue("");
      setSelectedValue("");
    }

    setStartTimeValue(null);
    setEndTimeValue(null);
    setCategoryValue("");
    setSelectedValue("");

    setLoading(false);
  };

  var Category: string[] = [];
  var categorySet = new Set<any>();
  useEffect(() => {
    setLoading(true);
    let categoriesList = Get("app/CommonMaster/GetCodeTableList");
    let DepartmentTimeList = Get("app/DepartmentTime/GetDepartmentTimes");
    categoriesList.then((response: any) => {
      var category = response?.data?.filter(
        (x: any) => x.codeType === "EmployeeCategory"
      );
      setcategories(category || []);
    });
    DepartmentTimeList.then((response: any) => {
      setDepartmentTime(response?.data || []);
      setLoading(false);
    });

    setLoading(false);
  }, [refetch, modalEmployees, load]);

  categories?.forEach((element: any) => {
    categorySet.add(element.codeName);
  });

  Category = [...categorySet];

  const handleCategoryChange = (event: any) => {
    if (subCategoryRef.current) subCategoryRef.current.value = "";
    const subCategories = categories.filter(
      (element: any) => element.codeName === event
    );
    setSubCategories(subCategories);
  };

  const handleSelectChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedValue(event.target.value);
  };

  const convertTo12HourFormat = (time24: any) => {
    let [hour, minute] = time24?.split(":");
    let period = "AM";

    if (hour >= 12) {
      period = "PM";
      if (hour > 12) {
        hour -= 12;
      }
    }

    return `${hour}:${minute} ${period}`;
  };

  const handleReset = () => {
    setStartTimeValue(null);
    setEndTimeValue(null);
    setCategoryValue("");
    setSelectedValue("");
  };

  return (
    <div className="main-container ">
      <div>
        <Breadcrumbs className="mt-3 mx-3 " separator=">">
          <Link to="/Admin">
            <Typography sx={{ fontWeight: "bold" }}>Home</Typography>
          </Link>
          <Typography sx={{ fontWeight: "bold", color: "grey" }}>
            Department
          </Typography>
        </Breadcrumbs>
        <Box sx={{ width: "80%", margin: "auto" }}>
          <Item>
            <Grid container sx={{ justifyContent: "space-between" }}>
              <Grid xs={12} sm={6} md={4} sx={{ p: "15px 0px 0px 50px" }}>
                <div
                  className="card text-white bg-info mb-3"
                  style={{ maxWidth: "18rem" }}
                >
                  <h4 className="card-header" style={{ textAlign: "center" }}>
                    Total Department
                  </h4>
                  <div className="card-body">
                    <h3 className="card-title" style={{ textAlign: "center" }}>
                      {tDepartmentTime[0]?.totalEmployees
                        ? categories?.length
                        : 0}
                    </h3>
                  </div>
                </div>
                <div
                  className="card text-white  bg-secondary  mb-3"
                  style={{ maxWidth: "18rem" }}
                >
                  <h4 className="card-header" style={{ textAlign: "center" }}>
                    Total Employees
                  </h4>
                  <div className="card-body">
                    <h3 className="card-title" style={{ textAlign: "center" }}>
                      {tDepartmentTime[0]?.totalEmployees || 0}
                    </h3>
                  </div>
                </div>
              </Grid>
              <Grid xs={12} sm={6} md={8} sx={{ p: "0px 15px 0px 0px" }}>
                <Items>
                  <Typography
                    sx={{
                      color: "Highlight",
                      fontWeight: "bold",
                      fontSize: "25px",
                      mb: "10px",
                      textDecoration: "underline",
                    }}
                  >
                    Set Department Time
                  </Typography>
                  <form onSubmit={handleSubmit(onSubmitHandler)}>
                    <div className="row">
                      <TextField
                        id="standard-select-currency"
                        className="col m-2"
                        select
                        label="Category"
                        helperText="Please select Category"
                        variant="filled"
                        value={categoryValue}
                        onChange={(
                          e: React.ChangeEvent<{ value: unknown }>
                        ) => {
                          const value = e.target.value as string;
                          setCategoryValue(value);
                          handleCategoryChange(value);
                        }}
                      >
                        {Category?.map((option: any) => (
                          <MenuItem key={option} value={option}>
                            {option}
                          </MenuItem>
                        ))}
                      </TextField>
                      <TextField
                        id="standard-select-currency"
                        className="col m-2"
                        select
                        label="Department"
                        helperText="Please select Department"
                        variant="filled"
                        {...register("subCategory")}
                        value={selectedValue}
                        onChange={handleSelectChange}
                      >
                        {subCategories?.map((option: any) => (
                          <MenuItem key={option} value={option.id}>
                            {option.codeValue}
                          </MenuItem>
                        ))}
                      </TextField>
                    </div>
                    <div
                      className="d-flex"
                      style={{ justifyContent: "center" }}
                    >
                      <div className="col-6 m-2 mx-">
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <DemoContainer components={["TimePicker"]}>
                            <TimePicker
                              className="w-100"
                              label="Start Time"
                              inputRef={startTimeRef}
                              value={startTimeValue}
                              onChange={(newValue:any) =>
                                setStartTimeValue(newValue)
                              }
                              sx={{
                                backgroundColor: "#f5f5f5",
                                borderRadius: "4px",
                              }}
                            />
                          </DemoContainer>
                        </LocalizationProvider>
                      </div>
                      <div className="col-6 m-2 mx-1">
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <DemoContainer components={["TimePicker"]}>
                            <TimePicker
                              className="w-100"
                              label="End Time"
                              inputRef={endTimeRef}
                              value={endTimeValue}
                              onChange={(newValue:any) => setEndTimeValue(newValue)}
                              sx={{
                                backgroundColor: "#f5f5f5",
                                borderRadius: "4px",
                              }}
                            />
                          </DemoContainer>
                        </LocalizationProvider>
                      </div>
                    </div>
                    <div className="row ">
                      <div className="col-6">
                        <Button
                          style={{
                            borderRadius: "15px",
                            border: "1px solid #2c73ff",
                            float: "left",
                          }}
                          variant="contained"
                          className="mt-4 custom-button-gradient"
                          type="submit"
                        >
                          Save
                        </Button>
                      </div>
                      <div className="col-6">
                        <Button
                          style={{
                            borderRadius: "15px",
                            border: "1px solid #2c73ff",
                            float: "right",
                          }}
                          variant="contained"
                          className="mt-4 custom-button-gradient"
                          onClick={handleReset}
                        >
                          Reset
                        </Button>
                      </div>
                    </div>
                  </form>
                </Items>
              </Grid>
            </Grid>
            <div
              className="data-div "
              style={{ justifyContent: "space-between" }}
            >
              <div
                className="data-list"
                style={{
                  backgroundColor: "blue",
                  margin: "1%",
                  borderRadius: "5px",
                }}
              >
                <TableContainer component={Paper}>
                  <Table sx={{ minWidth: 700 }} aria-label="simple table">
                    <TableHead
                      sx={{
                        position: "sticky",
                        top: 0,
                        backgroundColor: "#00bfff",
                        zIndex: 99,
                      }}
                    >
                      <TableRow>
                        <StyledTableCell align="center">Action</StyledTableCell>
                        <StyledTableCell align="center">
                          category
                        </StyledTableCell>
                        <StyledTableCell align="center">
                          Department
                        </StyledTableCell>
                        <StyledTableCell align="center">
                          Total Employees
                        </StyledTableCell>
                        <StyledTableCell align="center">
                          Start Time
                        </StyledTableCell>
                        <StyledTableCell align="center">
                          End Time
                        </StyledTableCell>
                      </TableRow>
                    </TableHead>

                    <TableBody>
                      {tDepartmentTime.map((row: any, index: number) => (
                        <StyledTableRow key={index}>
                          <StyledTableCell align="center">
                            <ToolTip title="Edit">
                              <EditIcon
                                className="fs-4 text-warning mx-2"
                                onClick={() => {
                                  setDepartmentView({ edit: true });
                                  setDepartmentDetails(row);
                                }}
                              />
                            </ToolTip>
                          </StyledTableCell>
                          <StyledTableCell align="center">
                            {row?.categories[0]?.codeName}
                          </StyledTableCell>
                          <StyledTableCell align="center">
                            {row?.categories[0]?.codeValue}
                          </StyledTableCell>
                          <StyledTableCell
                            align="center"
                            style={{ cursor: "pointer", color: "black" }}
                          >
                            <Button
                              variant="outlined"
                              color="warning"
                              onClick={() =>
                                handleModalOpen(row?.employeeDepartments)
                              }
                            >
                              {row?.empDepartment}
                            </Button>
                          </StyledTableCell>
                          <StyledTableCell align="center">
                            {convertTo12HourFormat(
                              row?.startTime?.split("T")[1]?.substring(0, 5)
                            )}
                          </StyledTableCell>
                          <StyledTableCell align="center">
                            {convertTo12HourFormat(
                              row?.endTime?.split("T")[1]?.substring(0, 5)
                            )}
                          </StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </div>
            </div>
          </Item>
        </Box>
      </div>
      <EditDepartmentTime
        editDepartment={DepartmentView}
        setEditDepartment={setDepartmentView}
        setReload={setRefetch}
        data={DepartmentDetails}
      />
      <EmployeeModal
        employees={modalEmployees}
        onClose={handleModalClose}
        setReload={setload}
      />
      <BackDrop
        open={tDepartmentTime[0]?.totalEmployees ? loading || load : true}
      />
    </div>
  );
};
