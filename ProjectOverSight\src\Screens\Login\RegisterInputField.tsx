import Cancel from "@mui/icons-material/Cancel";
import CheckCircle from "@mui/icons-material/CheckCircle";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import {
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  OutlinedInput,
} from "@mui/material";
import { useEffect, useState } from "react";

export const RegisterInputField = ({
  setModelError,
  setUserDto,
  modelError,
  userDto,
}: any) => {
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    event.preventDefault();
  };

  return (
    <FormControl sx={{ m: 2, width: "40ch" }} variant="outlined">
      <InputLabel htmlFor="outlined-adornment-password">Password*</InputLabel>
      <OutlinedInput
        id="outlined-adornment-password"
        autoComplete="new-password"
        required
        error={modelError?.Password}
        type={showPassword ? "text" : "password"}
        onChange={(event: any) => {
          setModelError({ ...modelError, Password: false });
          setUserDto({ ...userDto, Password: event.target.value });
        }}
        endAdornment={
          <InputAdornment position="end">
            <IconButton
              aria-label="toggle password visibility"
              onClick={handleClickShowPassword}
              onMouseDown={handleMouseDownPassword}
              edge="end"
            >
              {!showPassword ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </InputAdornment>
        }
        label="Password"
      />
      <ValidatePassword user={userDto} />
    </FormControl>
  );
};

function ValidatePassword({ user }: any): JSX.Element {
  const requirements = [
    {
      description: "Password must be atleast 8 characters long.",
      isValid: false,
    },
    {
      description: "Must contain atleast one special character.",
      isValid: false,
      regex: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/,
    },
    {
      description: "Must contain  one upper and lower case letter.",
      isValid: false,
      regex: /^(?=.*[a-z])(?=.*[A-Z]).+$/,
    },
    {
      description: "Must contain atlease one number.",
      isValid: false,
      regex: /\d+/,
    },
  ];

  const [isValid, setValid] = useState(requirements);
  const [hide, setHide] = useState(true);

  useEffect(() => {
    requirements[0].isValid = user?.Password?.length >= 8;
    requirements[1].isValid = requirements[1].regex?.test(
      user?.Password
    ) as boolean;
    requirements[2].isValid = requirements[2].regex?.test(
      user?.Password
    ) as boolean;
    requirements[3].isValid = requirements[3].regex?.test(
      user?.Password
    ) as boolean;
    var hide = requirements.find((x) => x.isValid === false) ? false : true;
    setHide(hide);
    setValid(requirements);
  }, [user?.Password]);
  return (
    <>
      <ul className={`mt-1 ${hide || !user?.Password ? "display-none" : ""}`}>
        {isValid.map((e: any) => (
          <span className="d-flex align-items-center">
            {e.isValid ? (
              <CheckCircle className="mx-1 fs-5" color="success" />
            ) : (
              <Cancel className="mx-1 fs-5" color="error" />
            )}

            <li
              key={e.description}
              className={`${
                e.isValid ? "text-success" : "text-danger"
              } list-unstyled`}
            >
              {e.description}
            </li>
          </span>
        ))}
      </ul>
    </>
  );
}
